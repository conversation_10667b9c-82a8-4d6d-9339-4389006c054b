#import "ProfileViewController.h"
#import "ProfileModel.h"
#import "NetworkManager.h"
#import "SettingViewController.h"
#import "H5WebViewController.h"
#import "UILabel+Outline.h"
#import "UIImageView+Web.h"
#import "ProfileFunctionCell.h"
// 新增：HUD 统一加载指示器封装
#import "HUD.h"

@interface ProfileViewController ()
@property (nonatomic, strong) UILabel *userLabel;
// 新增：下拉刷新控件
@property (nonatomic, strong) UIRefreshControl *refreshControl;
@end

@implementation ProfileViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // 隐藏导航栏
    self.navigationController.navigationBarHidden = YES;
    // 添加透明背景图
    [self setupBackgroundImage];
    self.view.backgroundColor = [UIColor systemBackgroundColor];
    // 移除标题
    // self.title = @"我的";
    [self setupTableView];
    // 初始化下拉刷新控件
    [self setupRefreshControl];
    [self requestProfileData];
}

#pragma mark - 数据刷新
- (void)refreshData {
    NSLog(@"刷新个人中心页数据");
    // 调用现有的数据加载方法
    [self requestProfileData];
}

// 新增：构建tableHeaderView
- (UIView *)buildTableHeaderViewWithPhone:(NSString *)phone {
    CGFloat screenWidth = self.view.bounds.size.width;
    UIView *header = [[UIView alloc] initWithFrame:CGRectMake(0, 0, screenWidth, 280)];
    header.backgroundColor = [UIColor clearColor];

    // 头像
    UIImageView *avatar = [[UIImageView alloc] initWithFrame:CGRectMake((screenWidth-80)/2, 20, 80, 80)];
    avatar.image = [UIImage imageNamed:@"icon_profile_header"];
    avatar.contentMode = UIViewContentModeScaleAspectFill;
    avatar.layer.cornerRadius = 40;
    avatar.layer.masksToBounds = YES;
    [header addSubview:avatar];

    // 手机号
    UILabel *phoneLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, CGRectGetMaxY(avatar.frame)+12, screenWidth, 40)];
    phoneLabel.textAlignment = NSTextAlignmentCenter;
    phoneLabel.autoresizingMask = UIViewAutoresizingFlexibleWidth;
    UIColor *purple = [UIColor colorWithRed:0.56 green:0.27 blue:0.98 alpha:1.0];
    UIFont *customFont = [UIFont fontWithName:@"Verdana-BoldItalic" size:30] ?: [UIFont boldSystemFontOfSize:23];
    [phoneLabel setOutlinedText:(phone ?: @"")
                          font:customFont
                     textColor:[UIColor whiteColor]
                  outlineColor:purple
                  outlineWidth:2.0];
    [header addSubview:phoneLabel];

    // 广告位
    CGFloat adY = CGRectGetMaxY(phoneLabel.frame) + 12;
    UIView *adView = [[UIView alloc] initWithFrame:CGRectMake(16, adY, screenWidth-32, 130)];
    adView.backgroundColor = [UIColor clearColor]; // 浅黄色
    adView.layer.cornerRadius = 16;
    adView.layer.masksToBounds = YES;
    [header addSubview:adView];

    // 新增：广告图片
    UIImageView *adImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, adView.bounds.size.width, adView.bounds.size.height)];
    adImageView.image = [UIImage imageNamed:@"profile_ad_banner"];
    adImageView.contentMode = UIViewContentModeScaleAspectFill;
    adImageView.clipsToBounds = YES;
    [adView addSubview:adImageView];

    // 新增：广告View下方15pt添加标题图片
    CGFloat titleImgY = CGRectGetMaxY(adView.frame) + 15;
    UIImageView *titleImg = [[UIImageView alloc] initWithFrame:CGRectMake(0, titleImgY, 188, 33)];
    titleImg.image = [UIImage imageNamed:@"profile_ad_title"];
    titleImg.contentMode = UIViewContentModeScaleAspectFit;
    [header addSubview:titleImg];

    // 新增：底部63pt间距
    CGFloat totalHeight = CGRectGetMaxY(titleImg.frame) + 23;
    header.frame = CGRectMake(0, 0, screenWidth, totalHeight);
    return header;
}

- (void)setupTableView {
    CGFloat topOffset = 0; // 头部内容已放入headerView
    self.tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, topOffset, self.view.bounds.size.width, self.view.bounds.size.height - topOffset) style:UITableViewStylePlain];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone; // 去除分割线
    [self.tableView registerClass:[ProfileFunctionCell class] forCellReuseIdentifier:@"ProfileFunctionCell"];
    self.tableView.contentInset = UIEdgeInsetsMake(0, 0, 90, 0); // 底部留出空间，避免被tabbar遮挡
    [self.view addSubview:self.tableView];
}

// 新增：下拉刷新控件配置
- (void)setupRefreshControl {
    if (self.refreshControl) { return; }
    self.refreshControl = [[UIRefreshControl alloc] init];
    self.refreshControl.tintColor = [UIColor systemGrayColor];
    [self.refreshControl addTarget:self action:@selector(refreshData) forControlEvents:UIControlEventValueChanged];
    if (@available(iOS 10.0, *)) {
        self.tableView.refreshControl = self.refreshControl;
    } else {
        [self.tableView addSubview:self.refreshControl];
    }
}

- (void)requestProfileData {
    UIView *hudSuperView = self.view.window ?: self.view;
    // 非下拉刷新时显示 HUD
    if (!self.refreshControl.isRefreshing) {
        [HUD showLoadingInView:hudSuperView withMessage:@"Loading…"];
    }

    __weak typeof(self) weakSelf = self;
    [NetworkManager requestWithAPI:@"Alicia/sauce" params:@{} method:@"GET" completion:^(NSDictionary *response, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            // 隐藏 HUD / 结束刷新
            [HUD hideForView:hudSuperView];
            if (weakSelf.refreshControl.isRefreshing) {
                [weakSelf.refreshControl endRefreshing];
            }

            if (!error && [response isKindOfClass:[NSDictionary class]]) {
                NSDictionary *awkward = response[@"awkward"];
                if ([awkward isKindOfClass:[NSDictionary class]]) {
                    ProfileModel *model = [[ProfileModel alloc] initWithDictionary:awkward];
                    weakSelf.profileModel = model;
                    weakSelf.listItems = model.boasting;
                    // 只展示手机号，无前缀
                    NSString *phone = model.userInfo.userphone ?: @"";
                    weakSelf.tableView.tableHeaderView = [weakSelf buildTableHeaderViewWithPhone:phone];
                    [weakSelf.tableView reloadData];
                }
            } else {
                NSLog(@"Profile接口请求失败: %@", error);
                if (error) {
                    // 新增：错误提示 Toast
                    [HUD showError:error inView:hudSuperView];
                }
            }
        });
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.listItems.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    ProfileFunctionCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ProfileFunctionCell" forIndexPath:indexPath];
    ProfileBoastingItem *item = self.listItems[indexPath.row];
    [cell setIconURL:item.mayonnaise placeholder:nil];
    cell.titleLabel.text = item.coldchicken;
    return cell;
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    ProfileBoastingItem *item = self.listItems[indexPath.row];
    NSString *route = item.toteach ?: @"";
    if ([route hasPrefix:@"to://"]) {
        NSLog(@"本地跳转: %@", route);
        // 解析本地路由
        NSString *lower = [route lowercaseString];
        if ([lower containsString:@"gwendoline"]) {
            // 跳转到设置页面
            SettingViewController *settingVC = [[SettingViewController alloc] init];
            [self.navigationController pushViewController:settingVC animated:YES];
        } else {
            // TODO: 处理其他本地路由
            NSLog(@"暂未实现的本地路由: %@", route);
        }
    } else if ([route hasPrefix:@"http"]) {
        NSLog(@"H5跳转: %@", route);
        if (route.length > 0) {
            H5WebViewController *webVC = [[H5WebViewController alloc] initWithURLString:route];
            [self.navigationController pushViewController:webVC animated:YES];
        }
    } else {
        NSLog(@"未知跳转类型: %@", route);
    }
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

// 新增方法：设置背景图
- (void)setupBackgroundImage {
    UIImageView *bgView = [[UIImageView alloc] initWithFrame:self.view.bounds];
    bgView.image = [UIImage imageNamed:@"general_background"];
    bgView.contentMode = UIViewContentModeScaleAspectFill;
    bgView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    bgView.userInteractionEnabled = NO;
    bgView.alpha = 1.0; // 可根据需要调整透明度
    [self.view insertSubview:bgView atIndex:0];
}

// 设置cell高度为50
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 60;
}

// 设置section间距为6pt
- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return 6;
}
- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    UIView *footer = [[UIView alloc] init];
    footer.backgroundColor = [UIColor clearColor];
    return footer;
}

@end 
