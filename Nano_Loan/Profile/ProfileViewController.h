#import <UIKit/UIKit.h>

@class ProfileModel;

NS_ASSUME_NONNULL_BEGIN

@interface ProfileViewController : UIViewController <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray *listItems;
@property (nonatomic, strong) ProfileModel *profileModel;

/**
 刷新个人中心页数据
 点击底部标签栏时调用，重新加载个人中心数据
 */
- (void)refreshData;

@end

NS_ASSUME_NONNULL_END

//个人中心&订单列表
//，支持下拉刷新功能，刷新当前页数据。
//登录手机号展示，手机号中间号码用*号代替
//几个订单状态按钮，点击跳转订单页对应的Tab
//服务端下发配置项，点击Item跳转原生或者H5
