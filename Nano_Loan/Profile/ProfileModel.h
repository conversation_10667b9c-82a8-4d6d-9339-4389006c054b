#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface ProfileBoastingItem : NSObject
@property (nonatomic, copy) NSString *coldchicken; // 名称
@property (nonatomic, copy) NSString *toteach;     // 跳转链接
@property (nonatomic, copy) NSString *mayonnaise;  // 图标URL
- (instancetype)initWithDictionary:(NSDictionary *)dict;
@end

@interface ProfileUserInfo : NSObject
@property (nonatomic, assign) NSInteger isReal;
@property (nonatomic, copy) NSString *tarts;
@property (nonatomic, copy) NSString *userphone;
- (instancetype)initWithDictionary:(NSDictionary *)dict;
@end

@interface ProfileModel : NSObject
@property (nonatomic, strong) NSArray<ProfileBoastingItem *> *boasting;
@property (nonatomic, strong) ProfileUserInfo *userInfo;
- (instancetype)initWithDictionary:(NSDictionary *)dict;
@end

NS_ASSUME_NONNULL_END 