#import "ProfileFunctionCell.h"
#import "UIImageView+Web.h"

@implementation ProfileFunctionCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        self.selectionStyle = UITableViewCellSelectionStyleNone;

        // 背景卡片图片
        UIImageView *bgImg = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"setting_cell_bg"]];
        bgImg.tag = 1000;
        [self.contentView addSubview:bgImg];
        
        // icon
        self.iconView = [[UIImageView alloc] init];
        self.iconView.backgroundColor = [UIColor clearColor];
        [bgImg addSubview:self.iconView];

        // 标题
        self.titleLabel = [[UILabel alloc] init];
        self.titleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        self.titleLabel.textColor = [UIColor blackColor];
        [bgImg addSubview:self.titleLabel];

        // 箭头
        self.arrowView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"setting_cell_arrow"]];
        [bgImg addSubview:self.arrowView];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    CGFloat margin = 16;
    CGFloat bgH = 54;
    UIImageView *bgImg = [self.contentView viewWithTag:1000];
    bgImg.frame = CGRectMake(margin, 3, self.contentView.bounds.size.width - margin*2, bgH);

    self.iconView.frame = CGRectMake(16, 13, 28, 28);
    self.titleLabel.frame = CGRectMake(16+28+12, 0, bgImg.bounds.size.width-28-12-40, bgH);
    self.arrowView.frame = CGRectMake(bgImg.bounds.size.width-28, 17, 20, 20);
}

- (void)setIconURL:(NSString *)url placeholder:(UIImage *)placeholder {
    [self.iconView setImageWithURLString:url placeholderColor:[UIColor colorWithWhite:0.95 alpha:1]];
}

@end 
