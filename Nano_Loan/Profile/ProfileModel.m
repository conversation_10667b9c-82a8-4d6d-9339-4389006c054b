#import "ProfileModel.h"

@implementation ProfileBoastingItem
- (instancetype)initWithDictionary:(NSDictionary *)dict {
    if (self = [super init]) {
        _coldchicken = [dict[@"coldchicken"] isKindOfClass:[NSString class]] ? dict[@"coldchicken"] : @"";
        _toteach = [dict[@"toteach"] isKindOfClass:[NSString class]] ? dict[@"toteach"] : @"";
        _mayonnaise = [dict[@"mayonnaise"] isKindOfClass:[NSString class]] ? dict[@"mayonnaise"] : @"";
    }
    return self;
}
@end

@implementation ProfileUserInfo
- (instancetype)initWithDictionary:(NSDictionary *)dict {
    if (self = [super init]) {
        _isReal = [dict[@"isReal"] respondsToSelector:@selector(integerValue)] ? [dict[@"isReal"] integerValue] : 0;
        _tarts = [dict[@"tarts"] isKindOfClass:[NSString class]] ? dict[@"tarts"] : @"";
        _userphone = [dict[@"userphone"] isKindOfClass:[NSString class]] ? dict[@"userphone"] : @"";
    }
    return self;
}
@end

@implementation ProfileModel
- (instancetype)initWithDictionary:(NSDictionary *)dict {
    if (self = [super init]) {
        NSArray *boastingArr = dict[@"boasting"];
        NSMutableArray *boastingItems = [NSMutableArray array];
        if ([boastingArr isKindOfClass:[NSArray class]]) {
            for (NSDictionary *itemDict in boastingArr) {
                if ([itemDict isKindOfClass:[NSDictionary class]]) {
                    ProfileBoastingItem *item = [[ProfileBoastingItem alloc] initWithDictionary:itemDict];
                    [boastingItems addObject:item];
                }
            }
        }
        _boasting = boastingItems;
        NSDictionary *userInfoDict = dict[@"userInfo"];
        if ([userInfoDict isKindOfClass:[NSDictionary class]]) {
            _userInfo = [[ProfileUserInfo alloc] initWithDictionary:userInfoDict];
        }
    }
    return self;
}
@end 