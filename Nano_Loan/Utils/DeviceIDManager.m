#import "DeviceIDManager.h"
#import <Security/Security.h>
#import <UIKit/UIKit.h>

@implementation DeviceIDManager

static NSString *const kIDFVKey = @"com.nano.device.idfv";

+ (NSString *)persistentIDFV {
    NSString *bundleId = [[NSBundle mainBundle] bundleIdentifier] ?: @"com.nano.app";

    // 从 Keychain 读取
    NSDictionary *query = @{(__bridge id)kSecClass: (__bridge id)kSecClassGenericPassword,
                             (__bridge id)kSecAttrService: bundleId,
                             (__bridge id)kSecAttrAccount: kIDFVKey,
                             (__bridge id)kSecReturnData: @YES};
    CFTypeRef result = NULL;
    OSStatus status = SecItemCopyMatching((__bridge CFDictionaryRef)query, &result);
    if (status == errSecSuccess && result) {
        NSData *data = (__bridge_transfer NSData *)result;
        NSString *saved = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        if (saved.length > 0) { 
            NSLog(@"[DeviceIDManager] 从Keychain读取IDFV: %@", saved);
            return [saved uppercaseString]; 
        }
    }

    // 生成新的 IDFV-like UUID（去掉横线以匹配 IDFA 样式）
    NSString *newID = [[[UIDevice currentDevice] identifierForVendor] UUIDString].uppercaseString;
    NSLog(@"[DeviceIDManager] 生成新的IDFV: %@", newID);

    // 保存到 Keychain
    NSData *data = [[newID uppercaseString] dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *addQuery = @{(__bridge id)kSecClass: (__bridge id)kSecClassGenericPassword,
                               (__bridge id)kSecAttrService: bundleId,
                               (__bridge id)kSecAttrAccount: kIDFVKey,
                               (__bridge id)kSecValueData: data};
    OSStatus addStatus = SecItemAdd((__bridge CFDictionaryRef)addQuery, NULL);
    if (addStatus == errSecSuccess) {
        NSLog(@"[DeviceIDManager] IDFV保存到Keychain成功");
    } else {
        NSLog(@"[DeviceIDManager] IDFV保存到Keychain失败: %d", (int)addStatus);
    }
    
    return newID;
}

+ (void)clearCachedIDFV {
    NSString *bundleId = [[NSBundle mainBundle] bundleIdentifier] ?: @"com.nano.app";
    
    NSDictionary *deleteQuery = @{(__bridge id)kSecClass: (__bridge id)kSecClassGenericPassword,
                                  (__bridge id)kSecAttrService: bundleId,
                                  (__bridge id)kSecAttrAccount: kIDFVKey};
    OSStatus deleteStatus = SecItemDelete((__bridge CFDictionaryRef)deleteQuery);
    if (deleteStatus == errSecSuccess) {
        NSLog(@"[DeviceIDManager] 清除缓存的IDFV成功");
    } else {
        NSLog(@"[DeviceIDManager] 清除缓存的IDFV失败: %d", (int)deleteStatus);
    }
}

@end
