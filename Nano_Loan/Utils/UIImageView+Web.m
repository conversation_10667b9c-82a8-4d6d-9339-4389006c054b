//  UIImageView+Web.m
//  Nano_Loan

#import "UIImageView+Web.h"

@implementation UIImageView (Web)

+ (NSCache<NSString *, UIImage *> *)_imageCache {
    static NSCache *cache;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        cache = [[NSCache alloc] init];
    });
    return cache;
}

- (void)setImageWithURLString:(NSString *)urlString placeholderColor:(UIColor *)color {
    self.backgroundColor = color;
    self.image = nil;
    if (urlString.length == 0) return;
    
    // 检查缓存
    UIImage *cached = [[[self class] _imageCache] objectForKey:urlString];
    if (cached) {
        self.image = cached;
        self.backgroundColor = UIColor.clearColor;
        return;
    }
    
    // 处理URL格式
    NSString *formattedURLString = urlString;
    if (![urlString hasPrefix:@"http://"] && ![urlString hasPrefix:@"https://"]) {
        formattedURLString = [@"http://" stringByAppendingString:urlString];
    }
    
    NSURL *url = [NSURL URLWithString:formattedURLString];
    if (!url) {
        NSLog(@"无效的URL: %@", urlString);
        return;
    }
    
    NSLog(@"开始加载图片: %@", url.absoluteString);
    
    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithURL:url completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        if (error) {
            NSLog(@"图片加载失败: %@, 错误: %@", url.absoluteString, error.localizedDescription);
            return;
        }
        
        UIImage *img = [UIImage imageWithData:data];
        if (!img) {
            NSLog(@"无法从数据创建图片: %@", url.absoluteString);
            return;
        }
        
        NSLog(@"图片加载成功: %@", url.absoluteString);
        [[[self class] _imageCache] setObject:img forKey:urlString]; // 使用原始URL作为缓存键
        
        dispatch_async(dispatch_get_main_queue(), ^{
            self.image = img;
            self.backgroundColor = UIColor.clearColor;
        });
    }];
    [task resume];
}

- (void)setImageWithURLString:(NSString *)urlString placeholderImage:(UIImage *)placeholderImage {
    // 保持占位图片不变，直到新图片加载完成
    if (placeholderImage) {
        self.image = placeholderImage;
    }
    self.backgroundColor = UIColor.clearColor;
    
    if (urlString.length == 0) return;
    
    // 检查缓存
    UIImage *cached = [[[self class] _imageCache] objectForKey:urlString];
    if (cached) {
        self.image = cached;
        return;
    }
    
    // 处理URL格式
    NSString *formattedURLString = urlString;
    if (![urlString hasPrefix:@"http://"] && ![urlString hasPrefix:@"https://"]) {
        formattedURLString = [@"http://" stringByAppendingString:urlString];
    }
    
    NSURL *url = [NSURL URLWithString:formattedURLString];
    if (!url) {
        NSLog(@"无效的URL: %@", urlString);
        return;
    }
    
    NSLog(@"开始加载图片: %@", url.absoluteString);
    
    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithURL:url completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        if (error) {
            NSLog(@"图片加载失败: %@, 错误: %@", url.absoluteString, error.localizedDescription);
            return;
        }
        
        UIImage *img = [UIImage imageWithData:data];
        if (!img) {
            NSLog(@"无法从数据创建图片: %@", url.absoluteString);
            return;
        }
        
        NSLog(@"图片加载成功: %@", url.absoluteString);
        [[[self class] _imageCache] setObject:img forKey:urlString]; // 使用原始URL作为缓存键
        
        dispatch_async(dispatch_get_main_queue(), ^{
            // 使用淡入动画效果，提升用户体验
            [UIView transitionWithView:self
                              duration:0.3
                               options:UIViewAnimationOptionTransitionCrossDissolve
                            animations:^{
                                self.image = img;
                            }
                            completion:nil];
        });
    }];
    [task resume];
}

@end 