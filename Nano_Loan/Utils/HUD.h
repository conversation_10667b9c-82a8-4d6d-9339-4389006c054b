//
//  HUD.h
//  Nano_Loan
//
//  Created for global MBProgressHUD wrapper to ensure multiline messages and unified style.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface HUD : NSObject

/// 显示加载指示器，需手动调用 hideForView: 关闭
+ (void)showLoadingInView:(UIView *)view withMessage:(nullable NSString *)message;

/// 隐藏指定 view 上的 HUD
+ (void)hideForView:(UIView *)view;

/// 显示文本提示，自动 1.5s 消失，支持多行
+ (void)showToast:(NSString *)message inView:(UIView *)view;

/// 显示错误信息，自动 1.5s 消失
+ (void)showError:(NSError *)error inView:(UIView *)view;

/// 全局 Loading（显示在 keyWindow），引用计数方式保证并发场景
+ (void)showLoading;

/// 全局 Loading 带文案
+ (void)showLoadingWithMessage:(nullable NSString *)message;

/// 关闭全局 Loading
+ (void)hideLoading;

@end

NS_ASSUME_NONNULL_END 