#import "UIColor+Hex.h"

@implementation UIColor (Hex)

+ (UIColor *)colorWithHexString:(NSString *)hexString {
    NSString *cleanString = [[hexString stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]] uppercaseString];
    if ([cleanString hasPrefix:@"#"]) {
        cleanString = [cleanString substringFromIndex:1];
    } else if ([cleanString hasPrefix:@"0X"]) {
        cleanString = [cleanString substringFromIndex:2];
    }
    NSUInteger length = [cleanString length];
    unsigned int a = 255, r = 0, g = 0, b = 0;
    if (length == 6) {
        // RRGGBB
        [[NSScanner scannerWithString:[cleanString substringWithRange:NSMakeRange(0, 2)]] scanHexInt:&r];
        [[NSScanner scannerWithString:[cleanString substringWithRange:NSMakeRange(2, 2)]] scanHexInt:&g];
        [[NSScanner scannerWithString:[cleanString substringWithRange:NSMakeRange(4, 2)]] scanHexInt:&b];
    } else if (length == 8) {
        // AARRGGBB
        [[NSScanner scannerWithString:[cleanString substringWithRange:NSMakeRange(0, 2)]] scanHexInt:&a];
        [[NSScanner scannerWithString:[cleanString substringWithRange:NSMakeRange(2, 2)]] scanHexInt:&r];
        [[NSScanner scannerWithString:[cleanString substringWithRange:NSMakeRange(4, 2)]] scanHexInt:&g];
        [[NSScanner scannerWithString:[cleanString substringWithRange:NSMakeRange(6, 2)]] scanHexInt:&b];
    } else {
        // 格式不正确，返回透明色
        return [UIColor clearColor];
    }
    return [UIColor colorWithRed:r/255.0 green:g/255.0 blue:b/255.0 alpha:a/255.0];
}

@end 