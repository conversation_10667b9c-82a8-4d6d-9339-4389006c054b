#import <UIKit/UIKit.h>

@interface UILabel (Outline)

/// 设置带描边（外边框）的文字样式
/// @param text 文字内容
/// @param font 字体
/// @param textColor 文字颜色
/// @param outlineColor 外边框颜色
/// @param outlineWidth 外边框宽度
- (void)setOutlinedText:(NSString *)text
                  font:(UIFont *)font
             textColor:(UIColor *)textColor
          outlineColor:(UIColor *)outlineColor
          outlineWidth:(CGFloat)outlineWidth;

@end 