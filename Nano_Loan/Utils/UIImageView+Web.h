//  UIImageView+Web.h
//  Nano_Loan
//  轻量级网络图片加载 (内存缓存)

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIImageView (Web)

/**
 使用URL字符串加载图片，使用指定的颜色作为占位背景
 @param urlString 图片URL字符串
 @param color 在图片加载前显示的背景颜色
 */
- (void)setImageWithURLString:(NSString *)urlString placeholderColor:(UIColor *)color;

/**
 使用URL字符串加载图片，使用指定的图片作为占位图
 @param urlString 图片URL字符串
 @param placeholderImage 在图片加载前显示的占位图片
 */
- (void)setImageWithURLString:(NSString *)urlString placeholderImage:(UIImage *)placeholderImage;

@end

NS_ASSUME_NONNULL_END 