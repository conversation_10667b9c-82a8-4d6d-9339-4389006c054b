#import "UILabel+Outline.h"

@implementation UILabel (Outline)

- (void)setOutlinedText:(NSString *)text
                  font:(UIFont *)font
             textColor:(UIColor *)textColor
          outlineColor:(UIColor *)outlineColor
          outlineWidth:(CGFloat)outlineWidth {
    if (!text) text = @"";
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:text];
    NSRange range = NSMakeRange(0, text.length);
    [attrStr addAttribute:NSFontAttributeName value:font range:range];
    [attrStr addAttribute:NSForegroundColorAttributeName value:textColor range:range];
    [attrStr addAttribute:NSStrokeColorAttributeName value:outlineColor range:range];
    [attrStr addAttribute:NSStrokeWidthAttributeName value:@(-outlineWidth) range:range];
    self.attributedText = attrStr;
}

@end 