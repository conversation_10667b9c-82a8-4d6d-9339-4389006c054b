//
//  HUD.m
//  Nano_Loan
//
//  Global MBProgressHUD wrapper
//

#import "HUD.h"
#import <MBProgressHUD/MBProgressHUD.h>
#import <UIKit/UIKit.h>

/// 内部静态变量记录全局 Loading HUD 与并发计数
static MBProgressHUD *_Nullable s_globalHUD = nil;
static NSUInteger s_loadingCounter = 0;

#pragma mark - Private helpers

/// 返回显示 HUD 的目标视图 (keyWindow)
static inline UIView *HUDTargetView(void) {
    UIWindow *keyWindow = UIApplication.sharedApplication.keyWindow;
    if (!keyWindow) {
        // iOS 13+ 多 Scene 场景
        for (UIWindow *window in UIApplication.sharedApplication.windows) {
            if (window.isKeyWindow) {
                keyWindow = window;
                break;
            }
        }
    }
    return keyWindow ?: UIApplication.sharedApplication.windows.firstObject;
}

#pragma mark - Global Loading APIs

@implementation HUD (GlobalLoading)

+ (void)commonStyleForHUD:(MBProgressHUD *)hud {
    hud.removeFromSuperViewOnHide = YES;
    hud.margin = 16.0;
    hud.label.numberOfLines = 0; // 允许多行
    hud.userInteractionEnabled = NO; // toast 不拦截触摸，loading 时仍可根据需求调整
}

+ (void)showLoadingInView:(UIView *)view withMessage:(nullable NSString *)message {
    if (!view) return;
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:view animated:YES];
    hud.mode = MBProgressHUDModeIndeterminate;
    if (message.length > 0) {
        hud.label.text = message;
    } else {
        hud.label.text = @"Loading…";
    }
    [self commonStyleForHUD:hud];
}

+ (void)hideForView:(UIView *)view {
    if (!view) return;
    [MBProgressHUD hideHUDForView:view animated:YES];
}

+ (void)showToast:(NSString *)message inView:(UIView *)view {
    if (!view || message.length == 0) return;
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:view animated:YES];
    hud.mode = MBProgressHUDModeText;
    hud.label.text = message;
    [self commonStyleForHUD:hud];
    [hud hideAnimated:YES afterDelay:1.5];
}

+ (void)showError:(NSError *)error inView:(UIView *)view {
    NSString *msg = error.localizedDescription ?: @"Unknown Error";
    [self showToast:msg inView:view];
}

@end

@implementation HUD

+ (void)showLoading {
    [self showLoadingWithMessage:nil];
}

+ (void)showLoadingWithMessage:(nullable NSString *)message {
    dispatch_async(dispatch_get_main_queue(), ^{
        s_loadingCounter += 1;
        if (s_loadingCounter > 1) {
            // 已经存在全局 HUD，仅更新文案（若有）
            if (message.length > 0) {
                s_globalHUD.label.text = message;
            }
            return;
        }
        UIView *view = HUDTargetView();
        if (!view) { return; }
        MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:view animated:YES];
        hud.mode = MBProgressHUDModeIndeterminate;
        hud.userInteractionEnabled = YES; // Loading 阻断触摸，防止误操作
        hud.label.text = (message.length > 0) ? message : @"Loading…";
        [self commonStyleForHUD:hud];
        s_globalHUD = hud;
    });
}

+ (void)hideLoading {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (s_loadingCounter == 0) { return; }
        s_loadingCounter -= 1;
        if (s_loadingCounter == 0 && s_globalHUD) {
            [s_globalHUD hideAnimated:YES];
            s_globalHUD = nil;
        }
    });
}

@end 