#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface HomeProductCell : UITableViewCell

- (void)configureWithProduct:(NSDictionary *)product;

// 配置产品并传入点击回调
- (void)configureWithProduct:(NSDictionary *)product
                   atIndex:(NSInteger)index
          applyButtonBlock:(void (^)(NSInteger idx))applyBlock;

@property (nonatomic, strong, readonly) UIButton *applyButton;

@end

NS_ASSUME_NONNULL_END 