#import "HomeProductCell.h"
#import <Masonry/Masonry.h>

@interface HomeProductCell ()

@property (nonatomic, strong) UIImageView *bgImageView;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *amountLabel;
@property (nonatomic, strong) UIImageView *nameLogoImgView;
@property (nonatomic, strong) UILabel *adLabel;
@property (nonatomic, strong) UIButton *applyButton;
@property (nonatomic, copy) void (^applyBlock)(NSInteger idx);
@property (nonatomic, assign) NSInteger cellIndex;
@property (nonatomic, copy) NSString *currentLogoURLString; // 记录当前正在显示的 logo URL，避免复用错位

@end

@implementation HomeProductCell

static CGFloat const kSideInset = 14.0f;

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = [UIColor clearColor];

        // 背景图
        _bgImageView = [[UIImageView alloc] init];
        _bgImageView.image = [UIImage imageNamed:@"home_small_card_bg"];// TODO: 替换实际资源名
        _bgImageView.contentMode = UIViewContentModeScaleAspectFill;
        [self.contentView addSubview:_bgImageView];

        // 产品名称 logo
        _nameLogoImgView = [[UIImageView alloc] init];
        // 默认占位图
        _nameLogoImgView.image = [UIImage imageNamed:@"home_cell_product_logo_x"];
        _nameLogoImgView.contentMode = UIViewContentModeScaleAspectFill;
        [_bgImageView addSubview:_nameLogoImgView];

        // 产品名称
        _nameLabel = [[UILabel alloc] init];
        _nameLabel.font = [UIFont boldSystemFontOfSize:16.0f];
        _nameLabel.textColor = [UIColor whiteColor];
        [_bgImageView addSubview:_nameLabel];

        //广告-固定词
        _adLabel = [[UILabel alloc] init];
        _adLabel.font = [UIFont systemFontOfSize:13.0f];
        _adLabel.text = @"Maximum limited";
        _adLabel.textColor = [UIColor blackColor];
        [_bgImageView addSubview:_adLabel];

        // 金额
        _amountLabel = [[UILabel alloc] init];
        _amountLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:24.0f];
        _amountLabel.textColor = [UIColor blackColor];
        [_bgImageView addSubview:_amountLabel];

        //申请按钮
        _applyButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_applyButton setTitle:@"Apply" forState:UIControlStateNormal];
        //按钮字体 Verdana-BoldItalic
        _applyButton.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:14.0f];
        [_applyButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_applyButton setBackgroundImage:[UIImage imageNamed:@"home_cell_apply_button_bg"] forState:UIControlStateNormal];
        [_bgImageView addSubview:_applyButton];

        [_applyButton addTarget:self action:@selector(applyTapped) forControlEvents:UIControlEventTouchUpInside];

        /***** Masonry 布局 *****/
        [_bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentView).offset(kSideInset);
            make.right.equalTo(self.contentView).offset(-kSideInset);
            make.top.equalTo(self.contentView);
            make.bottom.equalTo(self.contentView).offset(-2.0f);
        }];

        [_nameLogoImgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_bgImageView).offset(22.0f);
            make.top.equalTo(_bgImageView).offset(11.0f);
            make.width.height.mas_equalTo(18.0f);
        }];

        [_nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_nameLogoImgView.mas_right).offset(8.0f);
            make.centerY.equalTo(_nameLogoImgView);
            make.height.mas_equalTo(13);
        }];

        [_adLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_nameLogoImgView);
            make.top.equalTo(_bgImageView.mas_top).offset(45.0f);
            make.height.mas_equalTo(13);
        }];

        [_amountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_nameLogoImgView);
            make.top.equalTo(_adLabel.mas_bottom).offset(9.0f);
            make.height.mas_equalTo(25);
        }];

        [_applyButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(79.0f);
            make.right.equalTo(_bgImageView).offset(-22.0f);
            make.height.mas_equalTo(36.0f);
            make.bottom.equalTo(_bgImageView).offset(-25.0f);
        }];
    }
    return self;
}

- (void)configureWithProduct:(NSDictionary *)product {
    [self configureWithProduct:product atIndex:0 applyButtonBlock:nil];
}

- (void)configureWithProduct:(NSDictionary *)product atIndex:(NSInteger)index applyButtonBlock:(void (^)(NSInteger idx))applyBlock {
    self.cellIndex = index;
    self.applyBlock = applyBlock;

    self.nameLabel.text = product[@"cando"] ?: @"--";

    NSString *amountStr = product[@"disappointment"] ?: @"--";
    self.amountLabel.text = amountStr;

    NSString *adText = product[@"stitches"] ?: @"Maximum limited";
    self.adLabel.text = adText;

    NSString *applyText = product[@"embroidery"] ?: @"Apply";
    [self.applyButton setTitle:applyText forState:UIControlStateNormal];

    // -------- 网络加载 Logo --------
    NSString *logoURLStr = product[@"tapestrywork"];
    self.currentLogoURLString = logoURLStr;
    if ([logoURLStr isKindOfClass:[NSString class]] && logoURLStr.length > 0) {
        NSURL *url = [NSURL URLWithString:logoURLStr];
        if (url) {
            __weak typeof(self) weakSelf = self;
            NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithURL:url completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
                if (!error && data.length > 0) {
                    UIImage *img = [UIImage imageWithData:data];
                    if (img) {
                        dispatch_async(dispatch_get_main_queue(), ^{
                            // 防止复用导致错位
                            if ([weakSelf.currentLogoURLString isEqualToString:logoURLStr]) {
                                weakSelf.nameLogoImgView.image = img;
                            }
                        });
                    }
                }
            }];
            [task resume];
        }
    }
}

#pragma mark - Action

- (void)applyTapped {
    if (self.applyBlock) {
        self.applyBlock(self.cellIndex);
    }
}

#pragma mark - Getter

- (UIButton *)applyButton {
    return _applyButton;
}

@end 