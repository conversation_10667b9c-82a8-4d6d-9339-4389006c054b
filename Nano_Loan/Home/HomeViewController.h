#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface HomeViewController : UIViewController

/**
 刷新首页数据
 点击底部标签栏时调用，重新加载首页数据
 */
- (void)refreshData;

@end

NS_ASSUME_NONNULL_END

//各个tab选中时刷新当前页面数据
//支持下拉刷新功能，刷新当前页数据。
//首页入口页面（调用APP首页接口）
//banner自动轮播，可点击，根据“URL”跳转原生或者H5页面。
//大卡页面
//点击调用准入接口
//小卡页面
//顶部小卡位点击调用准入接口
//产品列表，
//产品item点击调用准入接口，整个item响应点击事件。
//准入接口返回URL不为空，跳转H5或者原生页面
//首页点击产品必须调用准入接口！根据接口返回的地址跳转H5或者原生页面
