///
///  AddressDataManager.m
///  Nano_Loan
///
///  Created by iOSDev on 2025/6/25.
///

#import "AddressDataManager.h"
#import "AddressNode.h"
#import "NetworkManager.h"
#import <MBProgressHUD/MBProgressHUD.h>

static NSString * const kAddressCacheKey = @"AddressDataCache";

@interface AddressDataManager ()
@property (nonatomic, strong) NSArray<AddressNode *> *addressRoots;
@end

@implementation AddressDataManager

+ (instancetype)sharedManager {
    static AddressDataManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[AddressDataManager alloc] init];
    });
    return instance;
}

#pragma mark - Public

- (void)fetchAddressDataIfNeededWithCompletion:(AddressDataCompletion)completion {
    // 如果内存已有数据或磁盘有缓存，则直接返回
    if (self.addressRoots) {
        if (completion) completion(self.addressRoots, nil);
        return;
    }
    NSArray *cached = [self cachedAddressRoots];
    if (cached) {
        self.addressRoots = cached;
        if (completion) completion(cached, nil);
        return;
    }
    // 无缓存，发起请求
    [self requestAddressDataWithForce:NO completion:completion];
}

- (void)forceRefreshAddressDataWithCompletion:(AddressDataCompletion)completion {
    [self requestAddressDataWithForce:YES completion:completion];
}

- (NSArray<AddressNode *> *)cachedAddressRoots {
    NSData *data = [[NSUserDefaults standardUserDefaults] dataForKey:kAddressCacheKey];
    if (!data) return nil;
    NSSet *allowed = [NSSet setWithObjects:[NSArray class], [AddressNode class], nil];
    NSArray<AddressNode *> *roots = [NSKeyedUnarchiver unarchivedObjectOfClasses:allowed fromData:data error:nil];
    return roots;
}

#pragma mark - Private

- (void)requestAddressDataWithForce:(BOOL)force completion:(AddressDataCompletion)completion {
    __weak typeof(self) weakSelf = self;
    [NetworkManager requestWithAPI:@"Alicia/salad" params:@{} method:@"GET" completion:^(NSDictionary *response, NSError *error) {
        if (!error && [response isKindOfClass:[NSDictionary class]]) {
            NSDictionary *awkward = response[@"awkward"];
            NSArray *boasting = [awkward isKindOfClass:[NSDictionary class]] ? awkward[@"boasting"] : nil;
            NSArray<AddressNode *> *roots = nil;
            if ([boasting isKindOfClass:[NSArray class]]) {
                NSMutableArray *tmp = [NSMutableArray arrayWithCapacity:boasting.count];
                for (NSDictionary *dict in boasting) {
                    if ([dict isKindOfClass:[NSDictionary class]]) {
                        AddressNode *node = [[AddressNode alloc] initWithDictionary:dict];
                        [tmp addObject:node];
                    }
                }
                roots = [tmp copy];
            }
            weakSelf.addressRoots = roots;
            // 缓存到磁盘
            NSData *data = [NSKeyedArchiver archivedDataWithRootObject:roots requiringSecureCoding:YES error:nil];
            [[NSUserDefaults standardUserDefaults] setObject:data forKey:kAddressCacheKey];
            [[NSUserDefaults standardUserDefaults] synchronize];
            if (completion) completion(roots, nil);
        } else {
            if (completion) completion(nil, error ?: [NSError errorWithDomain:@"AddressData" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Invalid data"}]);
        }
    }];
}

@end 