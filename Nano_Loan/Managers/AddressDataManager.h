///
///  AddressDataManager.h
///  Nano_Loan
///
///  Created by iOSDev on 2025/6/25.
///

#import <Foundation/Foundation.h>
@class AddressNode;

NS_ASSUME_NONNULL_BEGIN

typedef void(^AddressDataCompletion)(NSArray<AddressNode *> * _Nullable roots, NSError * _Nullable error);

/// 负责地址数据的获取、解析和缓存管理
@interface AddressDataManager : NSObject

+ (instancetype)sharedManager;

/// 获取根节点数组（大区列表）
- (void)fetchAddressDataIfNeededWithCompletion:(AddressDataCompletion)completion;

/// 强制刷新地址数据（忽略缓存）
- (void)forceRefreshAddressDataWithCompletion:(AddressDataCompletion)completion;

/// 直接读取缓存，如无缓存返回 nil
- (NSArray<AddressNode *> * _Nullable)cachedAddressRoots;

@end

NS_ASSUME_NONNULL_END 