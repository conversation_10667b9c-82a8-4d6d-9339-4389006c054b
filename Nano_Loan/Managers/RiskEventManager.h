#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/*
 
 埋点1：开始时间：首次点击发送验证码/ 语音验证码 时间，注册/登录 成功后上报
 埋点2：开始时间：点击证件上报按钮时间，结束时间：选择完成证件类型时间
 埋点3：开始时间：弹出引导证件正面拍照弹窗时间，结束时间：提交身份证件信息，接口返回成功时间
 埋点4：开始时间：点击人脸出现弹窗时间，结束时间：人脸上传成功时间
 埋点5-埋点8:开始时间：进入页面时间，结束时间：提交信息成功时间
 埋点9：开始时间结束时间一样，点击下一步 调用下单接口成功后上报
 埋点10：开始时间结束时间一样，收到 h5 交互函数（确认申请埋点调用方法）后上报
 埋点逻辑：每次进入页面页面获取GPS定位，更新当前位置。 提交埋点的时候，没获取到定位则GPS上报0。埋点上报异步进行，不得影响用户正常流程，报错也不要提示用户。埋点GPS上报 不允许做经纬度大于0判断
 */

/// 风控埋点类型
typedef NS_ENUM(NSInteger, RiskEventType) {
    RiskEventTypeRegister = 1,      // 注册-验证码
    RiskEventTypeDocumentSelect ,   // 证件类型选择
    RiskEventTypeDocumentFront ,    // 证件正面拍照
    RiskEventTypeFaceRecognition ,  // 人脸验证
    RiskEventTypePersonalInfo ,     // 个人信息
    RiskEventTypeJobInfo ,          // 工作信息
    RiskEventTypeContacts ,         // 联系人
    RiskEventTypeBindCard ,         // 绑卡
    RiskEventTypeStartLoan ,        // 开始审贷
    RiskEventTypeEndLoan            // 结束审贷
};

@interface RiskEventManager : NSObject

/// 单例
+ (instancetype)sharedManager;

/// 上报埋点
/// @param type 风控埋点类型 (listener)
/// @param startTime 开始时间（秒级时间戳）
/// @param endTime 结束时间（秒级时间戳）
/// @param orderId 订单号（type 9、10 必填，其余可 nil）
+ (void)reportEventType:(RiskEventType)type
             startTime:(NSTimeInterval)startTime
               endTime:(NSTimeInterval)endTime
               orderId:(nullable NSString *)orderId;

@end

NS_ASSUME_NONNULL_END 
