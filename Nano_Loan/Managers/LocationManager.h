#ifndef LocationManager_h
#define LocationManager_h

#import <Foundation/Foundation.h>
#import <CoreLocation/CoreLocation.h>

NS_ASSUME_NONNULL_BEGIN

/// 负责全局持续定位并进行位置上报的单例管理类
@interface LocationManager : NSObject <CLLocationManagerDelegate>

+ (instancetype)sharedManager;

/// 开启持续定位（若未授权则会弹询问框）
- (void)startUpdatingLocation;

/// 立刻尝试用最新定位数据（或测试数据）上报位置
- (void)reportLocationIfCached;

/// 获取最近一次定位
- (nullable CLLocation *)latestLocation;

@end

NS_ASSUME_NONNULL_END

#endif /* LocationManager_h */ 