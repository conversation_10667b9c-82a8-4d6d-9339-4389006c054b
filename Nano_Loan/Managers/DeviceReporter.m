#import "DeviceReporter.h"
#import "NetworkManager.h"
#import <AdSupport/ASIdentifierManager.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import "DeviceIDManager.h"
#import "AppDelegate.h"

@implementation DeviceReporter

+ (void)reportDeviceIdentifiers {
    AppDelegate *appDelegate = (AppDelegate *)[UIApplication sharedApplication].delegate;

    NSString *idfv = [DeviceIDManager persistentIDFV]; // 使用统一的设备ID管理器
    // 请求跟踪权限，如果尚未确定
    if (@available(iOS 14, *)) {
        ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];
        if (status == ATTrackingManagerAuthorizationStatusNotDetermined) {
            [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(__unused ATTrackingManagerAuthorizationStatus status) {
                // 无论结果如何，继续获取 IDFA
                [self fetchAndSendWithIDFV:idfv];
            }];
            return; // 等待回调
        }
    }
    [self fetchAndSendWithIDFV:idfv];
}

+ (void)fetchAndSendWithIDFV:(NSString *)idfv {
    NSString *idfa = @"";
    if ([[ASIdentifierManager sharedManager] isAdvertisingTrackingEnabled]) {
        idfa = [[[ASIdentifierManager sharedManager] advertisingIdentifier] UUIDString] ?: @"";
    }

    NSDictionary *params = @{ @"picnic": idfv ?: @"",
                              @"lunch": idfa ?: @"" };
    [self sendParams:params];
}

+ (void)sendParams:(NSDictionary *)params {
    [NetworkManager postFormWithAPI:@"Alicia/pickles" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
        if (error) {
            NSLog(@"[DeviceReporter] ID 上报失败: %@", error);
        } else {
            NSLog(@"[DeviceReporter] ID 上报成功: %@", response);
        }
    }];
}

@end 
