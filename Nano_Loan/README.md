# 金融贷款App

## 项目概述

本App是一款面向有贷款需求用户的金融贷款应用，提供贷款产品浏览、订单管理和个人中心等功能，界面现代简约，采用自定义浮岛风格TabBar，提升用户体验。

## 目标用户

主要面向有贷款需求的上班族、个体经营者等用户群体，注重操作便捷性和信息安全。

## 技术选型

- 开发语言：Objective-C
- UI框架：UIKit
- 最低支持系统：iOS 14.0
- 状态管理：原生
- 持久化：NSUserDefaults/后续可扩展Core Data
- UI风格：暗色科技风，现代简约，底部浮岛TabBar

## 应用结构

采用自定义TabBarController作为主导航，包含首页、订单、我的三个核心页面，所有页面均为独立ViewController，便于维护和扩展。

## 页面结构

| 页面/视图名称 | 用途 | 核心功能 | 技术实现 | 导航/用户流程 | 建议文件路径 |
|:--------:|:----:|:--------:|:--------:|:--------:|:--------:|
| 首页 | 展示贷款产品、活动、快捷入口 | 贷款产品列表、Banner、快捷操作 | UIKit，自定义ViewController | 启动后默认进入，可切换Tab | Nano_Loan/HomeViewController.m |
| 订单 | 展示用户贷款订单 | 订单列表、订单详情入口 | UIKit，自定义ViewController | Tab切换进入，可查看订单详情 | Nano_Loan/OrderViewController.m |
| 我的 | 展示个人信息与设置 | 个人资料、设置、退出登录 | UIKit，自定义ViewController | Tab切换进入，可进入设置等 | Nano_Loan/ProfileViewController.m |
| 自定义TabBar | 提供底部浮岛样式导航 | 3个按钮，浮岛、阴影、圆角 | UIKit，自定义TabBarController和TabBar | 全局底部导航 | Nano_Loan/CustomTabBarController.m、Nano_Loan/CustomTabBar.m |

## 数据模型

初期以本地数据为主，后续可扩展为网络数据和本地持久化。

### 地址数据 (AddressNode)

启动时向 `Alicia/salad` 接口异步请求三级地址数据，字段经过混淆处理，仅保留 `darn` (ID) 与 `excitedbecause` (名称) 以及嵌套 `boasting`。解析后转为递归模型 `AddressNode`（实现 NSSecureCoding），并通过 `AddressDataManager` 进行缓存与读取，避免重复网络请求，供后续地址选择器弹窗使用。

```mermaid
graph TD
    LaunchViewController --> AddressDataManager
    AddressDataManager --> NetworkManager
    AddressDataManager -->|缓存| NSUserDefaults
```

## 技术实现细节

### 启动页动态初始化 Facebook SDK

启动页 `LaunchViewController` 在完成登录初始化接口 `Alicia/bigger` 请求后，会解析返回字段 `fearlessness`，其中包含 Facebook SDK 所需 4 项配置：

| 字段 | 含义 | 绑定 FBSDKSettings 属性 |
|:----:|:----:|:-----------------------:|
| eventhe | CFBundleURLScheme | appURLSchemeSuffix |
| gotback | FacebookAppID | appID |
| chicken | FacebookDisplayName | displayName |
| ginger | FacebookClientToken | clientToken |

实现逻辑：
1. 成功解析到 `fearlessness` 字典后，通过 `UIApplication.sharedApplication.delegate` 获取 `AppDelegate`。
2. 调用新增方法 `configureFacebookSDKWithParameters:` 将字典回传给 AppDelegate。
3. `AppDelegate` 内部对参数进行合法性校验并动态调用 `FBSDKSettings` 及 `FBSDKApplicationDelegate` 完成 SDK 初始化。
4. 初始化成功后输出日志 `✅ Facebook SDK 已完成动态初始化`，方便调试。

这样可确保 Facebook SDK 的配置由后台下发，便于渠道区分和动态调整，无需每次发版都修改 Info.plist。

## 开发状态跟踪

| 页面/组件名称 | 开发状态 | 文件路径 |
|:-------------:|:--------:|:------------:|
| 首页 | 未开始 | Nano_Loan/HomeViewController.m |
| 订单 | 未开始 | Nano_Loan/OrderViewController.m |
| 我的 | 未开始 | Nano_Loan/ProfileViewController.m |
| 自定义TabBar | 未开始 | Nano_Loan/CustomTabBarController.m、Nano_Loan/CustomTabBar.m |
| 地址数据管理 | 已完成 | Nano_Loan/Managers/AddressDataManager.m | 