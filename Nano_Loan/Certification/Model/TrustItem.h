//  TrustItem.h
//  Nano_Loan

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface TrustItem : NSObject

@property (nonatomic, copy) NSString *title;      // coldchicken
@property (nonatomic, copy) NSString *subtitle;   // replies
@property (nonatomic, copy) NSString *link;       // toteach
@property (nonatomic, copy) NSString *iconURL;    // sallyhope
@property (nonatomic, assign) BOOL completed;     // amused==1
@property (nonatomic, copy) NSString *type;       // shookhands 认证类型

- (instancetype)initWithDictionary:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END 