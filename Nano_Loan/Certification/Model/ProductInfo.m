//  ProductInfo.m
//  Nano_Loan

#import "ProductInfo.h"

@implementation ProductInfo

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    self = [super init];
    if (self) {
        _productId = [NSString stringWithFormat:@"%@", dict[@"darn"] ?: @""];
        _name = dict[@"cando"] ?: @"";
        _imageURL = dict[@"tapestrywork"] ?: @"";
        _applyLabel = dict[@"embroidery"] ?: @"Apply";
        _loanAmountLabel = dict[@"heardgwendoline"] ?: @"Loan Amount";
        _termLabel = dict[@"fibs"] ?: @"Loan Term";
        _amountOptions = dict[@"fondly"] ?: @[];
        _symbol = dict[@"symbol"] ?: @"";
        _termValue = dict[@"twowomen"] ?: @"";
        _h5Link = dict[@"toteach"] ?: @"";
        _team = [NSString stringWithFormat:@"%@", dict[@"team"] ?: @""];
        _orderNumber = [NSString stringWithFormat:@"%@", dict[@"begnning"] ?: @""];
        _termType = [NSString stringWithFormat:@"%@", dict[@"shaggier"] ?: @""];
    }
    return self;
}

@end 