//  IdCardInfo.h
//  Nano_Loan

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface IdCardInfo : NSObject

@property (nonatomic, copy) NSString *frontMsg;   // id_front_msg
@property (nonatomic, copy) NSString *faceMsg;    // face_msg
@property (nonatomic, strong) NSArray<NSString *> *primaryIDs;   // beenwaiting
@property (nonatomic, strong) NSArray<NSString *> *secondaryIDs; // knocking
@property (nonatomic, assign) NSInteger status;   // waydown 或 amused 等
@property (nonatomic, assign) BOOL frontCompleted;
@property (nonatomic, copy) NSString *frontImageURL;
@property (nonatomic, copy) NSString *frontDocType;
@property (nonatomic, assign) BOOL faceCompleted;
@property (nonatomic, copy) NSString *faceImageURL;

- (instancetype)initWithDictionary:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END 