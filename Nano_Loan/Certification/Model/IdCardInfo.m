//  IdCardInfo.m
//  Nano_Loan

#import "IdCardInfo.h"

@implementation IdCardInfo

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    self = [super init];
    if (self) {
        _frontMsg = dict[@"id_front_msg"] ?: @"";
        _faceMsg = dict[@"face_msg"] ?: @"";
        _primaryIDs = dict[@"beenwaiting"] ?: @[];
        _secondaryIDs = dict[@"knocking"] ?: @[];
        _status = [dict[@"waydown"] integerValue];

        NSDictionary *youmust = dict[@"youmust"];
        if ([youmust isKindOfClass:[NSDictionary class]]) {
            _frontCompleted = ([youmust[@"amused"] integerValue] == 1);
            _frontImageURL = youmust[@"toteach"] ?: @"";
            _frontDocType = youmust[@"withcolour"] ?: @"";
        }
        NSDictionary *glowing = dict[@"glowing"];
        if ([glowing isKindOfClass:[NSDictionary class]]) {
            _faceCompleted = ([glowing[@"amused"] integerValue] == 1);
            _faceImageURL = glowing[@"toteach"] ?: @"";
        }
    }
    return self;
}

@end 