//  TrustItem.m
//  Nano_Loan

#import "TrustItem.h"

@implementation TrustItem

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    self = [super init];
    if (self) {
        _title = dict[@"coldchicken"] ?: @"";//认证项的title
        _subtitle = dict[@"replies"] ?: @"";
        _link = dict[@"toteach"] ?: @"";
        _iconURL = dict[@"sallyhope"] ?: @"";//认证项背景图，底图+logo
        _completed = [dict[@"amused"] integerValue] == 1;
        _type = dict[@"shookhands"] ?: @"";
    }
    return self;
}

@end 
