//  ProductInfo.h
//  Nano_Loan
//  自动生成的产品信息模型

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface ProductInfo : NSObject

@property (nonatomic, copy) NSString *productId;   // darn
@property (nonatomic, copy) NSString *name;        // cando
@property (nonatomic, copy) NSString *imageURL;    // tapestrywork
@property (nonatomic, copy) NSString *applyLabel;  // embroidery
@property (nonatomic, copy) NSString *loanAmountLabel; // heardgwendoline
@property (nonatomic, copy) NSString *termLabel;   // fibs
@property (nonatomic, strong) NSArray<NSNumber *> *amountOptions; // fondly
@property (nonatomic, copy) NSString *symbol;      // symbol
@property (nonatomic, copy) NSString *termValue;   // twowomen
@property (nonatomic, copy) NSString *h5Link;      // toteach
@property (nonatomic, copy) NSString *team;        // team
@property (nonatomic, copy) NSString *orderNumber;   // begnning
@property (nonatomic, copy) NSString *termType;      // shaggier

- (instancetype)initWithDictionary:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END 