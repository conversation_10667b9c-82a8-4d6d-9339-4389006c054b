//
//  PhotoSourcePickerView.h
//  Nano_Loan
//
//  Created for presenting custom photo source selection UI.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, PhotoSourceType) {
    PhotoSourceTypeCamera = 1,
    PhotoSourceTypeAlbum = 2
};

typedef void (^PhotoSourcePickerCompletionBlock)(PhotoSourceType selectedType);

@interface PhotoSourcePickerView : UIView

// 初始化方法，传入是否显示相册选项的参数
- (instancetype)initWithAllowAlbum:(BOOL)allowAlbum completion:(PhotoSourcePickerCompletionBlock)completion;

// 从父视图显示/隐藏
- (void)showInView:(UIView *)parentView animated:(BOOL)animated;
- (void)dismissAnimated:(BOOL)animated;

@end

NS_ASSUME_NONNULL_END 