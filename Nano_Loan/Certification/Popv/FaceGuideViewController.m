//
//  FaceGuideViewController.m
//  Nano_Loan
//
//  Created for displaying face recognition guidance UI.
//

#import "FaceGuideViewController.h"

#define SCREEN_WIDTH [UIScreen mainScreen].bounds.size.width
#define SCREEN_HEIGHT [UIScreen mainScreen].bounds.size.height
#define SCALE_375 (SCREEN_WIDTH / 375.0)

@interface FaceGuideViewController ()

@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) UIView *dimmingView;
@property (nonatomic, strong) UIButton *closeButton;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIImageView *faceIllustrationView;
@property (nonatomic, strong) UIImageView *tipImageView;
@property (nonatomic, strong) UIButton *goButton;
@property (nonatomic, copy) FaceGuideCompletionBlock completionBlock;

@end

@implementation FaceGuideViewController

#pragma mark - Lifecycle

- (instancetype)initWithCompletion:(FaceGuideCompletionBlock)completion {
    self = [super initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
    if (self) {
        _completionBlock = [completion copy];
        [self setupUI];
    }
    return self;
}

#pragma mark - Private Helpers

// 计算容器高度（包含安全区域调整）
- (CGFloat)calculatedContainerHeight {
    UIWindow *window = UIApplication.sharedApplication.windows.firstObject;
    CGFloat bottomSafeAreaInset = window.safeAreaInsets.bottom;
    CGFloat baseHeight = 455 * SCALE_375;
    return baseHeight + bottomSafeAreaInset - 35; // 减少35pt的多余空间
}

#pragma mark - UI Setup

- (void)setupUI {
    // 半透明背景
    self.dimmingView = [[UIView alloc] initWithFrame:self.bounds];
    self.dimmingView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.5];
    self.dimmingView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self addSubview:self.dimmingView];
    
    // 为背景添加点击事件
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dimmingViewTapped)];
    [self.dimmingView addGestureRecognizer:tapGesture];
    
    // 容器视图 - 底部对齐
    CGFloat adjustedHeight = [self calculatedContainerHeight];
    
    self.containerView = [[UIView alloc] init];
    self.containerView.layer.cornerRadius = 20.0;
    // 只圆角顶部
    self.containerView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    self.containerView.layer.masksToBounds = YES;
    self.containerView.backgroundColor = [UIColor clearColor];
    self.containerView.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.containerView];
    
    // 更新布局约束 - 底部对齐，考虑安全区域
    [NSLayoutConstraint activateConstraints:@[
        [self.containerView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor],
        [self.containerView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor],
        [self.containerView.bottomAnchor constraintEqualToAnchor:self.bottomAnchor],
        [self.containerView.heightAnchor constraintEqualToConstant:adjustedHeight]
    ]];
    
    // 背景图 - 复用PhotoSourcePickerView的背景图
    UIImageView *bgImageView = [[UIImageView alloc] init];
    bgImageView.image = [UIImage imageNamed:@"photo_source_picker_bg"];
    bgImageView.contentMode = UIViewContentModeScaleToFill;
    bgImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.containerView addSubview:bgImageView];
    
    [NSLayoutConstraint activateConstraints:@[
        [bgImageView.topAnchor constraintEqualToAnchor:self.containerView.topAnchor],
        [bgImageView.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor],
        [bgImageView.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor],
        [bgImageView.bottomAnchor constraintEqualToAnchor:self.containerView.bottomAnchor]
    ]];
    
    // 标题 - 使用与PhotoSourcePickerView相同的样式
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"Facial Recognition";
    self.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:20];
    self.titleLabel.textColor = [UIColor whiteColor];
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.containerView addSubview:self.titleLabel];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.containerView.topAnchor constant:11],
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor constant:30]
    ]];
    
    // 关闭按钮 - 复用PhotoSourcePickerView的关闭按钮
    self.closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.closeButton setImage:[UIImage imageNamed:@"photo_source_picker_close"] forState:UIControlStateNormal];
    self.closeButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.containerView addSubview:self.closeButton];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.closeButton.topAnchor constraintEqualToAnchor:self.containerView.topAnchor constant:20],
        [self.closeButton.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor constant:-20],
        [self.closeButton.widthAnchor constraintEqualToConstant:30],
        [self.closeButton.heightAnchor constraintEqualToConstant:30]
    ]];
    
    // 人脸插图 - 暂用占位名，后续替换
    self.faceIllustrationView = [[UIImageView alloc] init];
    self.faceIllustrationView.image = [UIImage imageNamed:@"face_guide_illustration"];
    self.faceIllustrationView.contentMode = UIViewContentModeScaleAspectFit;
    self.faceIllustrationView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.containerView addSubview:self.faceIllustrationView];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.faceIllustrationView.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor constant:30],
        [self.faceIllustrationView.centerXAnchor constraintEqualToAnchor:self.containerView.centerXAnchor],
        [self.faceIllustrationView.widthAnchor constraintEqualToConstant:240 * SCALE_375],
        [self.faceIllustrationView.heightAnchor constraintEqualToConstant:160 * SCALE_375]
    ]];
    
    // 替换提示容器和文本为图片
    self.tipImageView = [[UIImageView alloc] init];
    self.tipImageView.image = [UIImage imageNamed:@"face_guide_tip_text"];
    self.tipImageView.contentMode = UIViewContentModeScaleAspectFit;
    self.tipImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.containerView addSubview:self.tipImageView];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.tipImageView.topAnchor constraintEqualToAnchor:self.faceIllustrationView.bottomAnchor constant:20],
        [self.tipImageView.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor constant:24],
        [self.tipImageView.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor constant:-24],
        [self.tipImageView.heightAnchor constraintEqualToConstant:80 * SCALE_375]
    ]];
    
    // Go 按钮 - 使用占位名，后续替换
    self.goButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.goButton setTitle:@"Go" forState:UIControlStateNormal];
    [self.goButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.goButton.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:20];
    self.goButton.backgroundColor = [UIColor clearColor];
    [self.goButton setBackgroundImage:[UIImage imageNamed:@"face_guide_go_btn_bg"] forState:UIControlStateNormal];
    self.goButton.layer.cornerRadius = 25;
    self.goButton.layer.masksToBounds = YES;
    self.goButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.goButton addTarget:self action:@selector(goButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.containerView addSubview:self.goButton];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.goButton.topAnchor constraintEqualToAnchor:self.tipImageView.bottomAnchor constant:30],
        [self.goButton.centerXAnchor constraintEqualToAnchor:self.containerView.centerXAnchor],
        [self.goButton.widthAnchor constraintEqualToConstant:300 * SCALE_375],
        [self.goButton.heightAnchor constraintEqualToConstant:50]
    ]];
    
    // 初始状态：透明度为0，位置在屏幕底部以外
    self.alpha = 0;
    self.containerView.transform = CGAffineTransformMakeTranslation(0, [self calculatedContainerHeight]);
}

#pragma mark - Actions

- (void)goButtonTapped {
    if (self.completionBlock) {
        self.completionBlock();
    }
    [self dismissAnimated:YES];
}

- (void)closeButtonTapped {
    [self dismissAnimated:YES];
}

- (void)dimmingViewTapped {
    [self dismissAnimated:YES];
}

#pragma mark - Show/Dismiss

- (void)showInView:(UIView *)parentView animated:(BOOL)animated {
    [parentView addSubview:self];
    
    if (animated) {
        [UIView animateWithDuration:0.3 
                          delay:0.0 
                        options:UIViewAnimationOptionCurveEaseOut 
                     animations:^{
            self.alpha = 1.0;
            self.containerView.transform = CGAffineTransformIdentity;
        } completion:nil];
    } else {
        self.alpha = 1.0;
        self.containerView.transform = CGAffineTransformIdentity;
    }
}

- (void)dismissAnimated:(BOOL)animated {
    if (animated) {
        // 使用帮助方法获取高度
        CGFloat adjustedHeight = [self calculatedContainerHeight];
        
        [UIView animateWithDuration:0.3 
                          delay:0.0 
                        options:UIViewAnimationOptionCurveEaseIn 
                     animations:^{
            self.alpha = 0;
            self.containerView.transform = CGAffineTransformMakeTranslation(0, adjustedHeight);
        } completion:^(BOOL finished) {
            [self removeFromSuperview];
        }];
    } else {
        [self removeFromSuperview];
    }
}

@end 