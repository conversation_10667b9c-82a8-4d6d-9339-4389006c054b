//
//  IDInfoEditorViewController.h
//  Nano_Loan
//
//  Created for displaying ID information confirmation UI.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^IDInfoEditorCompletionBlock)(NSDictionary *editedInfo, BOOL isCancelled);

@interface IDInfoEditorViewController : UIView

// 初始化方法，传入身份信息数组和完成回调
- (instancetype)initWithInfoItems:(NSArray<NSDictionary *> *)items 
                      completion:(IDInfoEditorCompletionBlock)completion;

// 从父视图显示/隐藏
- (void)showInView:(UIView *)parentView animated:(BOOL)animated;
- (void)dismissAnimated:(BOOL)animated;

// 重新启用确认按钮（保存失败时调用）
- (void)enableConfirmButton;

@end

NS_ASSUME_NONNULL_END 