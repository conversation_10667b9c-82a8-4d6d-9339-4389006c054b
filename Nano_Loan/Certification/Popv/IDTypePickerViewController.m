//  IDTypePickerViewController.m
//  Nano_Loan
//
//  Created to replace the old UIAlertController-based ID type picker with a custom designed modal page that matches the product style.
//
//  使用示例：
//  IDTypePickerViewController *picker = [[IDTypePickerViewController alloc] initWithPrimaryIDs:primaryIDs
//                                                                                secondaryIDs:secondaryIDs
//                                                                         selectionHandler:^(NSString * _Nonnull docType) {
//      // handle selection
//  }];
//  picker.modalPresentationStyle = UIModalPresentationOverCurrentContext;
//  [self presentViewController:picker animated:NO completion:nil];

#import "IDTypePickerViewController.h"

#define SCALE_375 (UIScreen.mainScreen.bounds.size.width / 375.0)

@interface IDTypePickerViewController ()
@property (nonatomic, strong) NSArray<NSString *> *primaryIDs;
@property (nonatomic, strong) NSArray<NSString *> *secondaryIDs;
@property (nonatomic, copy) IDTypePickerSelectionHandler selectionHandler;

@property (nonatomic, strong) UIView *dimmingView; // 半透明遮罩
@property (nonatomic, strong) UIScrollView *contentScrollView;
@property (nonatomic, strong) UIView *contentContainer;
@property (nonatomic, strong) UIView *navView; // 自定义导航栏
@end

@implementation IDTypePickerViewController

#pragma mark - Initializer

- (instancetype)initWithPrimaryIDs:(NSArray<NSString *> *)primaryIDs
                      secondaryIDs:(NSArray<NSString *> *)secondaryIDs
                   selectionHandler:(IDTypePickerSelectionHandler)handler {
    self = [super initWithNibName:nil bundle:nil];
    if (self) {
        _primaryIDs = primaryIDs ?: @[];
        _secondaryIDs = secondaryIDs ?: @[];
        _selectionHandler = [handler copy];
        self.modalPresentationStyle = UIModalPresentationOverCurrentContext;
        self.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    }
    return self;
}

#pragma mark - View Lifecycle

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // 设置全屏背景图
    UIImageView *fullBg = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"general_background"]];
    fullBg.frame = self.view.bounds;
    fullBg.contentMode = UIViewContentModeScaleAspectFill;
    fullBg.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self.view addSubview:fullBg];
    
    // 页面基本设置
    self.view.backgroundColor = UIColor.clearColor;
    
    // 创建自定义导航栏
    [self setupCustomNav];
    
    // 创建内容区域
    [self setupContentUI];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.contentContainer.transform = CGAffineTransformMakeTranslation(0, self.view.bounds.size.height);
    [UIView animateWithDuration:0.25 animations:^{
        self.contentContainer.transform = CGAffineTransformIdentity;
    }];
}

#pragma mark - UI Setup

- (void)setupCustomNav {
    self.navView = [[UIView alloc] init];
    self.navView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.navView];

    UILayoutGuide *safe = self.view.safeAreaLayoutGuide;
    [NSLayoutConstraint activateConstraints:@[
        [self.navView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.navView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.navView.topAnchor constraintEqualToAnchor:safe.topAnchor],
        [self.navView.heightAnchor constraintEqualToConstant:44]
    ]];

    // Close button
    UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeBtn setImage:[UIImage imageNamed:@"nav_back"] forState:UIControlStateNormal]; // 可替换为关闭图标
    closeBtn.translatesAutoresizingMaskIntoConstraints = NO;
    [closeBtn addTarget:self action:@selector(backgroundTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.navView addSubview:closeBtn];
    [NSLayoutConstraint activateConstraints:@[
        [closeBtn.leadingAnchor constraintEqualToAnchor:self.navView.leadingAnchor constant:16],
        [closeBtn.centerYAnchor constraintEqualToAnchor:self.navView.centerYAnchor],
        [closeBtn.widthAnchor constraintEqualToConstant:34],
        [closeBtn.heightAnchor constraintEqualToConstant:34]
    ]];

    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    titleLabel.text = @"Select ID";
    titleLabel.textColor = UIColor.whiteColor;
    titleLabel.font = [UIFont boldSystemFontOfSize:22];
    [self.navView addSubview:titleLabel];
    [NSLayoutConstraint activateConstraints:@[
        [titleLabel.centerXAnchor constraintEqualToAnchor:self.navView.centerXAnchor],
        [titleLabel.centerYAnchor constraintEqualToAnchor:self.navView.centerYAnchor]
    ]];
}

- (void)setupContentUI {
    // 滚动视图 - 支持长列表
    self.contentScrollView = [[UIScrollView alloc] init];
    self.contentScrollView.translatesAutoresizingMaskIntoConstraints = NO;
    self.contentScrollView.alwaysBounceVertical = YES;
    self.contentScrollView.showsVerticalScrollIndicator = NO;

    // 内容容器
    self.contentContainer = [[UIView alloc] init];
    self.contentContainer.translatesAutoresizingMaskIntoConstraints = NO;
    self.contentContainer.backgroundColor = UIColor.clearColor;

    [self.view addSubview:self.contentScrollView];
    [self.contentScrollView addSubview:self.contentContainer];

    UILayoutGuide *guide = self.view.safeAreaLayoutGuide;
    CGFloat horizontalMargin = 14.0 * SCALE_375;  // 适配不同屏幕宽度

    // 滚动视图约束
    [NSLayoutConstraint activateConstraints:@[
        [self.contentScrollView.leadingAnchor constraintEqualToAnchor:guide.leadingAnchor constant:horizontalMargin],
        [self.contentScrollView.trailingAnchor constraintEqualToAnchor:guide.trailingAnchor constant:-horizontalMargin],
        [self.contentScrollView.topAnchor constraintEqualToAnchor:self.navView.bottomAnchor constant:16],
        [self.contentScrollView.bottomAnchor constraintEqualToAnchor:guide.bottomAnchor constant:-20]
    ]];

    // 内容容器约束 - 确保宽度与滚动视图一致
    [NSLayoutConstraint activateConstraints:@[
        [self.contentContainer.leadingAnchor constraintEqualToAnchor:self.contentScrollView.leadingAnchor],
        [self.contentContainer.trailingAnchor constraintEqualToAnchor:self.contentScrollView.trailingAnchor],
        [self.contentContainer.topAnchor constraintEqualToAnchor:self.contentScrollView.topAnchor],
        [self.contentContainer.bottomAnchor constraintEqualToAnchor:self.contentScrollView.bottomAnchor],
        [self.contentContainer.widthAnchor constraintEqualToAnchor:self.contentScrollView.widthAnchor]
    ]];

    // 创建分组区块
    CGFloat sectionSpacing = 24.0;  // 组间距
    UIView *lastSectionView = nil;
    
    // 设定固定高度（原始设计图尺寸）
    CGFloat primarySectionHeight = 0;  // 第一组自适应高度
    CGFloat secondarySectionHeight = 422.0;  // 第二组固定高度，来自设计图

    // 主要ID类型组 - 自适应高度
    if (self.primaryIDs.count > 0) {
        UIView *section = [self sectionViewWithTitle:@"Recommended ID Type"
                                           iconName:@"idTypeSectionIcon"
                                         background:@"idTypeGroupTopBg"
                                              items:self.primaryIDs];
        section.translatesAutoresizingMaskIntoConstraints = NO;
        [self.contentContainer addSubview:section];
        
        [NSLayoutConstraint activateConstraints:@[
            [section.leadingAnchor constraintEqualToAnchor:self.contentContainer.leadingAnchor],
            [section.trailingAnchor constraintEqualToAnchor:self.contentContainer.trailingAnchor],
            [section.topAnchor constraintEqualToAnchor:self.contentContainer.topAnchor]
        ]];
        lastSectionView = section;
    }

    // 次要ID类型组 - 固定高度
    if (self.secondaryIDs.count > 0) {
        UIView *containerView = [[UIView alloc] init];
        containerView.translatesAutoresizingMaskIntoConstraints = NO;
        [self.contentContainer addSubview:containerView];
        
        // 设置固定高度约束
        [NSLayoutConstraint activateConstraints:@[
            [containerView.leadingAnchor constraintEqualToAnchor:self.contentContainer.leadingAnchor],
            [containerView.trailingAnchor constraintEqualToAnchor:self.contentContainer.trailingAnchor],
            [containerView.topAnchor constraintEqualToAnchor:(lastSectionView ? lastSectionView.bottomAnchor : self.contentContainer.topAnchor) 
                                              constant:(lastSectionView ? sectionSpacing : 0)],
            [containerView.heightAnchor constraintEqualToConstant:secondarySectionHeight]
        ]];
        
        // 背景图 - 填充整个固定高度容器
        UIImageView *bgImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"idTypeGroupBottomBg"]];
        bgImageView.translatesAutoresizingMaskIntoConstraints = NO;
        bgImageView.contentMode = UIViewContentModeScaleToFill;
        [containerView addSubview:bgImageView];
        
        [NSLayoutConstraint activateConstraints:@[
            [bgImageView.leadingAnchor constraintEqualToAnchor:containerView.leadingAnchor],
            [bgImageView.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor],
            [bgImageView.topAnchor constraintEqualToAnchor:containerView.topAnchor],
            [bgImageView.bottomAnchor constraintEqualToAnchor:containerView.bottomAnchor]
        ]];
        
        // 创建堆栈视图放置按钮 - 向上靠齐
        UIStackView *stack = [[UIStackView alloc] init];
        stack.translatesAutoresizingMaskIntoConstraints = NO;
        stack.axis = UILayoutConstraintAxisVertical;
        stack.alignment = UIStackViewAlignmentFill;
        stack.distribution = UIStackViewDistributionFill;
        stack.spacing = 12.0;
        [containerView addSubview:stack];
        
        // 堆栈视图约束 - 向上靠齐，不关联底部
        [NSLayoutConstraint activateConstraints:@[
            [stack.leadingAnchor constraintEqualToAnchor:containerView.leadingAnchor constant:12],
            [stack.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor constant:-12],
            [stack.topAnchor constraintEqualToAnchor:containerView.topAnchor constant:72] // 顶部留出60pt标题区域 + 12pt间距
        ]];
        
        // 添加按钮
        for (NSString *doc in self.secondaryIDs) {
            UIButton *btn = [self cellButtonWithTitle:doc];
            [stack addArrangedSubview:btn];
            [btn addTarget:self action:@selector(cellTapped:) forControlEvents:UIControlEventTouchUpInside];
        }
        
        lastSectionView = containerView;
    }

    // 确保最后一个组与容器底部对齐
    if (lastSectionView) {
        [NSLayoutConstraint activateConstraints:@[
            [lastSectionView.bottomAnchor constraintEqualToAnchor:self.contentContainer.bottomAnchor]
        ]];
    }
}

#pragma mark - Section builder

- (UIView *)sectionViewWithTitle:(NSString *)title
                        iconName:(NSString *)iconName
                      background:(NSString *)bgName
                           items:(NSArray<NSString *> *)items {
    // 创建容器视图
    UIView *container = [[UIView alloc] init];
    container.translatesAutoresizingMaskIntoConstraints = NO;
    
    // 创建背景图视图
    UIImageView *backgroundImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:bgName]];
    backgroundImageView.translatesAutoresizingMaskIntoConstraints = NO;
    backgroundImageView.contentMode = UIViewContentModeScaleToFill;
    [container addSubview:backgroundImageView];
    
    // 背景图约束 - 填充整个容器
    [NSLayoutConstraint activateConstraints:@[
        [backgroundImageView.leadingAnchor constraintEqualToAnchor:container.leadingAnchor],
        [backgroundImageView.trailingAnchor constraintEqualToAnchor:container.trailingAnchor],
        [backgroundImageView.topAnchor constraintEqualToAnchor:container.topAnchor],
        [backgroundImageView.bottomAnchor constraintEqualToAnchor:container.bottomAnchor]
    ]];
    
    // 创建垂直堆栈视图来放置cell按钮
    UIStackView *stack = [[UIStackView alloc] init];
    stack.translatesAutoresizingMaskIntoConstraints = NO;
    stack.axis = UILayoutConstraintAxisVertical;
    stack.alignment = UIStackViewAlignmentFill;
    stack.distribution = UIStackViewDistributionFill;
    stack.spacing = 12.0;
    [container addSubview:stack];
    
    // 堆栈视图约束
    [NSLayoutConstraint activateConstraints:@[
        [stack.leadingAnchor constraintEqualToAnchor:container.leadingAnchor constant:12],
        [stack.trailingAnchor constraintEqualToAnchor:container.trailingAnchor constant:-12],
        [stack.topAnchor constraintEqualToAnchor:container.topAnchor constant:72], // 顶部留出60pt + 12pt间距
        [stack.bottomAnchor constraintEqualToAnchor:container.bottomAnchor constant:-32] // 底部留出20pt + 12pt间距
    ]];
    
    // 添加cell按钮
    for (NSString *doc in items) {
        UIButton *btn = [self cellButtonWithTitle:doc];
        [stack addArrangedSubview:btn];
    }
    
    return container;
}

- (UIButton *)cellButtonWithTitle:(NSString *)title {
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
    btn.translatesAutoresizingMaskIntoConstraints = NO;
    
    // 设置cell背景图
    [btn setBackgroundImage:[UIImage imageNamed:@"idTypeCellBg"] forState:UIControlStateNormal];
    
    // 设置文本对齐和内边距
    btn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    btn.titleEdgeInsets = UIEdgeInsetsMake(0, 16, 0, 0);
    
    // 设置文本样式
    [btn setTitle:title forState:UIControlStateNormal];
    [btn setTitleColor:UIColor.blackColor forState:UIControlStateNormal];
    btn.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightSemibold];
    
    // 设置高度固定为50pt
    [btn.heightAnchor constraintEqualToConstant:50.0].active = YES;
    
    // 添加右侧箭头图标
    UIImageView *arrow = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"setting_cell_arrow"]];
    arrow.translatesAutoresizingMaskIntoConstraints = NO;
    [btn addSubview:arrow];
    
    // 箭头约束
    [NSLayoutConstraint activateConstraints:@[
        [arrow.trailingAnchor constraintEqualToAnchor:btn.trailingAnchor constant:-16],
        [arrow.centerYAnchor constraintEqualToAnchor:btn.centerYAnchor],
        [arrow.widthAnchor constraintEqualToConstant:20],
        [arrow.heightAnchor constraintEqualToConstant:20]
    ]];
    
    // 添加点击事件
    [btn addTarget:self action:@selector(cellTapped:) forControlEvents:UIControlEventTouchUpInside];
    
    return btn;
}

#pragma mark - Actions

- (void)cellTapped:(UIButton *)sender {
    NSString *docType = sender.titleLabel.text ?: @"";
    if (self.selectionHandler) {
        self.selectionHandler(docType);
    }
    [self dismissPicker];
}

- (void)backgroundTapped {
    [self dismissPicker];
}

- (void)dismissPicker {
    [UIView animateWithDuration:0.25 animations:^{
        self.contentContainer.transform = CGAffineTransformMakeTranslation(0, self.view.bounds.size.height);
    } completion:^(BOOL finished) {
        [self dismissViewControllerAnimated:NO completion:nil];
    }];
}

@end 
