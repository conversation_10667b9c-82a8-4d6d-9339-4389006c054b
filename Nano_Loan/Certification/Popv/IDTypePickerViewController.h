///  IDTypePickerViewController.h
///  Nano_Loan
///
///  Created for presenting ID type selection list with custom UI.
///
///  This controller is presented modally over current context and provides a callback when user selects an ID type.

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^IDTypePickerSelectionHandler)(NSString *docType);

@interface IDTypePickerViewController : UIViewController

- (instancetype)initWithPrimaryIDs:(NSArray<NSString *> *)primaryIDs
                      secondaryIDs:(NSArray<NSString *> *)secondaryIDs
                   selectionHandler:(IDTypePickerSelectionHandler)handler;

@end

NS_ASSUME_NONNULL_END 