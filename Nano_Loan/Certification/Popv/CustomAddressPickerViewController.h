//
//  CustomAddressPickerViewController.h
//  Nano_Loan
//
//  自定义三级地址选择器，支持省市区三级联动选择。
//

#import <UIKit/UIKit.h>
@class AddressNode;

NS_ASSUME_NONNULL_BEGIN

typedef void(^CustomAddressPickerResult)(AddressNode *province, AddressNode *city, AddressNode *district);

@interface CustomAddressPickerViewController : UIViewController

/// 初始化方法，roots 为省级节点数组
- (instancetype)initWithAddressRoots:(NSArray<AddressNode *> *)roots
                           selection:(nullable CustomAddressPickerResult)result;

@end

NS_ASSUME_NONNULL_END 