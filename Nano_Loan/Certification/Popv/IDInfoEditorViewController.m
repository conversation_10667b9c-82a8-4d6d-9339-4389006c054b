//
//  IDInfoEditorViewController.m
//  Nano_Loan
//
//  Created for displaying ID information confirmation UI.
//

#import "IDInfoEditorViewController.h"
#import "DatePickerViewController.h"
#import <objc/runtime.h>

#define SCREEN_WIDTH [UIScreen mainScreen].bounds.size.width
#define SCREEN_HEIGHT [UIScreen mainScreen].bounds.size.height
#define SCALE_375 (SCREEN_WIDTH / 375.0)

@interface IDInfoEditorTableCell : UIView

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UITextField *valueField;
@property (nonatomic, strong) UIButton *accessoryButton; // 仅用于日期选择
@property (nonatomic, assign) BOOL isDateField;
@property (nonatomic, assign) BOOL isNumberField;

- (instancetype)initWithTitle:(NSString *)title value:(NSString *)value isDateField:(BOOL)isDateField isNumberField:(BOOL)isNumberField;

@end

@implementation IDInfoEditorTableCell

- (instancetype)initWithTitle:(NSString *)title value:(NSString *)value isDateField:(BOOL)isDateField isNumberField:(BOOL)isNumberField {
    self = [super init];
    if (self) {
        self.isDateField = isDateField;
        self.isNumberField = isNumberField;
        self.backgroundColor = [UIColor whiteColor];
        self.layer.cornerRadius = 12;
        self.layer.masksToBounds = YES;
        
        // 左侧标题
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.text = title;
        _titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightRegular];
        _titleLabel.textColor = [UIColor darkGrayColor];
        _titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
        [self addSubview:_titleLabel];
        
        // 右侧内容
        _valueField = [[UITextField alloc] init];
        _valueField.text = value;
        _valueField.font = [UIFont systemFontOfSize:16 weight:UIFontWeightSemibold];
        _valueField.textColor = [UIColor blackColor];
        _valueField.textAlignment = NSTextAlignmentRight;
        _valueField.translatesAutoresizingMaskIntoConstraints = NO;
        _valueField.enabled = !isDateField; // 日期字段不允许直接编辑
        _valueField.keyboardType = isNumberField ? UIKeyboardTypeASCIICapable : UIKeyboardTypeDefault;

        // 添加调试日志
        NSLog(@"📝 [单元格创建] title:'%@' value:'%@' enabled:%@ userInteractionEnabled:%@",
              title, value, _valueField.enabled ? @"YES" : @"NO", self.userInteractionEnabled ? @"YES" : @"NO");

        [self addSubview:_valueField];
        
        // 布局约束
        [NSLayoutConstraint activateConstraints:@[
            [_titleLabel.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:16],
            [_titleLabel.centerYAnchor constraintEqualToAnchor:self.centerYAnchor]
        ]];
        
        // 如果是日期字段，添加箭头按钮
        if (isDateField) {
            _accessoryButton = [UIButton buttonWithType:UIButtonTypeCustom];
            [_accessoryButton setImage:[UIImage imageNamed:@"setting_cell_arrow"] forState:UIControlStateNormal];
            _accessoryButton.translatesAutoresizingMaskIntoConstraints = NO;
            [self addSubview:_accessoryButton];
            
            [NSLayoutConstraint activateConstraints:@[
                [_accessoryButton.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-12],
                [_accessoryButton.centerYAnchor constraintEqualToAnchor:self.centerYAnchor],
                [_accessoryButton.widthAnchor constraintEqualToConstant:20],
                [_accessoryButton.heightAnchor constraintEqualToConstant:20],
                
                [_valueField.leadingAnchor constraintEqualToAnchor:_titleLabel.trailingAnchor constant:16],
                [_valueField.trailingAnchor constraintEqualToAnchor:_accessoryButton.leadingAnchor constant:-8],
                [_valueField.centerYAnchor constraintEqualToAnchor:self.centerYAnchor]
            ]];
        } else {
            [NSLayoutConstraint activateConstraints:@[
                [_valueField.leadingAnchor constraintEqualToAnchor:_titleLabel.trailingAnchor constant:16],
                [_valueField.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-16],
                [_valueField.centerYAnchor constraintEqualToAnchor:self.centerYAnchor]
            ]];
        }
    }
    return self;
}

@end

@interface IDInfoEditorViewController () <UITextFieldDelegate>

@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) UIView *dimmingView;
@property (nonatomic, strong) UIButton *closeButton;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) NSMutableArray<IDInfoEditorTableCell *> *cells;
@property (nonatomic, strong) UIButton *confirmButton;
@property (nonatomic, strong) NSArray<NSDictionary *> *infoItems;
@property (nonatomic, copy) IDInfoEditorCompletionBlock completionBlock;
@property (nonatomic, assign) CGFloat keyboardHeight;
@property (nonatomic, assign) CGFloat originalContainerBottom;
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) NSLayoutConstraint *containerBottomConstraint;
@property (nonatomic, weak) UITextField *activeTextField;

@end

@implementation IDInfoEditorViewController

#pragma mark - Lifecycle

- (instancetype)initWithInfoItems:(NSArray<NSDictionary *> *)items completion:(IDInfoEditorCompletionBlock)completion {
    self = [super initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
    if (self) {
        _infoItems = [items copy];
        _completionBlock = [completion copy];
        _cells = [NSMutableArray array];
        _keyboardHeight = 0;
        [self setupUI];
        [self setupKeyboardNotifications];
    }
    return self;
}

- (void)dealloc {
    [self removeKeyboardNotifications];
}

#pragma mark - Keyboard Handling

- (void)setupKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
}

- (void)removeKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                    name:UIKeyboardWillShowNotification
                                                  object:nil];
    
    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                    name:UIKeyboardWillHideNotification
                                                  object:nil];
}

- (void)keyboardWillShow:(NSNotification *)notification {
    // 获取键盘尺寸
    CGRect keyboardRect = [notification.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    _keyboardHeight = keyboardRect.size.height;
    
    // 动画持续时间
    NSTimeInterval duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    
    // 如果没有记录原始位置，记录当前位置
    if (_originalContainerBottom == 0) {
        _originalContainerBottom = self.containerView.frame.origin.y + self.containerView.frame.size.height;
    }
    
    // 确保当前焦点输入框不被键盘遮挡
    if (_activeTextField) {
        CGRect textFieldFrame = [_activeTextField convertRect:_activeTextField.bounds toView:self];
        CGFloat textFieldBottom = textFieldFrame.origin.y + textFieldFrame.size.height;
        CGFloat keyboardTop = self.frame.size.height - _keyboardHeight;
        
        CGFloat offset = 0;
        if (textFieldBottom > keyboardTop) {
            // 输入框被键盘遮挡，需要上移容器
            offset = textFieldBottom - keyboardTop + 20; // 额外留出20pt空间
        }
        
        // 更新容器位置
        [UIView animateWithDuration:duration animations:^{
            self.containerBottomConstraint.constant = -offset;
            [self layoutIfNeeded];
        }];
    }
}

- (void)keyboardWillHide:(NSNotification *)notification {
    // 恢复容器原始位置
    NSTimeInterval duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    
    [UIView animateWithDuration:duration animations:^{
        self.containerBottomConstraint.constant = 0;
        [self layoutIfNeeded];
    }];
    
    _keyboardHeight = 0;
}

#pragma mark - UITextFieldDelegate

- (void)textFieldDidBeginEditing:(UITextField *)textField {
    _activeTextField = textField;
}

- (void)textFieldDidEndEditing:(UITextField *)textField {
    _activeTextField = nil;
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    // 查找下一个输入框
    BOOL foundCurrent = NO;
    for (IDInfoEditorTableCell *cell in self.cells) {
        if (foundCurrent && cell.valueField.enabled) {
            [cell.valueField becomeFirstResponder];
            return NO;
        }
        
        if (cell.valueField == textField) {
            foundCurrent = YES;
        }
    }
    
    // 没有下一个输入框，收起键盘
    [textField resignFirstResponder];
    return YES;
}

#pragma mark - Private Helpers

// 计算容器高度（包含安全区域调整）
- (CGFloat)calculatedContainerHeight {
    UIWindow *window = UIApplication.sharedApplication.windows.firstObject;
    CGFloat bottomSafeAreaInset = window.safeAreaInsets.bottom;
    
    // 基础高度 = 标题(70) + 每行(60 * 行数) + 确认按钮(50) + 顶部底部间距(120) + 行间距((行数-1) * 16)
    NSInteger rowCount = self.infoItems.count;
    CGFloat baseHeight = 70 + (60 * rowCount) + 50 + 120 + ((rowCount-1) * 16);
    
    return baseHeight + bottomSafeAreaInset;
}

#pragma mark - UI Setup

- (void)setupUI {
    // 半透明背景
    self.dimmingView = [[UIView alloc] initWithFrame:self.bounds];
    self.dimmingView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.5];
    self.dimmingView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self addSubview:self.dimmingView];
    
    // 新增：点击背景隐藏键盘（不关闭弹窗）
    UITapGestureRecognizer *tapBackgroundToDismissKeyboard = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissKeyboard)];
    tapBackgroundToDismissKeyboard.cancelsTouchesInView = NO;
    [self.dimmingView addGestureRecognizer:tapBackgroundToDismissKeyboard];
    
    // 容器视图 - 底部对齐
    CGFloat adjustedHeight = [self calculatedContainerHeight];
    
    self.containerView = [[UIView alloc] init];
    self.containerView.layer.cornerRadius = 20.0;
    // 只圆角顶部
    self.containerView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    self.containerView.layer.masksToBounds = YES;
    self.containerView.backgroundColor = [UIColor clearColor];
    self.containerView.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.containerView];
    
    // 更新布局约束 - 底部对齐，考虑安全区域
    [NSLayoutConstraint activateConstraints:@[
        [self.containerView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor],
        [self.containerView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor],
    ]];
    
    // 使用约束变量来方便键盘弹出时调整
    self.containerBottomConstraint = [self.containerView.bottomAnchor constraintEqualToAnchor:self.bottomAnchor];
    self.containerBottomConstraint.active = YES;
    
    NSLayoutConstraint *heightConstraint = [self.containerView.heightAnchor constraintEqualToConstant:adjustedHeight];
    heightConstraint.active = YES;
    
    // 背景图 - 复用PhotoSourcePickerView的背景图
    UIImageView *bgImageView = [[UIImageView alloc] init];
    bgImageView.image = [UIImage imageNamed:@"photo_source_picker_bg"];
    bgImageView.contentMode = UIViewContentModeScaleToFill;
    bgImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.containerView addSubview:bgImageView];
    
    [NSLayoutConstraint activateConstraints:@[
        [bgImageView.topAnchor constraintEqualToAnchor:self.containerView.topAnchor],
        [bgImageView.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor],
        [bgImageView.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor],
        [bgImageView.bottomAnchor constraintEqualToAnchor:self.containerView.bottomAnchor]
    ]];
    
    // 标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"Confirm Information";
    self.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:18];
    self.titleLabel.textColor = [UIColor whiteColor];
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.containerView addSubview:self.titleLabel];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.containerView.topAnchor constant:11],
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor constant:30]
    ]];
    
    // 关闭按钮 - 复用PhotoSourcePickerView的关闭按钮
    self.closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.closeButton setImage:[UIImage imageNamed:@"photo_source_picker_close"] forState:UIControlStateNormal];
    self.closeButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.containerView addSubview:self.closeButton];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.closeButton.topAnchor constraintEqualToAnchor:self.containerView.topAnchor constant:20],
        [self.closeButton.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor constant:-20],
        [self.closeButton.widthAnchor constraintEqualToConstant:30],
        [self.closeButton.heightAnchor constraintEqualToConstant:30]
    ]];
    
    // 添加滚动视图以便在键盘显示时可以滚动查看内容
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    self.scrollView.showsVerticalScrollIndicator = YES;
    self.scrollView.showsHorizontalScrollIndicator = NO;
    self.scrollView.alwaysBounceVertical = YES;
    [self.containerView addSubview:self.scrollView];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.scrollView.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor constant:20],
        [self.scrollView.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor],
        [self.scrollView.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor],
        [self.scrollView.bottomAnchor constraintEqualToAnchor:self.containerView.bottomAnchor]
    ]];
    
    // 内容视图包含所有单元格
    self.contentView = [[UIView alloc] init];
    self.contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.scrollView addSubview:self.contentView];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.contentView.topAnchor constraintEqualToAnchor:self.scrollView.topAnchor],
        [self.contentView.leadingAnchor constraintEqualToAnchor:self.scrollView.leadingAnchor],
        [self.contentView.trailingAnchor constraintEqualToAnchor:self.scrollView.trailingAnchor],
        [self.contentView.bottomAnchor constraintEqualToAnchor:self.scrollView.bottomAnchor],
        [self.contentView.widthAnchor constraintEqualToAnchor:self.scrollView.widthAnchor]
    ]];
    
    // 创建身份信息编辑单元格
    [self createInfoCells];
    
    // 确认按钮
    self.confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.confirmButton setTitle:@"Confirm" forState:UIControlStateNormal];
    [self.confirmButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.confirmButton.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:20];
    self.confirmButton.backgroundColor = [UIColor clearColor];
    [self.confirmButton setBackgroundImage:[UIImage imageNamed:@"face_guide_go_btn_bg"] forState:UIControlStateNormal];
    self.confirmButton.layer.cornerRadius = 25;
    self.confirmButton.layer.masksToBounds = YES;
    self.confirmButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.confirmButton addTarget:self action:@selector(confirmButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.confirmButton];
    
    // 获取最后一个单元格作为参考位置
    UIView *lastCell = self.cells.lastObject;
    
    [NSLayoutConstraint activateConstraints:@[
        [self.confirmButton.topAnchor constraintEqualToAnchor:lastCell.bottomAnchor constant:30],
        [self.confirmButton.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:40],
        [self.confirmButton.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-40],
        [self.confirmButton.heightAnchor constraintEqualToConstant:50],
        [self.confirmButton.bottomAnchor constraintEqualToAnchor:self.contentView.bottomAnchor constant:-40]
    ]];
    
    // 初始状态：透明度为0，位置在屏幕底部以外
    self.alpha = 0;
    self.containerView.transform = CGAffineTransformMakeTranslation(0, [self calculatedContainerHeight]);
    
    // 添加点击空白处隐藏键盘的手势
    UITapGestureRecognizer *tapToDismissKeyboard = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissKeyboard)];
    tapToDismissKeyboard.cancelsTouchesInView = NO; // 允许点击事件继续传递
    [self.scrollView addGestureRecognizer:tapToDismissKeyboard];
}

- (void)dismissKeyboard {
    [self endEditing:YES];
}

- (void)createInfoCells {
    UIView *previousCell = nil;
    CGFloat margin = 16;
    
    // 遍历信息项创建单元格
    for (int i = 0; i < self.infoItems.count; i++) {
        NSDictionary *item = self.infoItems[i];
        NSString *title = item[@"shapes"] ?: @"";
        NSString *value = item[@"daysewfwf"] ?: @"";
        NSString *key = item[@"modest"] ?: @"";

        // 添加调试日志
        NSLog(@"🔍 [字段%d] title:'%@' value:'%@' key:'%@'", i, title, value, key);

        BOOL isDateField = [title containsString:@"irth"] || [title containsString:@"ate"]; // 检测是否包含"birth"或"date"
        BOOL isNumberField = [title containsString:@"ID"] || [title containsString:@"身份证"]; // 检测身份证相关字段

        // 检查是否是姓名字段（根据API文档，excitedbecause对应姓名）
        BOOL isNameField = [key isEqualToString:@"excitedbecause"] || [title containsString:@"name"] || [title containsString:@"Name"];

        // 添加字段类型调试日志
        NSLog(@"🔍 [字段%d] isDateField:%@ isNumberField:%@ isNameField:%@ enabled:%@",
              i, isDateField ? @"YES" : @"NO", isNumberField ? @"YES" : @"NO", isNameField ? @"YES" : @"NO", !isDateField ? @"YES" : @"NO");
        
        IDInfoEditorTableCell *cell = [[IDInfoEditorTableCell alloc] initWithTitle:title value:value isDateField:isDateField isNumberField:isNumberField];

        // 为姓名字段设置合适的键盘类型
        if (isNameField) {
            cell.valueField.keyboardType = UIKeyboardTypeDefault;
            cell.valueField.autocapitalizationType = UITextAutocapitalizationTypeWords; // 单词首字母大写
        }
        cell.translatesAutoresizingMaskIntoConstraints = NO;
        [self.contentView addSubview:cell];
        [self.cells addObject:cell];
        
        // 设置委托以响应键盘事件
        cell.valueField.delegate = self;

        // 确保非日期字段都可以编辑（特别是姓名字段）
        if (!isDateField) {
            cell.valueField.enabled = YES;
            NSLog(@"✅ [字段%d] 确保可编辑 title:'%@' enabled:%@", i, title, cell.valueField.enabled ? @"YES" : @"NO");
        }
        
        // 设置位置约束
        if (previousCell) {
            [NSLayoutConstraint activateConstraints:@[
                [cell.topAnchor constraintEqualToAnchor:previousCell.bottomAnchor constant:margin],
                [cell.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:24],
                [cell.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-24],
                [cell.heightAnchor constraintEqualToConstant:60]
            ]];
        } else {
            [NSLayoutConstraint activateConstraints:@[
                [cell.topAnchor constraintEqualToAnchor:self.contentView.topAnchor],
                [cell.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:24],
                [cell.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-24],
                [cell.heightAnchor constraintEqualToConstant:60]
            ]];
        }
        
        // 确保所有单元格都可以交互
        cell.userInteractionEnabled = YES;

        // 如果是日期字段，添加点击处理
        if (isDateField) {
            [cell.accessoryButton addTarget:self action:@selector(dateFieldTapped:) forControlEvents:UIControlEventTouchUpInside];

            // 单元格本身也可以点击
            UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(cellTapped:)];
            [cell addGestureRecognizer:tapGesture];
        } else {
            // 非日期字段：点击整行即可开始编辑
            UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(editableCellTapped:)];
            tapGesture.cancelsTouchesInView = NO;
            [cell addGestureRecognizer:tapGesture];
        }
        
        previousCell = cell;
    }
}

#pragma mark - Actions

- (void)confirmButtonTapped {
    // 先确保键盘隐藏
    [self endEditing:YES];

    if (self.completionBlock) {
        // 收集编辑后的信息
        NSMutableDictionary *editedInfo = [NSMutableDictionary dictionary];

        for (int i = 0; i < self.infoItems.count; i++) {
            NSDictionary *item = self.infoItems[i];
            NSString *key = item[@"modest"] ?: [NSString stringWithFormat:@"field%d", i];

            IDInfoEditorTableCell *cell = self.cells[i];
            NSString *value = cell.valueField.text ?: @"";

            // 检查是否为日期字段，并且有API格式的日期
            if (cell.isDateField) {
                NSString *apiFormattedDate = objc_getAssociatedObject(cell, "apiFormattedDate");
                if (apiFormattedDate) {
                    // 使用API格式的日期
                    value = apiFormattedDate;
                    NSLog(@"Using API formatted date for submission: %@", value);
                }
            }

            editedInfo[key] = value;
        }

        // 不再禁用按钮，允许用户随时点击
        // 成功就走收起流程，失败就走HUD打印，不需要手动限制用户的点击交互

        // 将结果交给外部处理，外部决定何时关闭
        self.completionBlock(editedInfo, NO);
    }
}

- (void)closeButtonTapped {
    // 先确保键盘隐藏
    [self endEditing:YES];
    
    if (self.completionBlock) {
        self.completionBlock(nil, YES);
    }
    [self dismissAnimated:YES];
}

- (void)dimmingViewTapped {
    // 先确保键盘隐藏
    [self endEditing:YES];
    
    if (self.completionBlock) {
        self.completionBlock(nil, YES);
    }
    [self dismissAnimated:YES];
}

- (void)dateFieldTapped:(UIButton *)button {
    // 找到对应的单元格
    for (IDInfoEditorTableCell *cell in self.cells) {
        if (cell.accessoryButton == button) {
            // 调用自定义日期选择器
            [self presentDatePickerForCell:cell];
            break;
        }
    }
}

- (void)cellTapped:(UITapGestureRecognizer *)gesture {
    IDInfoEditorTableCell *cell = (IDInfoEditorTableCell *)gesture.view;
    if (cell.isDateField) {
        // 调用自定义日期选择器
        [self presentDatePickerForCell:cell];
    }
}

- (void)editableCellTapped:(UITapGestureRecognizer *)gesture {
    IDInfoEditorTableCell *cell = (IDInfoEditorTableCell *)gesture.view;
    if (cell.valueField.enabled) {
        [cell.valueField becomeFirstResponder];
    }
}

#pragma mark - Show/Dismiss

- (void)showInView:(UIView *)parentView animated:(BOOL)animated {
    NSLog(@"[IDInfoEditor] showInView called, parentView=%@, superview before=%@", parentView, self.superview);

    // 将自身添加到父视图
    [parentView addSubview:self];

    NSLog(@"[IDInfoEditor] Added to parentView, superview after=%@", self.superview);

    // 设置层级，确保显示在内容之上，但低于全局提示（如 HUD）
    self.layer.zPosition = 1000; // 降低层级，让HUD能够显示在上方
    
    if (animated) {
        [UIView animateWithDuration:0.3 
                          delay:0.0 
                        options:UIViewAnimationOptionCurveEaseOut 
                     animations:^{
            self.alpha = 1.0;
            self.containerView.transform = CGAffineTransformIdentity;
        } completion:nil];
    } else {
        self.alpha = 1.0;
        self.containerView.transform = CGAffineTransformIdentity;
    }
}

- (void)dismissAnimated:(BOOL)animated {
    NSLog(@"[IDInfoEditor] dismissAnimated called with animated=%@", animated ? @"YES" : @"NO");

    // 先确保键盘隐藏
    [self endEditing:YES];

    if (animated) {
        // 使用帮助方法获取高度
        CGFloat adjustedHeight = [self calculatedContainerHeight];

        NSLog(@"[IDInfoEditor] Starting dismiss animation, adjustedHeight=%.2f", adjustedHeight);

        [UIView animateWithDuration:0.3
                          delay:0.0
                        options:UIViewAnimationOptionCurveEaseIn
                     animations:^{
            self.alpha = 0;
            self.containerView.transform = CGAffineTransformMakeTranslation(0, adjustedHeight);
        } completion:^(BOOL finished) {
            NSLog(@"[IDInfoEditor] Dismiss animation completed, finished=%@", finished ? @"YES" : @"NO");
            NSLog(@"[IDInfoEditor] About to removeFromSuperview, superview=%@", self.superview);
            [self removeFromSuperview];
            NSLog(@"[IDInfoEditor] removeFromSuperview called, superview after=%@", self.superview);
        }];
    } else {
        NSLog(@"[IDInfoEditor] Removing from superview immediately");
        [self removeFromSuperview];
    }
}

#pragma mark - DatePicker

- (void)presentDatePickerForCell:(IDInfoEditorTableCell *)cell {
    DatePickerViewController *picker = [[DatePickerViewController alloc] initWithInitialDateString:cell.valueField.text completion:^(NSString * _Nullable selectedDate, BOOL isCancelled) {
        if (!isCancelled && selectedDate) {
            // 日期已经在DatePickerViewController中被转换为YYYY/MM/DD格式
            cell.valueField.text = selectedDate;
            
            // 检查是否需要转换为API所需的格式（如DD/MM/YYYY）
            // 这里我们假设API需要DD/MM/YYYY格式，根据实际情况可能需要调整
            NSArray *parts = [selectedDate componentsSeparatedByString:@"-"];
            if (parts.count == 3) {
                NSString *year = parts[0];
                NSString *month = parts[1];
                NSString *day = parts[2];
                
                // 转换为API所需的格式（这里假设为DD/MM/YYYY）
                NSString *apiFormattedDate = [NSString stringWithFormat:@"%@-%@-%@", day, month, year];
                
                // 将转换后的格式保存到一个隐藏属性中，用于API提交
                // 这里我们使用关联对象来存储，或者可以考虑扩展IDInfoEditorTableCell类添加一个属性
                objc_setAssociatedObject(cell, "apiFormattedDate", apiFormattedDate, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
                
                NSLog(@"Date for display: %@, Date for API: %@", selectedDate, apiFormattedDate);
            }
        }
    }];
    UIWindow *keyWindow = UIApplication.sharedApplication.windows.firstObject;
    [picker showInView:keyWindow ?: self animated:YES];
}

@end 


