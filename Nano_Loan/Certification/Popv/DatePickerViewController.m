#import "DatePickerViewController.h"

#define SCREEN_WIDTH [UIScreen mainScreen].bounds.size.width
#define SCREEN_HEIGHT [UIScreen mainScreen].bounds.size.height

@interface DatePickerViewController () <UIPickerViewDelegate, UIPickerViewDataSource>

@property (nonatomic, strong) UIView *dimmingView;
@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *closeButton;
@property (nonatomic, strong) UIPickerView *pickerView;
@property (nonatomic, strong) UIButton *confirmButton;

@property (nonatomic, copy) NSArray<NSString *> *days;
@property (nonatomic, copy) NSArray<NSString *> *months;
@property (nonatomic, copy) NSArray<NSString *> *years;

@property (nonatomic, copy) DatePickerCompletionBlock completionBlock;
@property (nonatomic, assign) CGFloat containerHeight;

@end

@implementation DatePickerViewController

- (instancetype)initWithInitialDateString:(NSString *)dateString completion:(DatePickerCompletionBlock)completion {
    self = [super initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
    if (self) {
        _completionBlock = [completion copy];
        [self buildDataSource];
        [self setupUI];
        [self preselectDate:dateString];
    }
    return self;
}

#pragma mark - DataSource

- (void)buildDataSource {
    NSMutableArray *dArr = [NSMutableArray array];
    NSArray *suffix = @[ @"st", @"nd", @"rd" ];
    for (NSInteger i = 1; i <= 31; i++) {
        NSString *suf;
        if (i % 10 == 1 && i != 11) suf = suffix[0];
        else if (i % 10 == 2 && i != 12) suf = suffix[1];
        else if (i % 10 == 3 && i != 13) suf = suffix[2];
        else suf = @"th";
        [dArr addObject:[NSString stringWithFormat:@"%ld%@", (long)i, suf]];
    }
    _days = dArr;
    _months = @[ @"January", @"February", @"March", @"April", @"May", @"June", @"July", @"August", @"September", @"October", @"November", @"December" ];
    NSInteger currentYear = [NSCalendar.currentCalendar component:NSCalendarUnitYear fromDate:NSDate.date];
    NSMutableArray *yArr = [NSMutableArray array];
    for (NSInteger y = currentYear; y >= 1900; y--) {
        [yArr addObject:[NSString stringWithFormat:@"%ld", (long)y]];
    }
    _years = yArr;
}

#pragma mark - UI

- (void)setupUI {
    self.dimmingView = [[UIView alloc] initWithFrame:self.bounds];
    self.dimmingView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.6];
    [self addSubview:self.dimmingView];
    // 不允许点击背景关闭弹窗
    
    UIWindow *win = UIApplication.sharedApplication.windows.firstObject;
    CGFloat bottomSafe = win ? win.safeAreaInsets.bottom : 0;
    CGFloat containerHeight = 420 + bottomSafe;
    self.containerHeight = containerHeight;
    self.containerView = [[UIView alloc] init];
    self.containerView.translatesAutoresizingMaskIntoConstraints = NO;
    self.containerView.layer.cornerRadius = 20;
    self.containerView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    self.containerView.layer.masksToBounds = YES;
    self.containerView.backgroundColor = UIColor.clearColor;
    [self addSubview:self.containerView];

    // AutoLayout: full width, bottom anchored
    [NSLayoutConstraint activateConstraints:@[
        [self.containerView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor],
        [self.containerView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor]
    ]];
    NSLayoutConstraint *bottomC = [self.containerView.bottomAnchor constraintEqualToAnchor:self.bottomAnchor];
    bottomC.active = YES;
    NSLayoutConstraint *heightC = [self.containerView.heightAnchor constraintEqualToConstant:containerHeight];
    heightC.active = YES;
    
    // 背景图与身份证确认弹窗一致
    UIImageView *bgImageView = [[UIImageView alloc] initWithFrame:self.containerView.bounds];
    bgImageView.image = [UIImage imageNamed:@"photo_source_picker_bg"];
    bgImageView.contentMode = UIViewContentModeScaleToFill;
    bgImageView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self.containerView addSubview:bgImageView];
    [self.containerView sendSubviewToBack:bgImageView];
    
    // Title
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"Date Of Birth";
    self.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:20];
    self.titleLabel.textColor = UIColor.whiteColor;
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.containerView addSubview:self.titleLabel];
    [NSLayoutConstraint activateConstraints:@[
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.containerView.topAnchor constant:-7],
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor constant:30]
    ]];
    
    // Close
    self.closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.closeButton setImage:[UIImage imageNamed:@"photo_source_picker_close"] forState:UIControlStateNormal];
    self.closeButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.closeButton addTarget:self action:@selector(closeTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.containerView addSubview:self.closeButton];
    [NSLayoutConstraint activateConstraints:@[
        [self.closeButton.topAnchor constraintEqualToAnchor:self.containerView.topAnchor constant:20],
        [self.closeButton.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor constant:-20],
        [self.closeButton.widthAnchor constraintEqualToConstant:30],
        [self.closeButton.heightAnchor constraintEqualToConstant:30]
    ]];
    
    // Picker
    self.pickerView = [[UIPickerView alloc] init];
    self.pickerView.translatesAutoresizingMaskIntoConstraints = NO;
    self.pickerView.delegate = self;
    self.pickerView.dataSource = self;
    [self.containerView addSubview:self.pickerView];
    
    // Confirm button
    self.confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.confirmButton setTitle:@"Ok" forState:UIControlStateNormal];
    self.confirmButton.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:20];
    [self.confirmButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    [self.confirmButton setBackgroundImage:[UIImage imageNamed:@"face_guide_go_btn_bg"] forState:UIControlStateNormal];
    self.confirmButton.layer.cornerRadius = 25;
    self.confirmButton.layer.masksToBounds = YES;
    self.confirmButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.confirmButton addTarget:self action:@selector(confirmTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.containerView addSubview:self.confirmButton];

    // Layout picker and confirm button
    [NSLayoutConstraint activateConstraints:@[
        [self.pickerView.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor constant:20],
        [self.pickerView.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor],
        [self.pickerView.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor],
        [self.pickerView.heightAnchor constraintEqualToConstant:220],

        [self.confirmButton.topAnchor constraintEqualToAnchor:self.pickerView.bottomAnchor constant:30],
        [self.confirmButton.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor constant:40],
        [self.confirmButton.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor constant:-40],
        [self.confirmButton.heightAnchor constraintEqualToConstant:50]
    ]];

    // bottom spacing to safe area
    [self.confirmButton.bottomAnchor constraintEqualToAnchor:self.containerView.bottomAnchor constant:-(40+bottomSafe)].active = YES;
}

#pragma mark - Preselect

- (void)preselectDate:(NSString *)dateString {
    // 如果没有日期字符串，直接返回
    if (dateString.length == 0) return;
    
    // 分割日期字符串，支持 / 和 - 分隔符
    NSArray *parts = [dateString componentsSeparatedByCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@"/-"]];
    if (parts.count != 3) return;
    
    NSInteger day, month, year;
    
    // 检查第一部分是否是年份（4位数字）
    if ([parts[0] length] == 4) {
        // 假设格式为 YYYY/MM/DD
        year = [parts[0] integerValue];
        month = [parts[1] integerValue];
        day = [parts[2] integerValue];
    } else {
        // 假设格式为 DD/MM/YYYY
        day = [parts[0] integerValue];
        month = [parts[1] integerValue];
        year = [parts[2] integerValue];
    }
    
    // 设置日期选择器的值
    if (day >= 1 && day <= 31) {
        [self.pickerView selectRow:day-1 inComponent:0 animated:NO];
    }
    
    if (month >= 1 && month <= 12) {
        [self.pickerView selectRow:month-1 inComponent:1 animated:NO];
    }
    
    NSInteger currentYear = [NSCalendar.currentCalendar component:NSCalendarUnitYear fromDate:NSDate.date];
    NSInteger index = currentYear - year;
    if (index >= 0 && index < self.years.count) {
        [self.pickerView selectRow:index inComponent:2 animated:NO];
    }
}

#pragma mark - UIPickerView

- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    return 3; // day month year
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    if (component == 0) return self.days.count;
    if (component == 1) return self.months.count;
    return self.years.count;
}

- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component {
    if (component == 0) return self.days[row];
    if (component == 1) return self.months[row];
    return self.years[row];
}

#pragma mark - Actions

- (void)confirmTapped {
    NSInteger dayIndex = [self.pickerView selectedRowInComponent:0];
    NSInteger monthIndex = [self.pickerView selectedRowInComponent:1];
    NSInteger yearIndex = [self.pickerView selectedRowInComponent:2];
    
    // 获取原始值
    NSString *dayStr = self.days[dayIndex];
    NSString *monthStr = self.months[monthIndex];
    NSString *yearStr = self.years[yearIndex];
    
    // 转换为数字
    NSInteger day = dayIndex + 1; // 因为数组索引从0开始，天数从1开始
    NSInteger month = monthIndex + 1; // 月份也是从1开始
    NSInteger year = [yearStr integerValue];
    
    // 格式化为身份证日期格式 YYYY-MM-DD（与服务器一致）
    NSString *formattedDate = [NSString stringWithFormat:@"%04ld-%02ld-%02ld", (long)year, (long)month, (long)day];
    
    // 保存原始英文格式用于调试
    NSString *englishFormat = [NSString stringWithFormat:@"%@ %@ %@", dayStr, monthStr, yearStr];
    NSLog(@"Date selected - English: %@, Formatted for ID: %@", englishFormat, formattedDate);
    
    if (self.completionBlock) {
        self.completionBlock(formattedDate, NO);
    }
    
    [self dismissAnimated:YES];
}

- (void)closeTapped {
    if (self.completionBlock) self.completionBlock(nil, YES);
    [self dismissAnimated:YES];
}

#pragma mark - Show/Dismiss

- (void)showInView:(UIView *)parentView animated:(BOOL)animated {
    [parentView addSubview:self];
    // 提高层级，确保覆盖身份证确认弹窗
    self.layer.zPosition = 1100;
    self.alpha = 0;
    self.containerView.transform = CGAffineTransformMakeTranslation(0, self.containerHeight);
    if (animated) {
        [UIView animateWithDuration:0.25 animations:^{
            self.alpha = 1;
            self.containerView.transform = CGAffineTransformIdentity;
        }];
    } else {
        self.alpha = 1;
        self.containerView.transform = CGAffineTransformIdentity;
    }
}

- (void)dismissAnimated:(BOOL)animated {
    if (animated) {
        [UIView animateWithDuration:0.25 animations:^{
            self.alpha = 0;
            self.containerView.transform = CGAffineTransformMakeTranslation(0, self.containerHeight);
        } completion:^(BOOL finished) {
            [self removeFromSuperview];
        }];
    } else {
        [self removeFromSuperview];
    }
}

@end 
