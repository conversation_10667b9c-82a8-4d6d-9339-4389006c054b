/// 通用选项选择弹窗
/// 参考 CustomAddressPickerViewController 设计，支持标题、选项列表、选中勾选、点击保存回调

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^OptionPickerResult)(NSInteger selectedIndex, NSString * _Nullable selectedTitle);

@interface OptionPickerViewController : UIViewController

/// 初始化
/// @param title  主标题
/// @param options 选项文本数组
/// @param selectedTitle 当前已选标题，可为空
/// @param result 保存按钮点击后的回调
- (instancetype)initWithTitle:(NSString *)title
                       options:(NSArray<NSString *> *)options
                selectedTitle:(nullable NSString *)selectedTitle
                     completion:(OptionPickerResult)result NS_DESIGNATED_INITIALIZER;

/// 禁用默认 init 方法
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END 