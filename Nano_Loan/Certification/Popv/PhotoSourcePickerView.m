//
//  PhotoSourcePickerView.m
//  Nano_Loan
//
//  Created for displaying custom camera/album selection UI.
//

#import "PhotoSourcePickerView.h"

#define SCREEN_WIDTH [UIScreen mainScreen].bounds.size.width
#define SCREEN_HEIGHT [UIScreen mainScreen].bounds.size.height
#define SCALE_375 (SCREEN_WIDTH / 375.0)

@interface PhotoSourcePickerView ()

@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) UIView *dimmingView;
@property (nonatomic, strong) UIButton *closeButton;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIImageView *idIllustrationView;
@property (nonatomic, strong) UIView *tipContainer;
@property (nonatomic, strong) UILabel *tipLabel;
@property (nonatomic, strong) UIButton *cameraButton;
@property (nonatomic, strong) UIButton *albumButton;
@property (nonatomic, assign) BOOL allowAlbum;
@property (nonatomic, copy) PhotoSourcePickerCompletionBlock completionBlock;

@end

@implementation PhotoSourcePickerView

#pragma mark - Lifecycle

- (instancetype)initWithAllowAlbum:(BOOL)allowAlbum completion:(PhotoSourcePickerCompletionBlock)completion {
    self = [super initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
    if (self) {
        _allowAlbum = allowAlbum;
        _completionBlock = [completion copy];
        [self setupUI];
    }
    return self;
}

#pragma mark - Private Helpers

// 计算容器高度（包含安全区域调整）
- (CGFloat)calculatedContainerHeight {
    UIWindow *window = UIApplication.sharedApplication.windows.firstObject;
    CGFloat bottomSafeAreaInset = window.safeAreaInsets.bottom;
    CGFloat baseHeight = 455 * SCALE_375;
    return baseHeight + bottomSafeAreaInset - 35; // 减少35pt的多余空间
}

#pragma mark - UI Setup

- (void)setupUI {
    // 半透明背景
    self.dimmingView = [[UIView alloc] initWithFrame:self.bounds];
    self.dimmingView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.5];
    self.dimmingView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self addSubview:self.dimmingView];
    
    // 为背景添加点击事件
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dimmingViewTapped)];
    [self.dimmingView addGestureRecognizer:tapGesture];
    
    // 容器视图 - 改为底部对齐
    CGFloat containerWidth = MIN(375 * SCALE_375, SCREEN_WIDTH - 40);
    CGFloat adjustedHeight = [self calculatedContainerHeight];
    
    self.containerView = [[UIView alloc] init];
    self.containerView.layer.cornerRadius = 20.0;
    // 只圆角顶部
    self.containerView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    self.containerView.layer.masksToBounds = YES;
    self.containerView.backgroundColor = [UIColor clearColor];
    self.containerView.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.containerView];
    
    // 更新布局约束 - 改为底部对齐，考虑安全区域
    [NSLayoutConstraint activateConstraints:@[
        [self.containerView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor],
        [self.containerView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor],
        [self.containerView.bottomAnchor constraintEqualToAnchor:self.bottomAnchor],
        [self.containerView.heightAnchor constraintEqualToConstant:adjustedHeight]
    ]];
    
    // 背景图
    UIImageView *bgImageView = [[UIImageView alloc] init];
    bgImageView.image = [UIImage imageNamed:@"photo_source_picker_bg"];
    bgImageView.contentMode = UIViewContentModeScaleToFill;
    bgImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.containerView addSubview:bgImageView];
    
    [NSLayoutConstraint activateConstraints:@[
        [bgImageView.topAnchor constraintEqualToAnchor:self.containerView.topAnchor],
        [bgImageView.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor],
        [bgImageView.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor],
        [bgImageView.bottomAnchor constraintEqualToAnchor:self.containerView.bottomAnchor]
    ]];
    
    // 标题 - 减小字体并调整位置
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"Upload ID Photo";
    self.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:20]; // 从22减小到20
    self.titleLabel.textColor = [UIColor whiteColor];
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.containerView addSubview:self.titleLabel];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.containerView.topAnchor constant:11], // 从24减小到18
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor constant:30] // 从24减小到20
    ]];
    
    // 关闭按钮 - 调整位置
    self.closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.closeButton setImage:[UIImage imageNamed:@"photo_source_picker_close"] forState:UIControlStateNormal];
    self.closeButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.containerView addSubview:self.closeButton];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.closeButton.topAnchor constraintEqualToAnchor:self.containerView.topAnchor constant:20], // 从20减小到16
        [self.closeButton.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor constant:-20], // 从20减小到16
        [self.closeButton.widthAnchor constraintEqualToConstant:30],
        [self.closeButton.heightAnchor constraintEqualToConstant:30]
    ]];
    
    // 身份证插图
    self.idIllustrationView = [[UIImageView alloc] init];
    self.idIllustrationView.image = [UIImage imageNamed:@"photo_source_picker_id_illustration"];
    self.idIllustrationView.contentMode = UIViewContentModeScaleAspectFit;
    self.idIllustrationView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.containerView addSubview:self.idIllustrationView];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.idIllustrationView.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor constant:30],
        [self.idIllustrationView.centerXAnchor constraintEqualToAnchor:self.containerView.centerXAnchor],
        [self.idIllustrationView.widthAnchor constraintEqualToConstant:240 * SCALE_375],
        [self.idIllustrationView.heightAnchor constraintEqualToConstant:160 * SCALE_375]
    ]];
    
    // 提示容器
    self.tipContainer = [[UIView alloc] init];
    self.tipContainer.backgroundColor = [UIColor colorWithRed:170/255.0 green:192/255.0 blue:255/255.0 alpha:0.6];
    self.tipContainer.layer.cornerRadius = 12;
    self.tipContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [self.containerView addSubview:self.tipContainer];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.tipContainer.topAnchor constraintEqualToAnchor:self.idIllustrationView.bottomAnchor constant:20],
        [self.tipContainer.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor constant:24],
        [self.tipContainer.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor constant:-24],
        [self.tipContainer.heightAnchor constraintEqualToConstant:80 * SCALE_375]
    ]];
    
    // 提示文本
    self.tipLabel = [[UILabel alloc] init];
    self.tipLabel.text = @"For Proper Verification, Upload An ID Photo In Good Condition. Please.";
    self.tipLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    self.tipLabel.textColor = [UIColor darkGrayColor];
    self.tipLabel.numberOfLines = 0;
    self.tipLabel.textAlignment = NSTextAlignmentCenter;
    self.tipLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.tipContainer addSubview:self.tipLabel];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.tipLabel.topAnchor constraintEqualToAnchor:self.tipContainer.topAnchor constant:12],
        [self.tipLabel.leadingAnchor constraintEqualToAnchor:self.tipContainer.leadingAnchor constant:12],
        [self.tipLabel.trailingAnchor constraintEqualToAnchor:self.tipContainer.trailingAnchor constant:-12],
        [self.tipLabel.bottomAnchor constraintEqualToAnchor:self.tipContainer.bottomAnchor constant:-12]
    ]];
    
    // 底部按钮容器 - 考虑安全区域
    UIView *buttonContainer = [[UIView alloc] init];
    buttonContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [self.containerView addSubview:buttonContainer];
    
    [NSLayoutConstraint activateConstraints:@[
        [buttonContainer.topAnchor constraintEqualToAnchor:self.tipContainer.bottomAnchor constant:30],
        [buttonContainer.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor constant:24],
        [buttonContainer.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor constant:-24],
        [buttonContainer.heightAnchor constraintEqualToConstant:60]
    ]];
    
    // 相机按钮
    self.cameraButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.cameraButton setTitle:@"Camera" forState:UIControlStateNormal];
    [self.cameraButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.cameraButton.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:18];
    self.cameraButton.backgroundColor = [UIColor clearColor];
    [self.cameraButton setBackgroundImage:[UIImage imageNamed:@"photo_source_picker_camera_btn_bg"] forState:UIControlStateNormal];
    self.cameraButton.layer.cornerRadius = 25;
    self.cameraButton.layer.masksToBounds = YES;
    self.cameraButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.cameraButton addTarget:self action:@selector(cameraButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [buttonContainer addSubview:self.cameraButton];
    
    // 相册按钮（根据allowAlbum决定是否显示）
    if (_allowAlbum) {
        // 分两栏布局
        [NSLayoutConstraint activateConstraints:@[
            [self.cameraButton.leadingAnchor constraintEqualToAnchor:buttonContainer.leadingAnchor],
            [self.cameraButton.topAnchor constraintEqualToAnchor:buttonContainer.topAnchor],
            [self.cameraButton.bottomAnchor constraintEqualToAnchor:buttonContainer.bottomAnchor],
            [self.cameraButton.widthAnchor constraintEqualToAnchor:buttonContainer.widthAnchor multiplier:0.47]
        ]];
        
        self.albumButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [self.albumButton setTitle:@"Album" forState:UIControlStateNormal];
        [self.albumButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        self.albumButton.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:18];
        self.albumButton.backgroundColor = [UIColor clearColor];
        [self.albumButton setBackgroundImage:[UIImage imageNamed:@"photo_source_picker_album_btn_bg"] forState:UIControlStateNormal];
        self.albumButton.layer.cornerRadius = 25;
        self.albumButton.layer.masksToBounds = YES;
        self.albumButton.translatesAutoresizingMaskIntoConstraints = NO;
        [self.albumButton addTarget:self action:@selector(albumButtonTapped) forControlEvents:UIControlEventTouchUpInside];
        [buttonContainer addSubview:self.albumButton];
        
        [NSLayoutConstraint activateConstraints:@[
            [self.albumButton.trailingAnchor constraintEqualToAnchor:buttonContainer.trailingAnchor],
            [self.albumButton.topAnchor constraintEqualToAnchor:buttonContainer.topAnchor],
            [self.albumButton.bottomAnchor constraintEqualToAnchor:buttonContainer.bottomAnchor],
            [self.albumButton.widthAnchor constraintEqualToAnchor:buttonContainer.widthAnchor multiplier:0.47]
        ]];
    } else {
        // 相机按钮居中
        [NSLayoutConstraint activateConstraints:@[
            [self.cameraButton.centerXAnchor constraintEqualToAnchor:buttonContainer.centerXAnchor],
            [self.cameraButton.topAnchor constraintEqualToAnchor:buttonContainer.topAnchor],
            [self.cameraButton.bottomAnchor constraintEqualToAnchor:buttonContainer.bottomAnchor],
            [self.cameraButton.widthAnchor constraintEqualToAnchor:buttonContainer.widthAnchor multiplier:0.8]
        ]];
    }
    
    // 初始状态：透明度为0，位置在屏幕底部以外
    self.alpha = 0;
    self.containerView.transform = CGAffineTransformMakeTranslation(0, [self calculatedContainerHeight]);
}

#pragma mark - Actions

- (void)cameraButtonTapped {
    if (self.completionBlock) {
        self.completionBlock(PhotoSourceTypeCamera);
    }
    [self dismissAnimated:YES];
}

- (void)albumButtonTapped {
    if (self.completionBlock) {
        self.completionBlock(PhotoSourceTypeAlbum);
    }
    [self dismissAnimated:YES];
}

- (void)closeButtonTapped {
    [self dismissAnimated:YES];
}

- (void)dimmingViewTapped {
    [self dismissAnimated:YES];
}

#pragma mark - Show/Dismiss

- (void)showInView:(UIView *)parentView animated:(BOOL)animated {
    [parentView addSubview:self];
    
    if (animated) {
        [UIView animateWithDuration:0.3 
                          delay:0.0 
                        options:UIViewAnimationOptionCurveEaseOut 
                     animations:^{
            self.alpha = 1.0;
            self.containerView.transform = CGAffineTransformIdentity;
        } completion:nil];
    } else {
        self.alpha = 1.0;
        self.containerView.transform = CGAffineTransformIdentity;
    }
}

- (void)dismissAnimated:(BOOL)animated {
    if (animated) {
        // 使用帮助方法获取高度
        CGFloat adjustedHeight = [self calculatedContainerHeight];
        
        [UIView animateWithDuration:0.3 
                          delay:0.0 
                        options:UIViewAnimationOptionCurveEaseIn 
                     animations:^{
            self.alpha = 0;
            self.containerView.transform = CGAffineTransformMakeTranslation(0, adjustedHeight);
        } completion:^(BOOL finished) {
            [self removeFromSuperview];
        }];
    } else {
        [self removeFromSuperview];
    }
}

@end 