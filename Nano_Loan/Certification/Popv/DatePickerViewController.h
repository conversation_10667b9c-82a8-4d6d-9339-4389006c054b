#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^DatePickerCompletionBlock)(NSString * _Nullable selectedDate, BOOL isCancelled);

@interface DatePickerViewController : UIView

- (instancetype)initWithInitialDateString:(nullable NSString *)dateString
                                 completion:(DatePickerCompletionBlock)completion;

- (void)showInView:(UIView *)parentView animated:(BOOL)animated;

@end

NS_ASSUME_NONNULL_END 