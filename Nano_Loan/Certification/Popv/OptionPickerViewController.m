//
//  OptionPickerViewController.m
//  Nano_Loan
//
//  Created by iOSDev on 2025/07/04.
//

#import "OptionPickerViewController.h"

#define SCREEN_WIDTH  [UIScreen mainScreen].bounds.size.width
#define SCREEN_HEIGHT [UIScreen mainScreen].bounds.size.height
#define SCALE_375 (SCREEN_WIDTH / 375.0)

@interface OptionPickerViewController () <UITableViewDelegate, UITableViewDataSource, UIGestureRecognizerDelegate>

@property (nonatomic, copy) NSString *popupTitle;
@property (nonatomic, copy) NSArray<NSString *> *options;
@property (nonatomic, copy) OptionPickerResult result;
@property (nonatomic, assign) NSInteger selectedIndex;

// UI
@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) UITableView *tableView;
@end

@implementation OptionPickerViewController

#pragma mark - Init

- (instancetype)initWithTitle:(NSString *)title
                       options:(NSArray<NSString *> *)options
                selectedTitle:(NSString *)selectedTitle
                     completion:(OptionPickerResult)result {
    self = [super init];
    if (self) {
        _popupTitle = [title copy] ?: @"Option";
        _options = [options copy] ?: @[];
        _result = [result copy];
        _selectedIndex = NSNotFound;
        if (selectedTitle.length) {
            NSUInteger idx = [_options indexOfObject:selectedTitle];
            if (idx != NSNotFound) _selectedIndex = idx;
        }
        self.modalPresentationStyle = UIModalPresentationOverFullScreen;
        self.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    }
    return self;
}

#pragma mark - LifeCycle

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor colorWithWhite:0 alpha:0.5];
    [self setupUI];
    // 初始状态 - 隐藏并位移到底部，避免闪烁
    self.view.alpha = 0;
    self.containerView.transform = CGAffineTransformMakeTranslation(0, self.containerView.bounds.size.height);
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    // 弹入动画
    [UIView animateWithDuration:0.05 animations:^{
        self.view.alpha = 1;
        self.containerView.transform = CGAffineTransformIdentity;
    }];
}

#pragma mark - UI
- (void)setupUI {
    CGFloat height = 455.0 * SCALE_375;
    self.containerView = [[UIView alloc] initWithFrame:CGRectMake(0, SCREEN_HEIGHT - height, SCREEN_WIDTH, height)];
    self.containerView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleTopMargin;
    self.containerView.layer.cornerRadius = 20;
    self.containerView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    self.containerView.clipsToBounds = YES;
    [self.view addSubview:self.containerView];
    // 背景图
    UIImageView *bg = [[UIImageView alloc] initWithFrame:self.containerView.bounds];
    bg.image = [UIImage imageNamed:@"photo_source_picker_bg"];
    bg.contentMode = UIViewContentModeScaleToFill;
    bg.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self.containerView addSubview:bg];

    // 顶部 header
    UIView *header = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 50)];
    header.autoresizingMask = UIViewAutoresizingFlexibleWidth;
    [self.containerView addSubview:header];

    UILabel *titleLbl = [[UILabel alloc] init];
    titleLbl.text = self.popupTitle;
    titleLbl.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:18];
    titleLbl.textColor = UIColor.whiteColor;
    titleLbl.translatesAutoresizingMaskIntoConstraints = NO;
    [header addSubview:titleLbl];
    [NSLayoutConstraint activateConstraints:@[
        [titleLbl.leadingAnchor constraintEqualToAnchor:header.leadingAnchor constant:30],
        [titleLbl.topAnchor constraintEqualToAnchor:header.topAnchor constant:11]
    ]];

    // close button
    UIButton *close = [UIButton buttonWithType:UIButtonTypeCustom];
    [close setImage:[UIImage imageNamed:@"photo_source_picker_close"] forState:UIControlStateNormal];
    close.translatesAutoresizingMaskIntoConstraints = NO;
    [close addTarget:self action:@selector(closeTapped) forControlEvents:UIControlEventTouchUpInside];
    [header addSubview:close];
    [NSLayoutConstraint activateConstraints:@[
        [close.trailingAnchor constraintEqualToAnchor:header.trailingAnchor constant:-22],
        [close.topAnchor constraintEqualToAnchor:header.topAnchor constant:22],
        [close.widthAnchor constraintEqualToConstant:30],
        [close.heightAnchor constraintEqualToConstant:30]
    ]];

    // tableView
    CGFloat segmentHeight = 50; // header same height
    CGFloat bottomBtnH = 70;
    CGFloat yPos = segmentHeight;
    CGFloat tblH = self.containerView.bounds.size.height - yPos - bottomBtnH;
    self.tableView = [[UITableView alloc] initWithFrame:CGRectMake(15, yPos, SCREEN_WIDTH - 30, tblH) style:UITableViewStylePlain];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.backgroundColor = UIColor.clearColor;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.rowHeight = 45;
    self.tableView.showsVerticalScrollIndicator = YES;
    self.tableView.showsHorizontalScrollIndicator = NO;
    self.tableView.alwaysBounceHorizontal = NO;
    self.tableView.directionalLockEnabled = YES;
    [self.containerView addSubview:self.tableView];

    // 保存按钮
    UIButton *save = [UIButton buttonWithType:UIButtonTypeCustom];
    save.frame = CGRectMake(40, self.containerView.bounds.size.height - bottomBtnH + (bottomBtnH-50)/2, SCREEN_WIDTH - 80, 50);
    [save setTitle:@"Ok" forState:UIControlStateNormal];
    [save setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    save.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:20];
    [save setBackgroundImage:[UIImage imageNamed:@"face_guide_go_btn_bg"] forState:UIControlStateNormal];
    save.layer.cornerRadius = 25;
    save.clipsToBounds = YES;
    [save addTarget:self action:@selector(saveTapped) forControlEvents:UIControlEventTouchUpInside];
    save.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleTopMargin;
    [self.containerView addSubview:save];

    // 背景点击手势
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(bgTapped:)];
    tap.cancelsTouchesInView = NO;
    tap.delegate = self;
    [self.view addGestureRecognizer:tap];
}

#pragma mark - Actions
- (void)closeTapped {
    [self dismissAnimated:YES];
}

- (void)saveTapped {
    if (self.result) {
        NSString *title = (self.selectedIndex != NSNotFound && self.selectedIndex < self.options.count) ? self.options[self.selectedIndex] : nil;
        self.result(self.selectedIndex, title);
    }
    [self dismissAnimated:YES];
}

- (void)bgTapped:(UITapGestureRecognizer *)ges {
    CGPoint loc = [ges locationInView:self.view];
    if (!CGRectContainsPoint(self.containerView.frame, loc)) {
        [self dismissAnimated:YES];
    }
}

- (void)dismissAnimated:(BOOL)animated {
    if (animated) {
        [UIView animateWithDuration:0.05 animations:^{
            self.view.alpha = 0;
            self.containerView.transform = CGAffineTransformMakeTranslation(0, self.containerView.bounds.size.height);
        } completion:^(BOOL finished) {
            [self dismissViewControllerAnimated:NO completion:nil];
        }];
    } else {
        [self dismissViewControllerAnimated:NO completion:nil];
    }
}

#pragma mark - Table
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section { return self.options.count; }

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString *cid = @"OptCell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cid];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cid];
        cell.backgroundColor = UIColor.clearColor;
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        cell.textLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightRegular];
        cell.textLabel.textColor = UIColor.blackColor;
    }
    cell.textLabel.text = self.options[indexPath.row];
    if (indexPath.row == self.selectedIndex) {
        // 显示勾
        UIImageView *check = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"Select_right"]];
        // 这里使用现有蓝色勾资源，需要开发替换为专用图片
        check.frame = CGRectMake(0, 0, 24, 24);
        cell.accessoryView = check;
        cell.layer.borderColor = [UIColor colorWithRed:61/255.0 green:78/255.0 blue:255/255.0 alpha:1].CGColor; // 蓝色边框
        cell.layer.borderWidth = 2;
        cell.layer.cornerRadius = 12;
    } else {
        cell.accessoryView = nil;
        cell.layer.borderWidth = 0;
    }
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    self.selectedIndex = indexPath.row;
    [tableView reloadData];
}

#pragma mark - Gesture delegate
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    CGPoint p = [touch locationInView:self.containerView];
    return !CGRectContainsPoint(self.containerView.bounds, p);
}

@end 
