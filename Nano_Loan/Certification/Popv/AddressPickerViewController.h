//
//  AddressPickerViewController.h
//  Nano_Loan
//
//  用于省市区三级联动选择。
//

#import <UIKit/UIKit.h>
@class AddressNode;

NS_ASSUME_NONNULL_BEGIN

typedef void(^AddressPickerResult)(AddressNode *province, AddressNode *city, AddressNode *district);

@interface AddressPickerViewController : UIViewController

/// 初始化方法，roots 为省级节点数组
- (instancetype)initWithAddressRoots:(NSArray<AddressNode *> *)roots
                           selection:(nullable AddressPickerResult)result;

@end

NS_ASSUME_NONNULL_END 