#import "HomeAwkwardModel.h"
#import "HomeBoastingModel.h"

@implementation HomeAwkwardModel

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    self = [super init];
    if (self) {
        if (![dict isKindOfClass:[NSDictionary class]]) return self;
        _mayonnaise = dict[@"mayonnaise"];
        NSArray *pickleArr = dict[@"pickle"];
        if ([pickleArr isKindOfClass:[NSArray class]]) {
            _pickle = pickleArr;
        }
        _pickles = dict[@"pickles"];
        NSMutableArray *boastingModels = [NSMutableArray array];
        NSArray *boastingArr = dict[@"boasting"];
        if ([boastingArr isKindOfClass:[NSArray class]]) {
            for (NSDictionary *item in boastingArr) {
                HomeBoastingModel *model = [[HomeBoastingModel alloc] initWithDictionary:item];
                if (model) [boastingModels addObject:model];
            }
        }
        _boasting = boastingModels.copy;
    }
    return self;
}

@end 