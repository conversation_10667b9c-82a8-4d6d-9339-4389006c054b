///
///  AddressNode.m
///  Nano_Loan
///
///  Created by iOSDev on 2025/6/25.
///

#import "AddressNode.h"

@implementation AddressNode

#pragma mark - Init

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    self = [super init];
    if (self) {
        // 解析 id 和 name
        id identifier = dict[@"darn"];
        if ([identifier respondsToSelector:@selector(integerValue)]) {
            _identifier = [identifier integerValue];
        } else {
            _identifier = 0;
        }
        NSString *name = dict[@"excitedbecause"];
        _name = [name isKindOfClass:[NSString class]] ? name : @"";
        // 解析子节点
        NSArray *rawChildren = dict[@"boasting"];
        if ([rawChildren isKindOfClass:[NSArray class]] && rawChildren.count > 0) {
            NSMutableArray *tmp = [NSMutableArray arrayWithCapacity:rawChildren.count];
            for (NSDictionary *subDict in rawChildren) {
                if ([subDict isKindOfClass:[NSDictionary class]]) {
                    AddressNode *child = [[AddressNode alloc] initWithDictionary:subDict];
                    [tmp addObject:child];
                }
            }
            _children = [tmp copy];
        }
    }
    return self;
}

#pragma mark - NSSecureCoding

+ (BOOL)supportsSecureCoding {
    return YES;
}

- (void)encodeWithCoder:(NSCoder *)coder {
    [coder encodeInteger:self.identifier forKey:@"identifier"];
    [coder encodeObject:self.name forKey:@"name"];
    [coder encodeObject:self.children forKey:@"children"];
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super init];
    if (self) {
        _identifier = [coder decodeIntegerForKey:@"identifier"];
        _name = [coder decodeObjectOfClass:[NSString class] forKey:@"name"] ?: @"";
        NSSet *allowedClasses = [NSSet setWithObjects:[NSArray class], [AddressNode class], nil];
        _children = [coder decodeObjectOfClasses:allowedClasses forKey:@"children"];
    }
    return self;
}

@end 