#import <Foundation/Foundation.h>
@class HomeProductModel;

NS_ASSUME_NONNULL_BEGIN

@interface HomeBoastingModel : NSObject
@property (nonatomic, copy) NSString *subject; ///< 元素类型
@property (nonatomic, strong) NSArray *darning; ///< 原始darning数组，可根据subject再解析

// 若subject为PRODUCT_LIST，则darningItems会解析为ProductModel数组
@property (nonatomic, strong, nullable) NSArray<HomeProductModel *> *productList;

- (instancetype)initWithDictionary:(NSDictionary *)dict;
@end

NS_ASSUME_NONNULL_END 