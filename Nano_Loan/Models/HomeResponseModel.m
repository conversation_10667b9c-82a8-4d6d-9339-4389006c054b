#import "HomeResponseModel.h"
#import "HomeAwkwardModel.h"

@implementation HomeResponseModel

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    self = [super init];
    if (self) {
        if (![dict isKindOfClass:[NSDictionary class]]) return self;
        _modest = [dict[@"modest"] description] ?: @"";
        _patted = [dict[@"patted"] description] ?: @"";
        NSDictionary *awkwardDict = dict[@"awkward"];
        if ([awkwardDict isKindOfClass:[NSDictionary class]]) {
            _awkward = [[HomeAwkwardModel alloc] initWithDictionary:awkwardDict];
        }
    }
    return self;
}

@end