#import "LoginInitModel.h"

@implementation LoginInitModel

/**
 通过接口返回字典初始化模型，提取关键信息到属性
 */
- (instancetype)initWithDictionary:(NSDictionary *)dict {
    self = [super init];
    if (self) {
        // 顶层字段
        _modest = [dict[@"modest"] integerValue]; // 首页进入判断
        _patted = [dict[@"patted"] isKindOfClass:[NSString class]] ? dict[@"patted"] : @""; // 状态描述
        NSDictionary *awkward = dict[@"awkward"];
        if ([awkward isKindOfClass:[NSDictionary class]]) {
            // 国家类型
            _roundwarily = [awkward[@"roundwarily"] integerValue];
            // 位置引导弹窗
            _everyonecheered = [awkward[@"everyonecheered"] integerValue];
            // 隐私协议链接
            _girlswere = [awkward[@"girlswere"] isKindOfClass:[NSString class]] ? awkward[@"girlswere"] : @"";
            // 手机区号和国旗logo
            NSDictionary *heardmrs = awkward[@"heardmrs"];
            if ([heardmrs isKindOfClass:[NSDictionary class]]) {
                _heardmrs_topmost = [NSString stringWithFormat:@"%@", heardmrs[@"topmost"] ?: @""];
                _heardmrs_swallow = [heardmrs[@"swallow"] isKindOfClass:[NSString class]] ? heardmrs[@"swallow"] : @"";
            }
            // Facebook相关参数
            NSDictionary *fearlessness = awkward[@"fearlessness"];
            if ([fearlessness isKindOfClass:[NSDictionary class]]) {
                _fb_eventhe = [fearlessness[@"eventhe"] isKindOfClass:[NSString class]] ? fearlessness[@"eventhe"] : @"";
                _fb_gotback = [NSString stringWithFormat:@"%@", fearlessness[@"gotback"] ?: @""];
                _fb_chicken = [fearlessness[@"chicken"] isKindOfClass:[NSString class]] ? fearlessness[@"chicken"] : @"";
                _fb_ginger = [fearlessness[@"ginger"] isKindOfClass:[NSString class]] ? fearlessness[@"ginger"] : @"";
            }
        }
    }
    return self;
}

+ (BOOL)supportsSecureCoding {
    return YES;
}

/**
 对模型属性进行编码，便于本地持久化
 */
- (void)encodeWithCoder:(NSCoder *)coder {
    [coder encodeInteger:self.modest forKey:@"modest"];
    [coder encodeObject:self.patted forKey:@"patted"];
    [coder encodeInteger:self.roundwarily forKey:@"roundwarily"];
    [coder encodeInteger:self.everyonecheered forKey:@"everyonecheered"];
    [coder encodeObject:self.girlswere forKey:@"girlswere"];
    [coder encodeObject:self.heardmrs_topmost forKey:@"heardmrs_topmost"];
    [coder encodeObject:self.heardmrs_swallow forKey:@"heardmrs_swallow"];
    [coder encodeObject:self.fb_eventhe forKey:@"fb_eventhe"];
    [coder encodeObject:self.fb_gotback forKey:@"fb_gotback"];
    [coder encodeObject:self.fb_chicken forKey:@"fb_chicken"];
    [coder encodeObject:self.fb_ginger forKey:@"fb_ginger"];
}

/**
 解码本地持久化数据，恢复模型属性
 */
- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super init];
    if (self) {
        _modest = [coder decodeIntegerForKey:@"modest"];
        _patted = [coder decodeObjectOfClass:[NSString class] forKey:@"patted"];
        _roundwarily = [coder decodeIntegerForKey:@"roundwarily"];
        _everyonecheered = [coder decodeIntegerForKey:@"everyonecheered"];
        _girlswere = [coder decodeObjectOfClass:[NSString class] forKey:@"girlswere"];
        _heardmrs_topmost = [coder decodeObjectOfClass:[NSString class] forKey:@"heardmrs_topmost"];
        _heardmrs_swallow = [coder decodeObjectOfClass:[NSString class] forKey:@"heardmrs_swallow"];
        _fb_eventhe = [coder decodeObjectOfClass:[NSString class] forKey:@"fb_eventhe"];
        _fb_gotback = [coder decodeObjectOfClass:[NSString class] forKey:@"fb_gotback"];
        _fb_chicken = [coder decodeObjectOfClass:[NSString class] forKey:@"fb_chicken"];
        _fb_ginger = [coder decodeObjectOfClass:[NSString class] forKey:@"fb_ginger"];
    }
    return self;
}

@end 