///
///  AddressNode.h
///  Nano_Loan
///
///  Created by iOSDev on 2025/6/25.
///

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 三级地址选择所用节点模型，支持递归嵌套，已实现 NSSecureCoding 便于本地缓存
@interface AddressNode : NSObject <NSSecureCoding>

/// 唯一标识，对应接口字段 "darn"
@property (nonatomic, assign) NSInteger identifier;
/// 名称，对应接口字段 "excitedbecause"
@property (nonatomic, copy) NSString *name;
/// 子节点数组 <AddressNode *>，对应接口字段 "boasting"
@property (nonatomic, copy, nullable) NSArray<AddressNode *> *children;

/// 使用接口字典递归初始化模型
- (instancetype)initWithDictionary:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END 