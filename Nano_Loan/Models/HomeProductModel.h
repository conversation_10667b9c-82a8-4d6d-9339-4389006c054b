#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 产品模型，字段只保留业务关键字段
@interface HomeProductModel : NSObject

@property (nonatomic, assign) NSInteger productId;  ///< 产品ID (darn)
@property (nonatomic, copy) NSString *name;         ///< 产品名称 (cando)
@property (nonatomic, copy) NSString *logoURL;      ///< 产品logo (tapestrywork)
@property (nonatomic, copy) NSString *applyText;    ///< 申请按钮文字 (embroidery)
@property (nonatomic, copy) NSString *amount;       ///< 产品金额 (disappointment)
@property (nonatomic, copy) NSString *amountDesc;   ///< 金额文案 (stitches)
@property (nonatomic, copy) NSString *term;         ///< 期限 (cover)
@property (nonatomic, copy) NSString *termDesc;     ///< 期限文案 (thecushion)
@property (nonatomic, copy) NSString *rate;         ///< 利率 (describing / bushes)
@property (nonatomic, copy) NSString *rateDesc;     ///< 利率文案 (hark)
@property (nonatomic, assign) NSInteger opening;    ///< 产品类型 1 API 2 H5
@property (nonatomic, copy) NSString *jumpURL;      ///< 跳转URL (toteach)
@property (nonatomic, strong) NSArray<NSString *> *tags;   ///< 产品标签数组 (fond)
@property (nonatomic, copy) NSString *desc;                ///< 产品描述 (somany)
@property (nonatomic, copy) NSString *buttonColor;         ///< 申请按钮颜色 (cove)
@property (nonatomic, copy) NSString *termLogoURL;         ///< 期限Logo (inastonishment)
@property (nonatomic, copy) NSString *rateLogoURL;         ///< 利率Logo (listened)
@property (nonatomic, assign) NSInteger status;            ///< 产品状态 (leads)

- (instancetype)initWithDictionary:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END 