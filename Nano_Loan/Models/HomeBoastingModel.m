#import "HomeBoastingModel.h"
#import "HomeProductModel.h"

@implementation HomeBoastingModel

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    self = [super init];
    if (self) {
        if (![dict isKindOfClass:[NSDictionary class]]) return self;
        _subject = [dict[@"subject"] description] ?: @"";
        NSArray *darningArr = dict[@"darning"];
        if ([darningArr isKindOfClass:[NSArray class]]) {
            _darning = darningArr;
        }
        // 如果是产品列表，则解析为产品模型
        if ([_subject containsString:@"PRODUCT"] || [_subject containsString:@"CARD"]) {
            NSMutableArray *products = [NSMutableArray array];
            for (NSDictionary *item in _darning) {
                if ([item isKindOfClass:[NSDictionary class]]) {
                    HomeProductModel *p = [[HomeProductModel alloc] initWithDictionary:item];
                    if (p) [products addObject:p];
                }
            }
            _productList = products.copy;
        }
    }
    return self;
}

@end 