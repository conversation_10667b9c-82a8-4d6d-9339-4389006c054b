#import <Foundation/Foundation.h>
@class HomeBoastingModel;

NS_ASSUME_NONNULL_BEGIN

@interface HomeAwkwardModel : NSObject

@property (nonatomic, strong) NSDictionary *mayonnaise; ///< 客服信息字典
@property (nonatomic, strong) NSArray<NSString *> *pickle; ///< 滚动消息
@property (nonatomic, strong) NSDictionary *pickles; ///< 协议信息
@property (nonatomic, strong) NSArray<HomeBoastingModel *> *boasting; ///< 首页元素数组

- (instancetype)initWithDictionary:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END 