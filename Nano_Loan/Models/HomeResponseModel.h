#import <Foundation/Foundation.h>
@class HomeAwkwardModel;

NS_ASSUME_NONNULL_BEGIN

/// 首页接口整体响应模型
@interface HomeResponseModel : NSObject

@property (nonatomic, copy) NSString *modest;      ///< 状态码字符串
@property (nonatomic, copy) NSString *patted;      ///< 服务器提示信息
@property (nonatomic, strong, nullable) HomeAwkwardModel *awkward; ///< 实际业务数据

- (instancetype)initWithDictionary:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END 