#import <Foundation/Foundation.h>

/**
 登录初始化数据模型，保存/Alicia/bigger接口返回的关键信息
 */
@interface LoginInitModel : NSObject <NSSecureCoding>

/// 是否允许进入首页（0=允许，其他=异常）
@property (nonatomic, assign) NSInteger modest;
/// 状态描述（如：success）
@property (nonatomic, copy) NSString *patted;

// awkward
/// 国家类型（1=印度(审核面)，2=菲律宾(用户面)，后续请求需带入）
@property (nonatomic, assign) NSInteger roundwarily;
/// 是否弹出位置引导（1=弹出，2=不弹）
@property (nonatomic, assign) NSInteger everyonecheered;
/// 隐私协议链接
@property (nonatomic, copy) NSString *girlswere;

// heardmrs
/// 手机区号（如：63）
@property (nonatomic, copy) NSString *heardmrs_topmost;
/// 国旗logo图片地址
@property (nonatomic, copy) NSString *heardmrs_swallow;

// fearlessness
/// Facebook URL Scheme（eventhe）
@property (nonatomic, copy) NSString *fb_eventhe;
/// Facebook AppID（gotback）
@property (nonatomic, copy) NSString *fb_gotback;
/// Facebook 显示名（chicken）
@property (nonatomic, copy) NSString *fb_chicken;
/// Facebook ClientToken（ginger）
@property (nonatomic, copy) NSString *fb_ginger;

/// 通过接口字典初始化模型
- (instancetype)initWithDictionary:(NSDictionary *)dict;

@end 