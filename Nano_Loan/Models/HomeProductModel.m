#import "HomeProductModel.h"

@implementation HomeProductModel

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    self = [super init];
    if (self) {
        if (![dict isKindOfClass:[NSDictionary class]]) return self;
        _productId = [dict[@"darn"] respondsToSelector:@selector(integerValue)] ? [dict[@"darn"] integerValue] : 0;
        _name = [dict[@"cando"] description] ?: @"";
        _logoURL = [dict[@"tapestrywork"] description] ?: @"";
        _applyText = [dict[@"embroidery"] description] ?: @"";
        _amount = [dict[@"disappointment"] description] ?: @"";
        _amountDesc = [dict[@"stitches"] description] ?: @"";
        _term = [dict[@"cover"] description] ?: @"";
        _termDesc = [dict[@"thecushion"] description] ?: @"";
//        _rate = ([dict[@"describing"] ?: dict[@"bushes"]) description] ?: @"";
//        _rateDesc = [dict[@"hark"] description] ?: @"";
//        _opening = [dict[@"opening"] respondsToSelector:@selector(integerValue)] ? [dict[@"opening"] integerValue] : 0;
//        _jumpURL = [dict[@"toteach"] description] ?: @"";
//        NSArray *tagsArr = dict[@"fond"];
//        if ([tagsArr isKindOfClass:[NSArray class]]) {
//            _tags = tagsArr;
//        }
//        _desc = [dict[@"somany"] description] ?: @"";
//        _buttonColor = [dict[@"cove"] description] ?: @"";
//        _termLogoURL = [dict[@"inastonishment"] description] ?: @"";
//        _rateLogoURL = [dict[@"listened"] description] ?: @"";
//        _status = [dict[@"leads"] respondsToSelector:@selector(integerValue)] ? [dict[@"leads"] integerValue] : 0;
    }
    return self;
}

@end 
