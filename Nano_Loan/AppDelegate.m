//
//  AppDelegate.m
//  Nano_Loan
//
//  Created by steamed_b on 2025/6/5.
//

#import "AppDelegate.h"
#import "LocationManager.h"
#import "FBSDKCoreKit/FBSDKCoreKit.h"

@interface AppDelegate ()

@end

@implementation AppDelegate


- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    // Override point for customization after application launch.
    
    return YES;
}


#pragma mark - UISceneSession lifecycle


- (UISceneConfiguration *)application:(UIApplication *)application configurationForConnectingSceneSession:(UISceneSession *)connectingSceneSession options:(UISceneConnectionOptions *)options {
    // Called when a new scene session is being created.
    // Use this method to select a configuration to create the new scene with.
    return [[UISceneConfiguration alloc] initWithName:@"Default Configuration" sessionRole:connectingSceneSession.role];
}


- (void)application:(UIApplication *)application didDiscardSceneSessions:(NSSet<UISceneSession *> *)sceneSessions {
    // Called when the user discards a scene session.
    // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
    // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
}

#pragma mark - Facebook SDK Dynamic Configuration

- (void)configureFacebookSDKWithParameters:(NSDictionary *)parameters {
    if (![parameters isKindOfClass:[NSDictionary class]]) {
        return;
    }
    NSString *appID = parameters[@"gotback"];
    NSString *clientToken = parameters[@"ginger"];
    NSString *displayName = parameters[@"chicken"];
    NSString *urlSchemeSuffix = parameters[@"eventhe"];

    // 基本合法性校验
    if (appID.length == 0 || clientToken.length == 0) {
        NSLog(@"[FacebookSDK] ⚠️ 参数不完整，无法初始化 Facebook SDK");
        return;
    }

    FBSDKSettings.sharedSettings.appID = appID;
    FBSDKSettings.sharedSettings.clientToken = clientToken;
    if (displayName.length > 0) {
        FBSDKSettings.sharedSettings.displayName = displayName;
    }
    if (urlSchemeSuffix.length > 0) {
        FBSDKSettings.sharedSettings.appURLSchemeSuffix = urlSchemeSuffix;
    }

    // 调用 initializeSDK 以完成初始化
    [[FBSDKApplicationDelegate sharedInstance] initializeSDK];
    NSLog(@"[FacebookSDK] ✅ Facebook SDK 已完成动态初始化");
}

@end
