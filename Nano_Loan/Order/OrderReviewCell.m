#import "OrderReviewCell.h"
#import <Masonry/Masonry.h>

@implementation OrderReviewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        // 背景图
        UIImage *bg = [[UIImage imageNamed:@"order_cell_review_bg"] resizableImageWithCapInsets:UIEdgeInsetsMake(0, 0, 0, 0) resizingMode:UIImageResizingModeStretch];
        UIImageView *bgView = [[UIImageView alloc] initWithImage:bg];
        bgView.frame = self.contentView.bounds;
        bgView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        [self.contentView addSubview:bgView];

        // 产品图标
        self.productIconView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"nav_back"]]; // 默认占位
        [self.contentView addSubview:self.productIconView];

        // 产品名称
        self.productNameLabel = [[UILabel alloc] init];
        self.productNameLabel.font = [UIFont systemFontOfSize:12];
        self.productNameLabel.textColor = [UIColor labelColor];
        [self.contentView addSubview:self.productNameLabel];

        // 文字标签
        self.titleLabel = [[UILabel alloc] init];
        self.titleLabel.font = [UIFont boldSystemFontOfSize:18];
        [self.contentView addSubview:self.titleLabel];

        self.hintLabel = [[UILabel alloc] init];
        self.hintLabel.font = [UIFont systemFontOfSize:13];
        self.hintLabel.textColor = [UIColor secondaryLabelColor];
        [self.contentView addSubview:self.hintLabel];

        // Loan amount title label
        self.loanAmountTitleLabel = [[UILabel alloc] init];
        self.loanAmountTitleLabel.font = [UIFont systemFontOfSize:12];
        self.loanAmountTitleLabel.textColor = [UIColor colorWithRed:0xA7/255.0 green:0xA7/255.0 blue:0xA7/255.0 alpha:1.0];
        [self.contentView addSubview:self.loanAmountTitleLabel];

        // 金额值标签
        UIFont *priceFont = [UIFont fontWithName:@"Verdana-BoldItalic" size:16];
        if (!priceFont) priceFont = [UIFont boldSystemFontOfSize:16];
        self.amountLabel = [[UILabel alloc] init];
        self.amountLabel.font = priceFont;
        self.amountLabel.textColor = [UIColor colorWithRed:0x29/255.0 green:0x29/255.0 blue:0x29/255.0 alpha:1.0];
        [self.contentView addSubview:self.amountLabel];

        // Loan date title label
        self.loanDateTitleLabel = [[UILabel alloc] init];
        self.loanDateTitleLabel.font = [UIFont systemFontOfSize:12];
        self.loanDateTitleLabel.textColor = [UIColor colorWithRed:0xA7/255.0 green:0xA7/255.0 blue:0xA7/255.0 alpha:1.0];
        [self.contentView addSubview:self.loanDateTitleLabel];

        // Loan date value label
        self.loanDateValueLabel = [[UILabel alloc] init];
        self.loanDateValueLabel.font = priceFont;
        self.loanDateValueLabel.textColor = [UIColor colorWithRed:0x29/255.0 green:0x29/255.0 blue:0x29/255.0 alpha:1.0];
        [self.contentView addSubview:self.loanDateValueLabel];

        // Masonry constraints
        // 1. 图标
        [self.productIconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentView).offset(21);
            make.top.equalTo(self.contentView).offset(12);
            make.width.height.mas_equalTo(17);
        }];

        // 2. 产品名称
        [self.productNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.productIconView.mas_right).offset(6);
            make.centerY.equalTo(self.productIconView);
            make.right.lessThanOrEqualTo(self.contentView).offset(-14);
        }];

        // 3. Loan amount title
        [self.loanAmountTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.productNameLabel.mas_left);
            make.top.equalTo(self.productNameLabel.mas_bottom).offset(19);
            make.height.mas_equalTo(15);
        }];

        // 4. Amount value
        [self.amountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.loanAmountTitleLabel.mas_left);
            make.top.equalTo(self.loanAmountTitleLabel.mas_bottom).offset(6);
            make.height.mas_equalTo(21);
        }];

        // 5. Loan date title（与 loanAmount 同一行，右移 5pt）
        [self.loanDateTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.loanAmountTitleLabel.mas_left).offset(5);
            make.centerY.equalTo(self.loanAmountTitleLabel);
            make.height.mas_equalTo(15);
        }];

        // 6. Loan date value 与 amount 同行
        [self.loanDateValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.loanDateTitleLabel.mas_left);
            make.centerY.equalTo(self.amountLabel);
            make.height.mas_equalTo(21);
        }];

        // 7. 状态及提示文案放在金额区域下方 8pt
        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentView).offset(14);
            make.top.equalTo(self.amountLabel.mas_bottom).offset(8);
            make.right.equalTo(self.contentView).offset(-14);
        }];

        [self.hintLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentView).offset(14);
            make.top.equalTo(self.titleLabel.mas_bottom).offset(6);
            make.right.equalTo(self.contentView).offset(-14);
            make.bottom.equalTo(self.contentView).offset(-0);
        }];
    }
    return self;
}

@end 
