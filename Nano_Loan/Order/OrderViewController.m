/*
订单页面功能清单：
- [x] 自定义左上角标题
- [x] 横向可滚动状态选择器
- [x] 订单列表（UITableView）
- [x] 3种不同高度的Cell
- [x] 订单金额和状态标识
- [x] 假数据填充
- [x] 适配安全区和TabBar
*/
#import "OrderViewController.h"
#import <UIKit/UIKit.h>
#import "NetworkManager.h"
#import "HUD.h"
#import "H5WebViewController.h"
#import <Masonry/Masonry.h>
#import "UIColor+Hex.h"
#import "OrderRegularCell.h"
#import "OrderReviewCell.h"
#import <SDWebImage/UIImageView+WebCache.h>
#import "CustomTabBarController.h"   // 用于切换 Tab 到首页
#import "CustomTabBar.h"            // 修复前向声明错误
#import "HomeViewController.h"       // 调用首页产品准入方法
#import "ProductsCertificationViewController.h" // 用于原生路由跳转

#pragma mark - 订单状态模型
@interface OrderStatus : NSObject
@property (nonatomic, copy) NSString *title;
@end
@implementation OrderStatus
@end

#pragma mark - 订单数据模型
@interface OrderModel : NSObject
@property (nonatomic, copy) NSString *orderId;
@property (nonatomic, copy) NSString *amount;
@property (nonatomic, copy) NSString *status;
@property (nonatomic, assign) NSInteger cellType; // 0:140, 1:150, 2:180
@property (nonatomic, copy) NSString *jumpURL; // shegot
@property (nonatomic, copy) NSString *loanDate;
@property (nonatomic, copy) NSString *loanDateText;
@property (nonatomic, copy) NSString *loanAmountText;
@property (nonatomic, copy) NSString *productName;
@property (nonatomic, copy) NSString *productLogoURL; // tapestrywork
@end
@implementation OrderModel
@end

#pragma mark - 状态选择器Cell
@interface OrderStatusSelectorCell : UICollectionViewCell
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIImageView *bgImageView;
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
- (void)configureWithStatus:(OrderStatus *)status selected:(BOOL)selected;
@end

@implementation OrderStatusSelectorCell {
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        _bgImageView = [[UIImageView alloc] initWithFrame:self.contentView.bounds];
        _bgImageView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        [self.contentView addSubview:_bgImageView];

        self.titleLabel = [[UILabel alloc] initWithFrame:self.contentView.bounds];
        self.titleLabel.textAlignment = NSTextAlignmentCenter;
        self.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        self.titleLabel.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        [self.contentView addSubview:self.titleLabel];

        // 圆角
        self.contentView.layer.cornerRadius = 19;
        self.contentView.layer.masksToBounds = YES;
    }
    return self;
}

- (void)configureWithStatus:(OrderStatus *)status selected:(BOOL)selected {
    self.titleLabel.text = status.title;

    _bgImageView.image = nil;

    if (selected) {
        // 初始化或显示渐变层
        if (!self.gradientLayer) {
            self.gradientLayer = [CAGradientLayer layer];
            self.gradientLayer.startPoint = CGPointMake(0.0, 0.5);
            self.gradientLayer.endPoint = CGPointMake(1.0, 0.5);
            self.gradientLayer.cornerRadius = 19;
            [self.contentView.layer insertSublayer:self.gradientLayer atIndex:0];
        }
        UIColor *startColor = [UIColor colorWithHexString:@"#5B3EFF"];
        UIColor *endColor   = [UIColor colorWithHexString:@"#3172FF"];
        self.gradientLayer.colors = @[ (id)startColor.CGColor, (id)endColor.CGColor ];
        self.gradientLayer.hidden = NO;

        self.contentView.backgroundColor = UIColor.clearColor;
        _bgImageView.backgroundColor = UIColor.clearColor;
    } else {
        // 隐藏渐变层，使用纯色
        if (self.gradientLayer) self.gradientLayer.hidden = YES;
        UIColor *unselectedColor = [UIColor colorWithHexString:@"#83B1FF"];
        self.contentView.backgroundColor = unselectedColor;
        _bgImageView.backgroundColor = unselectedColor;
    }

    self.titleLabel.textColor = [UIColor whiteColor];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    if (self.gradientLayer && !self.gradientLayer.hidden) {
        self.gradientLayer.frame = self.contentView.bounds;
    }
}
@end

#pragma mark - 主控制器实现
@interface OrderViewController () <UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout, UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) UILabel *customTitleLabel;
@property (nonatomic, strong) UILabel *subtitleLabel;
@property (nonatomic, strong) UICollectionView *statusSelector;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *emptyStateView;
@property (nonatomic, strong) NSArray<OrderStatus *> *statusList;
@property (nonatomic, assign) NSInteger selectedStatusIndex;
@property (nonatomic, strong) NSArray<OrderModel *> *allOrders;
@property (nonatomic, strong) NSArray<OrderModel *> *displayOrders;
// 新增：下拉刷新控件
@property (nonatomic, strong) UIRefreshControl *refreshControl;
@end

@implementation OrderViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // 添加透明背景图
    [self setupBackgroundImage];
    self.view.backgroundColor = [UIColor systemBackgroundColor];
    self.selectedStatusIndex = 0;
    [self setupTitleLabel];
    [self setupSubtitleLabel];
    [self setupStatusSelector];
    [self setupTableView];
    // 新增：初始化下拉刷新控件
    [self setupRefreshControl];
    [self configureStatusList];
    [self fetchOrders];
}

#pragma mark - 数据刷新
- (void)refreshData {
    NSLog(@"刷新订单页数据");
    // 调用现有的数据加载方法，保持当前选中的状态过滤
    [self fetchOrders];
}

- (void)setupTitleLabel {
    // 订单中心Logo图片
    UIImageView *logoView = [[UIImageView alloc] init];
    logoView.image = [UIImage imageNamed:@"ordercenter_title_logo"];
    logoView.contentMode = UIViewContentModeScaleAspectFit;
    logoView.accessibilityLabel = @"Order Center Logo";
    [self.view addSubview:logoView];
    [logoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view.mas_safeAreaLayoutGuideLeft).offset(14);
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).offset(4);
        make.width.mas_equalTo(155);
        make.height.mas_equalTo(22);
    }];
    self.customTitleLabel = (UILabel *)logoView; // 兼容后续用法
}

- (void)setupSubtitleLabel {
    self.subtitleLabel = [[UILabel alloc] init];
    self.subtitleLabel.text = @"All your order information is here.\nClick to view the order details.";
    self.subtitleLabel.font = [UIFont systemFontOfSize:13];
    self.subtitleLabel.numberOfLines = 2;
    self.subtitleLabel.textColor = [UIColor secondaryLabelColor];
    self.subtitleLabel.textAlignment = NSTextAlignmentLeft;
    [self.view addSubview:self.subtitleLabel];

    UIView *logoView = (UIView *)self.customTitleLabel;
    [self.subtitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view.mas_safeAreaLayoutGuideLeft).offset(14);
        make.right.equalTo(self.view.mas_safeAreaLayoutGuideRight).offset(-14);
        make.top.equalTo(logoView.mas_bottom).offset(4);
        make.height.mas_equalTo(34);
    }];
}

- (void)setupStatusSelector {
    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
    layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    layout.minimumLineSpacing = 12;
    layout.sectionInset = UIEdgeInsetsMake(0, 20, 0, 20);
    layout.estimatedItemSize = CGSizeZero;
    self.statusSelector = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
    self.statusSelector.backgroundColor = [UIColor clearColor];
    self.statusSelector.showsHorizontalScrollIndicator = NO;
    self.statusSelector.delegate = self;
    self.statusSelector.dataSource = self;
    [self.statusSelector registerClass:[OrderStatusSelectorCell class] forCellWithReuseIdentifier:@"OrderStatusSelectorCell"];
    [self.view addSubview:self.statusSelector];
    UIView *subtitleView = self.subtitleLabel;
    [self.statusSelector mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view.mas_safeAreaLayoutGuideLeft);
        make.right.equalTo(self.view.mas_safeAreaLayoutGuideRight);
        make.top.equalTo(subtitleView.mas_bottom).offset(8);
        make.height.mas_equalTo(39);
    }];
}

- (void)setupTableView {
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    [self.tableView registerClass:[OrderRegularCell class] forCellReuseIdentifier:@"OrderRegularCell"];
    [self.tableView registerClass:[OrderReviewCell class] forCellReuseIdentifier:@"OrderReviewCell"];
    self.tableView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view.mas_safeAreaLayoutGuideLeft);
        make.right.equalTo(self.view.mas_safeAreaLayoutGuideRight);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom);
        make.top.equalTo(self.statusSelector.mas_bottom).offset(8);
    }];
    // 适配自定义TabBar高度，避免内容被遮挡
    CGFloat tabBarHeight = 80.0;
    CGFloat bottomSafe = 0;
    if (@available(iOS 11.0, *)) {
        UIWindow *window = UIApplication.sharedApplication.windows.firstObject;
        bottomSafe = window.safeAreaInsets.bottom;
    }
    self.tableView.contentInset = UIEdgeInsetsMake(0, 0, tabBarHeight + bottomSafe, 0);
    self.tableView.scrollIndicatorInsets = self.tableView.contentInset;
}

// 新增：下拉刷新控件配置
- (void)setupRefreshControl {
    if (@available(iOS 10.0, *)) {
        self.refreshControl = [[UIRefreshControl alloc] init];
        self.refreshControl.tintColor = [UIColor systemGrayColor];
        [self.refreshControl addTarget:self action:@selector(refreshData) forControlEvents:UIControlEventValueChanged];
        self.tableView.refreshControl = self.refreshControl;
    } else {
        UIRefreshControl *refresh = [[UIRefreshControl alloc] init];
        refresh.tintColor = [UIColor systemGrayColor];
        [refresh addTarget:self action:@selector(refreshData) forControlEvents:UIControlEventValueChanged];
        [self.tableView addSubview:refresh];
        self.refreshControl = refresh;
    }
}

#pragma mark - Status & Data

- (void)configureStatusList {
    NSArray *statusTitles = @[ @"ALL", @"In progress", @"Repayment pending", @"Completed" ];
    NSMutableArray *arr = [NSMutableArray arrayWithCapacity:statusTitles.count];
    for (NSString *title in statusTitles) {
        OrderStatus *status = [OrderStatus new];
        status.title = title;
        [arr addObject:status];
    }
    self.statusList = arr;
}

- (NSString *)sunlightCodeForSelectedIndex:(NSInteger)index {
    // 4=全部订单 7=进行中 6=待还款 5=已结清
    switch (index) {
        case 0: return @"4";
        case 1: return @"7";
        case 2: return @"6";
        case 3: return @"5";
        default: return @"4";
    }
}

- (void)fetchOrders {
    // 正常加载时显示 HUD，下拉刷新时不显示
    if (!self.refreshControl.isRefreshing) {
        [HUD showLoadingInView:self.view withMessage:@"Loading orders…"];
    }
    
    NSString *code = [self sunlightCodeForSelectedIndex:self.selectedStatusIndex];
    NSDictionary *params = @{ @"sunlight": code };

    __weak typeof(self) weakSelf = self;
    [NetworkManager postFormWithAPI:@"Alicia/jam" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [HUD hideForView:weakSelf.view];
            // 结束下拉刷新动画
            if (weakSelf.refreshControl.isRefreshing) {
                [weakSelf.refreshControl endRefreshing];
            }

            if (error) {
                [HUD showError:error inView:weakSelf.view];
                return;
            }

            NSDictionary *awkward = response[@"awkward"];
            NSArray *boasting = awkward[@"boasting"];
            if (![boasting isKindOfClass:[NSArray class]]) {
                [HUD showToast:@"Data format error" inView:weakSelf.view];
                return;
            }

            NSMutableArray *orders = [NSMutableArray arrayWithCapacity:boasting.count];
            for (NSDictionary *dict in boasting) {
                if (![dict isKindOfClass:[NSDictionary class]]) continue;
                OrderModel *model = [OrderModel new];
                model.orderId = [dict[@"laceyor"] description] ?: @"";
                model.amount = dict[@"itworse"] ?: @"";
                model.status = dict[@"embroidery"] ?: @"";
                model.cellType = 0; // 可根据需要调整高度类别
                model.jumpURL = dict[@"shegot"] ?: @"";
                model.loanDate = dict[@"asking"] ?: @"";
                model.productLogoURL = dict[@"tapestrywork"] ?: @"";
                //日期文案
                model.loanDateText = dict[@"andasking"] ?: @"";
                //额度文案
                model.loanAmountText = dict[@"puts"] ?: @"";
                //产品名称
                model.productName = dict[@"cando"] ?: @"";
                [orders addObject:model];
            }
            weakSelf.allOrders = orders;
            weakSelf.displayOrders = orders;
            [weakSelf updateEmptyState];
            [weakSelf.tableView reloadData];
        });
    }];
}

// 更新空数据视图
- (void)updateEmptyState {
    if (!self.emptyStateView) {
        UIImageView *img = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"orders_empty_placeholder"]];
        [img.widthAnchor constraintEqualToConstant:68].active = YES;
        [img.heightAnchor constraintEqualToConstant:70].active = YES;
        UILabel *lab = [[UILabel alloc] init];
        lab.text = @"No orders";
        lab.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        lab.textColor = [UIColor whiteColor];
        lab.textAlignment = NSTextAlignmentCenter;
        lab.translatesAutoresizingMaskIntoConstraints = NO;

        UIView *container = [[UIView alloc] init];
        container.translatesAutoresizingMaskIntoConstraints = NO;
        [container addSubview:img];
        [container addSubview:lab];

        img.translatesAutoresizingMaskIntoConstraints = NO;
        [img.centerXAnchor constraintEqualToAnchor:container.centerXAnchor].active = YES;
        [img.topAnchor constraintEqualToAnchor:container.topAnchor].active = YES;

        [lab.topAnchor constraintEqualToAnchor:img.bottomAnchor constant:8].active = YES;
        [lab.centerXAnchor constraintEqualToAnchor:container.centerXAnchor].active = YES;
        [lab.bottomAnchor constraintEqualToAnchor:container.bottomAnchor].active = YES;

        self.emptyStateView = container;
        [self.view addSubview:self.emptyStateView];
        [self.emptyStateView.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor].active = YES;
        [self.emptyStateView.centerYAnchor constraintEqualToAnchor:self.view.centerYAnchor].active = YES;
    }
    self.emptyStateView.hidden = self.displayOrders.count != 0;
}

#pragma mark - UICollectionView
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.statusList.count;
}
- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    OrderStatusSelectorCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"OrderStatusSelectorCell" forIndexPath:indexPath];
    OrderStatus *status = self.statusList[indexPath.item];
    BOOL selected = indexPath.item == self.selectedStatusIndex;
    [cell configureWithStatus:status selected:selected];
    return cell;
}
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    // 立即取消系统选中高亮，避免留下点击阴影
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    self.selectedStatusIndex = indexPath.item;
    [self.statusSelector reloadData];
    [self fetchOrders];
}

// 允许高亮，提升交互反馈
- (BOOL)collectionView:(UICollectionView *)collectionView shouldHighlightItemAtIndexPath:(NSIndexPath *)indexPath {
    return YES;
}

// 计算每个状态按钮宽度，基于文字内容
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    NSString *title = self.statusList[indexPath.item].title ?: @"";
    UIFont *font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    CGSize textSize = [title sizeWithAttributes:@{NSFontAttributeName: font}];
    CGFloat padding = 30; // 左右各12
    CGFloat width = ceil(textSize.width) + padding;
    return CGSizeMake(width, 39);
}

#pragma mark - UITableView
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.displayOrders.count;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    OrderModel *order = self.displayOrders[indexPath.row];
    BOOL isReview = [[order.status lowercaseString] containsString:@"review"];
    CGFloat baseHeight = isReview ? 105.0 : 165.0;
    CGFloat scale = tableView.bounds.size.width / 375.0;
    return ceil(baseHeight * scale) + 10.0;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    OrderModel *order = self.displayOrders[indexPath.row];
    BOOL isReview = [[order.status lowercaseString] containsString:@"review"];
    if (isReview) {
        OrderReviewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"OrderReviewCell" forIndexPath:indexPath];
        cell.titleLabel.text = order.status;
        cell.hintLabel.text = order.amount;
        cell.productNameLabel.text = order.productName;
        // 加载产品 logo
        NSURL *logoURL = [NSURL URLWithString:order.productLogoURL ?: @""];
        UIImage *placeholder = [UIImage imageNamed:@"order_cell_logo"];
        [cell.productIconView sd_setImageWithURL:logoURL placeholderImage:placeholder];
        cell.loanAmountTitleLabel.text = order.loanAmountText;
        cell.loanDateTitleLabel.text = order.loanDateText;
        cell.loanDateValueLabel.text = order.loanDate ?: @"12/10/2024";
        return cell;
    } else {
        OrderRegularCell *cell = [tableView dequeueReusableCellWithIdentifier:@"OrderRegularCell" forIndexPath:indexPath];
        cell.amountLabel.text = order.amount;
        cell.loanAmountTitleLabel.text = order.loanAmountText; // 可根据接口返回字段修改
        cell.statusLabel.text = order.status;
        cell.statusLabel.textColor = [UIColor whiteColor];
        cell.productNameLabel.text = order.productName;
        NSURL *logoURL2 = [NSURL URLWithString:order.productLogoURL ?: @""];
        UIImage *placeholder2 = [UIImage imageNamed:@"order_cell_logo"];
        [cell.productIconView sd_setImageWithURL:logoURL2 placeholderImage:placeholder2];
        cell.loanDateTitleLabel.text = order.loanDateText;
        cell.loanDateValueLabel.text = order.loanDate ?: @"12/10/2024";
        // 固定设置按钮标题为 "Check"
        [cell.button setTitle:@"Check" forState:UIControlStateNormal];
        cell.button.hidden = NO;
        
        //按钮点击事件=cell 点击事件
        [cell.button removeTarget:nil action:NULL forControlEvents:UIControlEventAllEvents];
        [cell.button addTarget:self action:@selector(buttonClicked:) forControlEvents:UIControlEventTouchUpInside];
        return cell;
    }
}
- (UIColor *)colorForStatus:(NSString *)status {
    NSString *lower = [status lowercaseString];
    if ([lower containsString:@"已完成"] || [lower containsString:@"complete"]) {
        return [UIColor systemGreenColor];
    }
    if ([lower containsString:@"未完成"] || [lower containsString:@"incomplete"]) {
        return [UIColor systemOrangeColor];
    }
    if ([lower containsString:@"待支付"] || [lower containsString:@"pending"] || [lower containsString:@"pay"]) {
        return [UIColor systemBlueColor];
    }
    return [UIColor labelColor];
}

#pragma mark - UITableViewDelegate (Select)

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    OrderModel *order = self.displayOrders[indexPath.row];
    [self handleOrderTapWithOrder:order];
}

// 新增方法：设置背景图
- (void)setupBackgroundImage {
    UIImageView *bgView = [[UIImageView alloc] initWithFrame:self.view.bounds];
    bgView.image = [UIImage imageNamed:@"general_background"];
    bgView.contentMode = UIViewContentModeScaleAspectFill;
    bgView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    bgView.userInteractionEnabled = NO;
    bgView.alpha = 1.0; // 可根据需要调整透明度
    [self.view insertSubview:bgView atIndex:0];
}

// 隐藏系统导航栏，让自定义标题紧贴状态栏
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.navigationController setNavigationBarHidden:NO animated:animated];
}



#pragma mark - Order Action Handling

// 统一处理订单跳转逻辑（列表点击或按钮点击）
- (void)handleOrderTapWithOrder:(OrderModel *)order {
    if (!order) { return; }
    NSString *route = order.jumpURL ?: @"";
    if ([route.lowercaseString hasPrefix:@"http"]) {
        H5WebViewController *webVC = [[H5WebViewController alloc] initWithURLString:route];
        webVC.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:webVC animated:YES];
        return;
    }

    if ([route.lowercaseString hasPrefix:@"to://"]) {
        // 原生页面路由：直接在订单页面处理准入逻辑
        NSLog(@"[Order] Native route detected: %@", route);
        
        // 从URL中提取splendid参数
        NSString *splendid = @"";
        NSRange questionMarkRange = [route rangeOfString:@"?"];
        if (questionMarkRange.location != NSNotFound) {
            NSString *queryString = [route substringFromIndex:questionMarkRange.location + 1];
            NSArray *queryItems = [queryString componentsSeparatedByString:@"&"];
            for (NSString *item in queryItems) {
                NSArray *keyValue = [item componentsSeparatedByString:@"="];
                if (keyValue.count == 2) {
                    NSString *key = keyValue[0];
                    NSString *value = keyValue[1];
                    if ([key isEqualToString:@"splendid"]) {
                        splendid = value;
                        break;
                    }
                }
            }
        }
        
        NSLog(@"[Order] Extracted splendid: %@", splendid);
        
        // 直接调用产品准入API
        // NSString *splendid = order.orderId ?: @"";
        // NSDictionary *params = @{ @"splendid": splendid };
        
        // // 显示加载提示
        // [HUD showLoadingInView:self.view withMessage:@"Checking…"];
        
        // __weak typeof(self) weakSelf = self;
        // [NetworkManager postFormWithAPI:@"Alicia/heardmrs" params:params completion:^(NSDictionary *response, NSError *error) {
        //     dispatch_async(dispatch_get_main_queue(), ^{
        //         [HUD hideForView:weakSelf.view];
                
        //         if (error) {
        //             [HUD showError:error inView:weakSelf.view];
        //             return;
        //         }
                
                // 处理准入结果
                // NSString *toteach = nil;
                // if ([response isKindOfClass:[NSDictionary class]]) {
                //     NSDictionary *awkward = response[@"awkward"];
                //     if ([awkward isKindOfClass:[NSDictionary class]]) {
                //         toteach = awkward[@"toteach"];
                //     }
                //     if (!toteach) {
                //         toteach = response[@"toteach"]; // 兜底
                //     }
                // }
                
                // if ([toteach isKindOfClass:[NSString class]] && toteach.length > 0) {
                //     if ([toteach containsString:@"http"]) {
                //         // 跳转到H5页面
                //         H5WebViewController *webVC = [[H5WebViewController alloc] initWithURLString:toteach];
                //         webVC.hidesBottomBarWhenPushed = YES;
                //         [weakSelf.navigationController pushViewController:webVC animated:YES];
                //     } else {
                        // 跳转到认证页面
                        ProductsCertificationViewController *certVC = [[ProductsCertificationViewController alloc] init];
                        certVC.splendid = splendid.length > 0 ? splendid : (order.orderId ?: @"");
                        certVC.hidesBottomBarWhenPushed = YES;
                        [self.navigationController pushViewController:certVC animated:YES];
                //     }
                // } else {
                //     // 无跳转链接，显示提示
                //     [HUD showToast:@"No valid route" inView:weakSelf.view];
                // }
            // });
        // }];
        return;
    }

    // 未知链接类型，给出提示
    [HUD showToast:@"Unsupported link" inView:self.view];
}

// 按钮点击
- (void)buttonClicked:(UIButton *)sender {
    // 将按钮坐标转换到 tableView，找出对应 indexPath
    CGPoint point = [sender convertPoint:CGPointZero toView:self.tableView];
    NSIndexPath *indexPath = [self.tableView indexPathForRowAtPoint:point];
    if (!indexPath) { return; }
    OrderModel *order = self.displayOrders[indexPath.row];
    [self handleOrderTapWithOrder:order];
}

/*
订单列表
请求方式 POST from-data 表单提交
请求地址 "/Alicia/jam":
场景
请求参数
"sunlight":    string    否    状态 4=全部订单 7=进行中订单 6=待还款订单 5=已结清订单

{
  "modest": "0",
  "awkward": {
      "boasting": [{
          "laceyor": "432006",   //订单id
          "dawned": "2",
          "anexciting": 2,
          "cando": "Super Prestamo",   //【重要】 产品名称
          "tapestrywork": "http://super.png",//【重要】 产品logo
          "boldly": "120",  //订单状态
          "resuch": "2021-10-14",
          "thedecency": "Harap bayar tepat waktu",
          "embroidery": "Bayar Segera",   //【重要】状态名称
          "thatmary": "Menunggu pembayaran",    //该笔订单的描述
          "itworse": "Rp 2.000.000",   //借款金额
          "shegot": "http://47.89.212.157:8080/#/confirmOfLoanV3?orderId=432006&productId=2",   //【重要】跳转地址
          "obviously": "http://47.89.212.157:8080/#/confirmOfLoanV3?orderId=432006&productId=2",
          "andasking": "Repayment Date",//日期文案
          "puts": "Repayment Amount",//额度文案
          "asking": "08-09-2022", //展期日期
          "purposely": 0, //逾期天数
          "herforthright": 1, //是否放款
          "hadoverhead": "29-12-2020",   //借款时间
          "nearby": "12-01-2021",    //应还时间
          "twowomen": "91 Hari"   //借款期限
        "asilly": [   //【重要】 订单列表显示数据 直接遍历下方数据就行，左右结构展示
          {
            "coldchicken": "Loan Amount",  //标题
            "terrible": "₹ 2000"   //值
          },
          {
            "coldchicken": "Loan Term",
            "terrible": "91 Days"
          },
          {
            "coldchicken": "application time",
            "terrible": "July 05, 2023"
          },
          {
            "coldchicken": "due time",
            "terrible": "October 04, 2023"
          }
        ],
        "upenough": "View Loan Agreement",  //【重要】借款协议 展示文案  为空不显示
        "pluck": "https://scb.britexfinancialspl.com/bee/yeah/frightente?putter=SBI20230705144
      }],
      "anythingto": 1
  },
  "patted": "success"
}
 */
@end

