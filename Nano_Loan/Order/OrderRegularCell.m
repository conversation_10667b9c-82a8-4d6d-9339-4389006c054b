#import "OrderRegularCell.h"
#import <Masonry/Masonry.h>

@implementation OrderRegularCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        // 背景图, 改成mas
        self.backgroundColor = [UIColor clearColor];
        UIImage *bg = [[UIImage imageNamed:@"order_cell_regular_bg"] resizableImageWithCapInsets:UIEdgeInsetsMake(0, 0, 0, 0) resizingMode:UIImageResizingModeStretch];
        UIImageView *bgView = [[UIImageView alloc] init];
        bgView.image = bg;
        [self.contentView addSubview:bgView];   
        [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
            //浮岛样式，左右还有12pt
            make.left.equalTo(self.contentView).offset(12);
            make.right.equalTo(self.contentView).offset(-12);
            make.top.equalTo(self.contentView).offset(0);
            make.bottom.equalTo(self.contentView).offset(0);
        }];

        // 文字标签
        // 产品图标
        self.productIconView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"order_cell_logo"]]; // 默认占位图，可在外部替换
        [self.contentView addSubview:self.productIconView];

        // 产品名称
        self.productNameLabel = [[UILabel alloc] init];
        self.productNameLabel.font = [UIFont systemFontOfSize:12];
        self.productNameLabel.textColor = [UIColor labelColor];
        [self.contentView addSubview:self.productNameLabel];

        // Loan amount title label
        self.loanAmountTitleLabel = [[UILabel alloc] init];
        self.loanAmountTitleLabel.font = [UIFont systemFontOfSize:12];
        self.loanAmountTitleLabel.textColor = [UIColor colorWithRed:0xA7/255.0 green:0xA7/255.0 blue:0xA7/255.0 alpha:1.0];
        [self.contentView addSubview:self.loanAmountTitleLabel];

        // 金额等文字标签
        UIFont *priceFont = [UIFont fontWithName:@"Verdana-BoldItalic" size:16];
        if (!priceFont) {
            priceFont = [UIFont boldSystemFontOfSize:16]; // fallback
        }
        self.amountLabel = [[UILabel alloc] init];
        self.amountLabel.font = priceFont;
        self.amountLabel.textColor = [UIColor colorWithRed:0x29/255.0 green:0x29/255.0 blue:0x29/255.0 alpha:1.0];
        [self.contentView addSubview:self.amountLabel];

        self.statusLabel = [[UILabel alloc] init];
        self.statusLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightSemibold];
        self.statusLabel.textAlignment = NSTextAlignmentCenter;
        self.statusLabel.textColor = [UIColor whiteColor];
        [self.contentView addSubview:self.statusLabel];

        // Loan date title label
        self.loanDateTitleLabel = [[UILabel alloc] init];
        self.loanDateTitleLabel.font = [UIFont systemFontOfSize:12];
        self.loanDateTitleLabel.textColor = [UIColor colorWithRed:0xA7/255.0 green:0xA7/255.0 blue:0xA7/255.0 alpha:1.0];
        [self.contentView addSubview:self.loanDateTitleLabel];

        // Loan date value label
        self.loanDateValueLabel = [[UILabel alloc] init];
        self.loanDateValueLabel.font = priceFont; // 同价格 label 字体
        self.loanDateValueLabel.textColor = [UIColor colorWithRed:0x29/255.0 green:0x29/255.0 blue:0x29/255.0 alpha:1.0];
        [self.contentView addSubview:self.loanDateValueLabel];

        //下方按钮
        self.button = [[UIButton alloc] init];
        [self.button setTitle:@"Check" forState:UIControlStateNormal];
        [self.button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        //白色字体
        self.button.titleLabel.textColor = [UIColor whiteColor];
        //字号 16，字体为Verdana-BoldItalic
        self.button.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:16];
        [self.button setBackgroundImage:[UIImage imageNamed:@"order_cell_button_bg"] forState:UIControlStateNormal];
        [self.contentView addSubview:self.button];

        // Masonry constraints
        // 1. 产品图标
        [self.productIconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentView).offset(21);
            make.top.equalTo(self.contentView).offset(12);
            make.width.height.mas_equalTo(17);
        }];

        // 2. 产品名称
        [self.productNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.productIconView.mas_right).offset(6);
            make.centerY.equalTo(self.productIconView);
            make.right.lessThanOrEqualTo(self.contentView).offset(-14);
            make.height.mas_equalTo(17);
        }];

        // 3. Loan amount title label
        [self.loanAmountTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.productNameLabel.mas_left);
            make.top.equalTo(self.productNameLabel.mas_bottom).offset(19);
            make.height.mas_equalTo(15);
        }];

        // 4. 价格标签位于 Loan amount title 下方 6pt
        [self.amountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.loanAmountTitleLabel.mas_left);
            make.top.equalTo(self.loanAmountTitleLabel.mas_bottom).offset(6);
            make.height.mas_equalTo(21);
        }];

        // 5. Loan date title label (左对齐 Loan amount title ，右移 5pt)
        [self.loanDateTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.statusLabel.mas_left).offset(5);
            make.centerY.equalTo(self.loanAmountTitleLabel);
            make.height.mas_equalTo(15);
        }];

        // 6. Loan date value label (下方 6pt，与价格 label 左对齐)
        [self.loanDateValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.loanDateTitleLabel.mas_left);
            make.centerY.equalTo(self.amountLabel);
            make.height.mas_equalTo(21);
        }];

        [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.contentView.mas_right).offset(-14);
            make.top.equalTo(self.contentView).offset(6);
            // 宽度按 375 设计稿中的 144 等比缩放
            make.width.equalTo(self.contentView.mas_width).multipliedBy(144.0/375.0);
        }];

        // 7. 下方按钮
        [self.button mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.contentView);
            make.bottom.equalTo(self.contentView.mas_bottom).offset(-28);
            make.width.mas_equalTo(300);
            make.height.mas_equalTo(42);
        }];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
}

@end 
