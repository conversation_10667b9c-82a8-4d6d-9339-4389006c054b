//
//  SettingViewController.m
//  Nano_Loan
//
//  Created by yongsheng ye on 2025/6/23.
//

#import "SettingViewController.h"
#import <objc/runtime.h>
#import "NetworkManager.h" // 网络请求
#import "CustomTabBarController.h" // 用于切换Tab
#import "CustomTabBar.h" // 解决 forward declaration 问题
#import "UIColor+Hex.h" // 颜色扩展
#import <Masonry/Masonry.h>
#import "HUD.h"

@interface SettingViewController ()

@property (nonatomic, strong) UIButton *logoutButton;   // 退出登录
@property (nonatomic, strong) UIButton *deleteButton;   // 注销账号（顶部列表样式）

@property (nonatomic, strong) UIView *overlayView;      // 半透明遮罩
@property (nonatomic, strong) UIView *dialogView;       // 弹窗容器

@property (nonatomic, strong) UIView *navView;          // 自定义导航栏

// 注销弹窗相关
@property (nonatomic, strong) UIButton *agreementCheckbox; // 协议勾选框
@property (nonatomic, assign) BOOL isAgreementChecked;     // 协议是否已勾选

@end

@implementation SettingViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor systemBackgroundColor];
    [self setupUI];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    // 隐藏系统导航栏，使用自定义导航
    [self.navigationController setNavigationBarHidden:YES animated:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.navigationController setNavigationBarHidden:NO animated:animated];
}

#pragma mark - UI

- (void)setupUI {
    // 添加背景图覆盖全屏
    UIImageView *backgroundImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"general_background"]];
    backgroundImageView.contentMode = UIViewContentModeScaleAspectFill;
    backgroundImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:backgroundImageView];
    [NSLayoutConstraint activateConstraints:@[
        [backgroundImageView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [backgroundImageView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [backgroundImageView.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [backgroundImageView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
    ]];

    // 自定义导航栏
    self.navView = [[UIView alloc] init];
    self.navView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.navView];
    UILayoutGuide *safe = self.view.safeAreaLayoutGuide;
    [NSLayoutConstraint activateConstraints:@[
        [self.navView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.navView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.navView.topAnchor constraintEqualToAnchor:safe.topAnchor],
        [self.navView.heightAnchor constraintEqualToConstant:44]
    ]];

    UIButton *backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [backBtn setImage:[UIImage imageNamed:@"nav_back"] forState:UIControlStateNormal];
    backBtn.frame = CGRectMake(0, 0, 34, 34);
    backBtn.translatesAutoresizingMaskIntoConstraints = NO;
    [backBtn addTarget:self action:@selector(backAction) forControlEvents:UIControlEventTouchUpInside];
    [self.navView addSubview:backBtn];
    [NSLayoutConstraint activateConstraints:@[
        [backBtn.leadingAnchor constraintEqualToAnchor:self.navView.leadingAnchor constant:16],
        [backBtn.centerYAnchor constraintEqualToAnchor:self.navView.centerYAnchor],
        [backBtn.widthAnchor constraintEqualToConstant:34],
        [backBtn.heightAnchor constraintEqualToConstant:34]
    ]];

    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"Settings";
    titleLabel.font = [UIFont boldSystemFontOfSize:22];
    titleLabel.textColor = [UIColor whiteColor];
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.navView addSubview:titleLabel];
    [NSLayoutConstraint activateConstraints:@[
        [titleLabel.centerXAnchor constraintEqualToAnchor:self.navView.centerXAnchor],
        [titleLabel.centerYAnchor constraintEqualToAnchor:self.navView.centerYAnchor]
    ]];

    // 顶部只保留"Logout"按钮
    self.deleteButton = [self createRowButtonWithTitle:@"Logout" selector:@selector(logoutButtonTapped)];
    UIStackView *stack = [[UIStackView alloc] initWithArrangedSubviews:@[self.deleteButton]];
    stack.axis = UILayoutConstraintAxisVertical;
    stack.spacing = 8; // 增加间距使背景图片不重叠
    stack.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:stack];
    [NSLayoutConstraint activateConstraints:@[
        [stack.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:16],
        [stack.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-16],
        [stack.topAnchor constraintEqualToAnchor:self.navView.bottomAnchor constant:16]
    ]];

    // 底部"Logout"按钮样式与布局
    self.logoutButton = [UIButton buttonWithType:UIButtonTypeSystem];
    self.logoutButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.logoutButton setTitle:@"Delete Account" forState:UIControlStateNormal];
    [self.logoutButton setTitleColor:[UIColor colorWithHexString:@"#5841FF"] forState:UIControlStateNormal];
    self.logoutButton.titleLabel.font = [UIFont preferredFontForTextStyle:UIFontTextStyleBody];
    self.logoutButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentCenter;
    [self.logoutButton addTarget:self action:@selector(deleteButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.logoutButton];

    [NSLayoutConstraint activateConstraints:@[
        [self.logoutButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.logoutButton.bottomAnchor constraintEqualToAnchor:safe.bottomAnchor constant:-16],
        [self.logoutButton.heightAnchor constraintEqualToConstant:44]
    ]];
}

- (UIButton *)createRowButtonWithTitle:(NSString *)title selector:(SEL)selector {
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeSystem];
    btn.translatesAutoresizingMaskIntoConstraints = NO;
    
    // 使用背景图片替代纯色背景
    UIImage *bgImage = [UIImage imageNamed:@"setting_cell_bg"];
    [btn setBackgroundImage:bgImage forState:UIControlStateNormal];
    
    btn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    btn.titleEdgeInsets = UIEdgeInsetsMake(0, 48, 0, 0); // 图标宽度32+间距16=48
    btn.titleLabel.font = [UIFont preferredFontForTextStyle:UIFontTextStyleBody];
    [btn setTitle:title forState:UIControlStateNormal];
    [btn setTitleColor:[UIColor labelColor] forState:UIControlStateNormal];
    [btn addTarget:self action:selector forControlEvents:UIControlEventTouchUpInside];
    [btn.heightAnchor constraintEqualToConstant:56].active = YES;

    // 只为"Logout"按钮添加图标
    if ([title isEqualToString:@"Logout"]) {
        UIImageView *iconView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"setting_cell_logout"]];
        iconView.translatesAutoresizingMaskIntoConstraints = NO;
        [btn addSubview:iconView];
        [NSLayoutConstraint activateConstraints:@[
            [iconView.leadingAnchor constraintEqualToAnchor:btn.leadingAnchor constant:16],
            [iconView.centerYAnchor constraintEqualToAnchor:btn.centerYAnchor],
            [iconView.widthAnchor constraintEqualToConstant:22],
            [iconView.heightAnchor constraintEqualToConstant:22]
        ]];
        // 添加右侧箭头图标
        UIImageView *arrowView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"setting_cell_arrow"]];
        arrowView.translatesAutoresizingMaskIntoConstraints = NO;
        [btn addSubview:arrowView];
        [NSLayoutConstraint activateConstraints:@[
            [arrowView.trailingAnchor constraintEqualToAnchor:btn.trailingAnchor constant:-14],
            [arrowView.centerYAnchor constraintEqualToAnchor:btn.centerYAnchor],
            [arrowView.widthAnchor constraintEqualToConstant:20],
            [arrowView.heightAnchor constraintEqualToConstant:20]
        ]];
    }
    return btn;
}

#pragma mark - Actions

- (void)logoutButtonTapped {
    __weak typeof(self) weakSelf = self;
    [self presentDialogWithPrimaryTitle:@"Logout"
                         secondaryTitle:@"Cancel"
                       horizontalLayout:YES
                          primaryColor:[UIColor systemRedColor]
                          primaryBlock:^{
                              [weakSelf performLogout];
                          }];
}

- (void)deleteButtonTapped {
    __weak typeof(self) weakSelf = self;
    [self presentDeleteAccountDialog];
}

#pragma mark - Delete Account Dialog

- (void)presentDeleteAccountDialog {
    // 重置协议勾选状态
    self.isAgreementChecked = NO;

    // 1. 半透明遮罩
    self.overlayView = [[UIView alloc] initWithFrame:self.view.bounds];
    self.overlayView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    self.overlayView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self.view addSubview:self.overlayView];

    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissDialog)];
    [self.overlayView addGestureRecognizer:tap];

    // 2. 主弹窗背景（替换为图片）
    UIImageView *dialogBg = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"pop_dialog_bg"]];
    dialogBg.userInteractionEnabled = YES; // 允许交互
    self.dialogView = dialogBg; // 复用 dialogView 属性，方便后续动画&关闭
    [self.overlayView addSubview:self.dialogView];

    // 计算尺寸: 宽 = 屏幕宽 - 30, 高按 323/346 比例
    CGFloat dialogWidth = UIScreen.mainScreen.bounds.size.width - 30.0;
    CGFloat ratio = 323.0 / 346.0;

    [self.dialogView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.overlayView);
        make.width.mas_equalTo(dialogWidth);
        make.height.equalTo(self.dialogView.mas_width).multipliedBy(ratio);
    }];

    // 3. 关闭按钮
    UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeBtn setImage:[UIImage imageNamed:@"pop_close_icon"] forState:UIControlStateNormal];
    [closeBtn addTarget:self action:@selector(dismissDialog) forControlEvents:UIControlEventTouchUpInside];
    [self.dialogView addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.dialogView).offset(23);
        make.right.equalTo(self.dialogView).offset(-23);
        make.width.height.mas_equalTo(24);
    }];

    // 4. 标题
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"Delete Account";
    UIFont *verdanaBI = [UIFont fontWithName:@"Verdana-BoldItalic" size:18];
    titleLabel.font = verdanaBI ?: [UIFont italicSystemFontOfSize:18];
    titleLabel.textColor = [UIColor whiteColor];
    [self.dialogView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.dialogView.mas_left).offset(25);
        make.top.equalTo(self.dialogView.mas_top).offset(9);
        make.right.lessThanOrEqualTo(closeBtn.mas_left).offset(-8);
    }];

    // 5. 文案 - 与退出登录弹窗位置一致
    UILabel *messageLabel = [[UILabel alloc] init];
    messageLabel.text = @"Are You Sure You Want To Log Out And Delete Your Account? This Action Will Permanently Remove All Your Data.";
    messageLabel.textColor = [UIColor colorWithHexString:@"#1B0F55"] ?: [UIColor labelColor];
    messageLabel.font = [UIFont systemFontOfSize:15];
    messageLabel.numberOfLines = 0;
    messageLabel.textAlignment = NSTextAlignmentCenter;
    [self.dialogView addSubview:messageLabel];
    [messageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(titleLabel.mas_bottom).offset(100); // 与退出登录弹窗一致
        make.centerX.equalTo(self.dialogView);
        make.width.equalTo(self.dialogView).multipliedBy(0.7); // 与退出登录弹窗一致
    }];

    // 6. 按钮栈 - 先创建按钮栈以便协议相对按钮定位
    UIButton *deleteButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [deleteButton setTitle:@"Delete Account" forState:UIControlStateNormal];
    [deleteButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    deleteButton.titleLabel.font = [UIFont boldSystemFontOfSize:17];
    [deleteButton setBackgroundImage:[UIImage imageNamed:@"pop_button_bg"] forState:UIControlStateNormal];
    [deleteButton addTarget:self action:@selector(confirmDeleteAccount) forControlEvents:UIControlEventTouchUpInside];

    UIButton *cancelButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [cancelButton setTitle:@"No" forState:UIControlStateNormal];
    [cancelButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    cancelButton.titleLabel.font = [UIFont boldSystemFontOfSize:17];
    [cancelButton setBackgroundImage:[UIImage imageNamed:@"pop_button_bg"] forState:UIControlStateNormal];
    [cancelButton addTarget:self action:@selector(dismissDialog) forControlEvents:UIControlEventTouchUpInside];

    UIStackView *btnStack = [[UIStackView alloc] initWithArrangedSubviews:@[deleteButton, cancelButton]];
    btnStack.axis = UILayoutConstraintAxisHorizontal;
    btnStack.spacing = 16;
    btnStack.distribution = UIStackViewDistributionFillEqually;
    [self.dialogView addSubview:btnStack];

    [btnStack mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.dialogView).offset(24);
        make.right.equalTo(self.dialogView).offset(-24);
        make.bottom.equalTo(self.dialogView.mas_bottom).offset(-35);
        make.height.mas_equalTo(48);
    }];

    // 7. 协议勾选区域 - 放在按钮上方10pt，支持多行文本
    UIView *agreementView = [[UIView alloc] init];
    [self.dialogView addSubview:agreementView];
    [agreementView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(btnStack.mas_top).offset(-10); // 按钮上方10pt
        make.centerX.equalTo(self.dialogView);
        make.width.equalTo(self.dialogView).multipliedBy(0.8);
        // 移除固定高度约束，让内容自适应
    }];

    // 勾选框
    self.agreementCheckbox = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.agreementCheckbox setImage:[UIImage imageNamed:@"login_privacy_unselected"] forState:UIControlStateNormal];
    [self.agreementCheckbox setImage:[UIImage imageNamed:@"login_privacy_selected"] forState:UIControlStateSelected];
    [self.agreementCheckbox addTarget:self action:@selector(toggleAgreementCheckbox:) forControlEvents:UIControlEventTouchUpInside];
    self.agreementCheckbox.selected = NO; // 默认不勾选
    [agreementView addSubview:self.agreementCheckbox];
    [self.agreementCheckbox mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(agreementView);
        make.top.equalTo(agreementView); // 改为顶部对齐
        make.width.height.mas_equalTo(20);
    }];

    // 协议文本 - 支持多行
    UILabel *agreementLabel = [[UILabel alloc] init];
    agreementLabel.text = @"I Confirm Deleting All Data Of The Current Account";
    agreementLabel.textColor = [UIColor colorWithHexString:@"#1B0F55"] ?: [UIColor labelColor];
    agreementLabel.font = [UIFont systemFontOfSize:13];
    agreementLabel.numberOfLines = 0; // 支持多行
    agreementLabel.lineBreakMode = NSLineBreakByWordWrapping; // 按单词换行
    [agreementView addSubview:agreementLabel];
    [agreementLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.agreementCheckbox.mas_right).offset(8);
        make.top.equalTo(agreementView);
        make.right.equalTo(agreementView);
        make.bottom.equalTo(agreementView); // 让agreementView的高度由文本内容决定
    }];

    // 8. 弹入动画
    self.dialogView.transform = CGAffineTransformMakeScale(1.1, 1.1);
    self.dialogView.alpha = 0;
    [UIView animateWithDuration:0.25 animations:^{
        self.dialogView.transform = CGAffineTransformIdentity;
        self.dialogView.alpha = 1.0;
    }];
}

- (void)toggleAgreementCheckbox:(UIButton *)sender {
    self.isAgreementChecked = !self.isAgreementChecked;
    self.agreementCheckbox.selected = self.isAgreementChecked;
}

- (void)confirmDeleteAccount {
    if (!self.isAgreementChecked) {
        // 未勾选协议，显示英文提示
        [HUD showToast:@"Please check the agreement to proceed with account deletion" inView:self.view];
        return;
    }

    // 勾选了协议，执行删除账号
    [self dismissDialog];
    [self performDeleteAccount];
}

#pragma mark - Dialog

- (void)presentDialogWithPrimaryTitle:(NSString *)primaryTitle
                        secondaryTitle:(NSString *)secondaryTitle
                      horizontalLayout:(BOOL)isHorizontal
                         primaryColor:(UIColor *)primaryColor
                         primaryBlock:(void (^)(void))primaryBlock {
    // 1. 半透明遮罩
    self.overlayView = [[UIView alloc] initWithFrame:self.view.bounds];
    self.overlayView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    self.overlayView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self.view addSubview:self.overlayView];

    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissDialog)];
    [self.overlayView addGestureRecognizer:tap];

    // 2. 主弹窗背景（替换为图片）
    UIImageView *dialogBg = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"pop_dialog_bg"]];
    dialogBg.userInteractionEnabled = YES; // 允许交互
    self.dialogView = dialogBg; // 复用 dialogView 属性，方便后续动画&关闭
    [self.overlayView addSubview:self.dialogView];

    // 计算尺寸: 宽 = 屏幕宽 - 30, 高按 323/346 比例
    CGFloat dialogWidth = UIScreen.mainScreen.bounds.size.width - 30.0;
    CGFloat ratio = 323.0 / 346.0;

    [self.dialogView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.overlayView);
        make.width.mas_equalTo(dialogWidth);
        make.height.equalTo(self.dialogView.mas_width).multipliedBy(ratio);
    }];

    // 3. 关闭按钮
    UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeBtn setImage:[UIImage imageNamed:@"pop_close_icon"] forState:UIControlStateNormal];
    [closeBtn addTarget:self action:@selector(dismissDialog) forControlEvents:UIControlEventTouchUpInside];
    [self.dialogView addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.dialogView).offset(23);
        make.right.equalTo(self.dialogView).offset(-23);
        make.width.height.mas_equalTo(24);
    }];

    // 4. 标题 & 文案根据 primaryTitle 区分
    NSString *dialogTitle = @"Log Out of All Devices?";
    NSString *dialogMessage = @"You are currently logged in on multiple devices. Do you want to log out of all devices or just this one?";
    if ([primaryTitle isEqualToString:@"Delete Account"]) {
        dialogTitle = @"Don't Leave";
        dialogMessage = @"We'd hate to see you go! As a token of appreciation, here's a special offer just for you. Stay with us and enjoy exclusive benefits!";
    }

    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = dialogTitle;
    UIFont *verdanaBI = [UIFont fontWithName:@"Verdana-BoldItalic" size:18];
    titleLabel.font = verdanaBI ?: [UIFont italicSystemFontOfSize:18];
    titleLabel.textColor = [UIColor whiteColor];
    [self.dialogView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.dialogView.mas_left).offset(25);
        make.top.equalTo(self.dialogView.mas_top).offset(9);
        make.right.lessThanOrEqualTo(closeBtn.mas_left).offset(-8);
    }];

    // 6. 文案
    UILabel *messageLabel = [[UILabel alloc] init];
    messageLabel.text = dialogMessage;
    messageLabel.textColor = [UIColor colorWithHexString:@"#1B0F55"] ?: [UIColor labelColor];
    messageLabel.font = [UIFont systemFontOfSize:15];
    messageLabel.numberOfLines = 0;
    messageLabel.textAlignment = NSTextAlignmentCenter;
    [self.dialogView addSubview:messageLabel];
    [messageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.edges.equalTo(contentBg).insets(UIEdgeInsetsMake(24, 16, 24, 16));
        make.top.equalTo(titleLabel.mas_bottom).offset(100);
        make.centerX.equalTo(self.dialogView);
        make.width.equalTo(self.dialogView).multipliedBy(0.7);
    }];

    // 7. 按钮栈
    UIButton *primaryButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [primaryButton setTitle:primaryTitle forState:UIControlStateNormal];
    [primaryButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    primaryButton.titleLabel.font = [UIFont boldSystemFontOfSize:17];
    [primaryButton setBackgroundImage:[UIImage imageNamed:@"pop_button_bg"] forState:UIControlStateNormal];
    [primaryButton addTarget:self action:@selector(dialogPrimaryTapped:) forControlEvents:UIControlEventTouchUpInside];
    objc_setAssociatedObject(primaryButton, @selector(dialogPrimaryTapped:), primaryBlock, OBJC_ASSOCIATION_COPY_NONATOMIC);

    UIButton *secondaryButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [secondaryButton setTitle:secondaryTitle forState:UIControlStateNormal];
    [secondaryButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    secondaryButton.titleLabel.font = [UIFont boldSystemFontOfSize:17];
    [secondaryButton setBackgroundImage:[UIImage imageNamed:@"pop_button_bg"] forState:UIControlStateNormal];
    [secondaryButton addTarget:self action:@selector(dismissDialog) forControlEvents:UIControlEventTouchUpInside];

    UIStackView *btnStack = [[UIStackView alloc] initWithArrangedSubviews:@[primaryButton, secondaryButton]];
    btnStack.axis = UILayoutConstraintAxisHorizontal;
    btnStack.spacing = 16;
    btnStack.distribution = UIStackViewDistributionFillEqually;
    [self.dialogView addSubview:btnStack];

    [btnStack mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.dialogView).offset(24);
        make.right.equalTo(self.dialogView).offset(-24);
        make.bottom.equalTo(self.dialogView.mas_bottom).offset(-35);
        make.height.mas_equalTo(48);
        make.bottom.equalTo(self.dialogView).offset(-24);
    }];

    // 8. 弹入动画
    self.dialogView.transform = CGAffineTransformMakeScale(1.1, 1.1);
    self.dialogView.alpha = 0;
    [UIView animateWithDuration:0.25 animations:^{
        self.dialogView.transform = CGAffineTransformIdentity;
        self.dialogView.alpha = 1.0;
    }];
}

- (void)dialogPrimaryTapped:(UIButton *)sender {
    void (^block)(void) = objc_getAssociatedObject(sender, _cmd);
    if (block) {
        block();
    }
    [self dismissDialog];
}

- (void)dismissDialog {
    [UIView animateWithDuration:0.2 animations:^{
        self.overlayView.alpha = 0;
        self.dialogView.alpha = 0;
    } completion:^(BOOL finished) {
        [self.overlayView removeFromSuperview];
        self.overlayView = nil;
        self.dialogView = nil;
    }];
}

#pragma mark - Logout API

- (void)performLogout {
    [self showLoadingIndicator:YES];
    __weak typeof(self) weakSelf = self;
    [NetworkManager requestWithAPI:@"Alicia/likes" params:@{} method:@"Get" completion:^(NSDictionary *response, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf showLoadingIndicator:NO];
            if (!error) {
                NSLog(@"Logout API patted: %@", response[@"patted"]);

                NSString *msg = response[@"patted"] ?: @"Logout result";
                NSInteger modestValue = [response[@"modest"] integerValue];

                // 使用 HUD 显示提示
                [HUD showToast:msg inView:weakSelf.view];

                if (modestValue == 0) {
                    // 成功：清理本地 token，并在 Toast 消失后跳转
                    [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"token"];
                    [[NSUserDefaults standardUserDefaults] synchronize];

                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [weakSelf goToHome];
                        [[NSNotificationCenter defaultCenter] postNotificationName:@"HomeShouldRefresh" object:nil];
                    });
                }
            } else {
                NSLog(@"Logout API error: %@", error.localizedDescription);
                [HUD showError:error inView:weakSelf.view];
            }
        });
    }];
}

#pragma mark - Delete Account API

- (void)performDeleteAccount {
    [self showLoadingIndicator:YES];
    __weak typeof(self) weakSelf = self;
    [NetworkManager requestWithAPI:@"Alicia/coldchicken" params:@{} method:@"Get" completion:^(NSDictionary *response, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf showLoadingIndicator:NO];
            if (!error) {
                // 清理 token
                [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"token"];
                [[NSUserDefaults standardUserDefaults] synchronize];

                UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Account Deleted" message:@"Your account has been deleted successfully." preferredStyle:UIAlertControllerStyleAlert];
                UIAlertAction *ok = [UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                    [weakSelf goToHome];
                }];
                [alert addAction:ok];
                [weakSelf presentViewController:alert animated:YES completion:nil];
            } else {
                UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Error" message:error.localizedDescription ?: @"Delete failed" preferredStyle:UIAlertControllerStyleAlert];
                UIAlertAction *ok = [UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil];
                [alert addAction:ok];
                [weakSelf presentViewController:alert animated:YES completion:nil];
            }
        });
    }];
}

#pragma mark - Helper to switch to Home

- (void)goToHome {
    // 切换Tab到首页 (index 0)
    UITabBarController *tab = self.tabBarController;
    if ([tab isKindOfClass:[CustomTabBarController class]]) {
        CustomTabBarController *customTab = (CustomTabBarController *)tab;
        customTab.selectedIndex = 0;
        if ([customTab.customTabBar respondsToSelector:@selector(setSelectedIndex:)]) {
            [customTab.customTabBar setSelectedIndex:0];
        }
    } else {
        tab.selectedIndex = 0;
    }

    // 返回到首页导航的根视图
    [self.navigationController popToRootViewControllerAnimated:YES];

    // 发送通知供其他页面刷新
    [[NSNotificationCenter defaultCenter] postNotificationName:@"UserStatusCleared" object:nil];
    // 额外通知首页重新拉取数据
    [[NSNotificationCenter defaultCenter] postNotificationName:@"HomeShouldRefresh" object:nil];
}

#pragma mark - Loading HUD (simple)

- (void)showLoadingIndicator:(BOOL)show {
    static UIActivityIndicatorView *indicator;
    if (show) {
        if (!indicator) {
            indicator = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleLarge];
            indicator.color = [UIColor labelColor];
            indicator.translatesAutoresizingMaskIntoConstraints = NO;
            [self.view addSubview:indicator];
            [NSLayoutConstraint activateConstraints:@[
                [indicator.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
                [indicator.centerYAnchor constraintEqualToAnchor:self.view.centerYAnchor]
            ]];
        }
        [indicator startAnimating];
    } else {
        [indicator stopAnimating];
    }
}

#pragma mark - Custom Nav Back

- (void)backAction {
    [self.navigationController popViewControllerAnimated:YES];
}

@end
