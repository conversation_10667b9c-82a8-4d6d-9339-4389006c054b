#import "CustomTabBarController.h"
#import "CustomTabBar.h"
#import "HomeViewController.h"
#import "OrderViewController.h"
#import "ProfileViewController.h"
#import "LoginViewController.h"

@interface CustomTabBarController () <CustomTabBarDelegate>
@end

@implementation CustomTabBarController {
    NSArray *_viewControllersArray;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupViewControllers];
    [self setupCustomTabBar];
    // 默认选中首页Tab
    self.selectedIndex = 0;
    if ([self.customTabBar respondsToSelector:@selector(setSelectedIndex:)]) {
        [self.customTabBar setSelectedIndex:0];
    }
}

- (void)setupViewControllers {
    UINavigationController *homeNav   = [[UINavigationController alloc] initWithRootViewController:[[HomeViewController alloc] init]];
    UINavigationController *orderNav  = [[UINavigationController alloc] initWithRootViewController:[[OrderViewController alloc] init]];
    UINavigationController *profileNav = [[UINavigationController alloc] initWithRootViewController:[[ProfileViewController alloc] init]];
    _viewControllersArray = @[homeNav, orderNav, profileNav];
    self.viewControllers = _viewControllersArray;

    // 设置导航控制器代理，用于隐藏/显示自定义TabBar
    for (UINavigationController *nav in _viewControllersArray) {
        nav.delegate = self;
    }
}

- (void)setupCustomTabBar {
    self.tabBar.hidden = YES;
    self.customTabBar = [[CustomTabBar alloc] initWithFrame:self.tabBar.frame];
    self.customTabBar.delegate = self;
    [self.view addSubview:self.customTabBar];
}

#pragma mark - CustomTabBarDelegate
- (void)customTabBarDidSelectIndex:(NSInteger)index {
    NSString *token = [[NSUserDefaults standardUserDefaults] stringForKey:@"token"];
    if ((index == 1 || index == 2) && (!token || token.length == 0)) {
        // 未登录，弹出登录页并锁定首页
        self.selectedIndex = 0;
        if ([self.customTabBar respondsToSelector:@selector(setSelectedIndex:)]) {
            [self.customTabBar setSelectedIndex:0];
        }
        LoginViewController *loginVC = [[LoginViewController alloc] init];
        loginVC.modalPresentationStyle = UIModalPresentationFullScreen;
        [self presentViewController:loginVC animated:YES completion:nil];
        return;
    }
    
    // 如果已经在当前选中的页面，则刷新该页面数据
    if (self.selectedIndex == index) {
        [self refreshCurrentPageData:index];
    } else {
        // 切换到新页面，并在页面显示后刷新数据
        self.selectedIndex = index;
        [self refreshCurrentPageData:index];
    }
}

// 刷新当前页面数据
- (void)refreshCurrentPageData:(NSInteger)index {
    if (index < _viewControllersArray.count) {
        UINavigationController *navController = _viewControllersArray[index];
        UIViewController *rootViewController = navController.viewControllers.firstObject;
        
        // 根据不同页面类型调用对应的刷新方法
        if ([rootViewController isKindOfClass:[HomeViewController class]]) {
            HomeViewController *homeVC = (HomeViewController *)rootViewController;
            if ([homeVC respondsToSelector:@selector(refreshData)]) {
                [homeVC refreshData];
            }
        } else if ([rootViewController isKindOfClass:[OrderViewController class]]) {
            OrderViewController *orderVC = (OrderViewController *)rootViewController;
            if ([orderVC respondsToSelector:@selector(refreshData)]) {
                [orderVC refreshData];
            }
        } else if ([rootViewController isKindOfClass:[ProfileViewController class]]) {
            ProfileViewController *profileVC = (ProfileViewController *)rootViewController;
            if ([profileVC respondsToSelector:@selector(refreshData)]) {
                [profileVC refreshData];
            }
        }
    }
}

#pragma mark - UINavigationControllerDelegate

- (void)navigationController:(UINavigationController *)navigationController willShowViewController:(UIViewController *)viewController animated:(BOOL)animated {
    BOOL isRootVC = (viewController == navigationController.viewControllers.firstObject);
    [self setCustomTabBarHidden:!isRootVC animated:animated];
}

- (void)setCustomTabBarHidden:(BOOL)hidden animated:(BOOL)animated {
    if (self.customTabBar.hidden == hidden) return;
    if (animated) {
        [UIView animateWithDuration:0.25 animations:^{
            self.customTabBar.alpha = hidden ? 0 : 1;
        } completion:^(BOOL finished) {
            self.customTabBar.hidden = hidden;
            if (!hidden) {
                if ([self.customTabBar respondsToSelector:@selector(setSelectedIndex:)]) {
                    [self.customTabBar setSelectedIndex:self.selectedIndex];
                }
            }
        }];
    } else {
        self.customTabBar.hidden = hidden;
        self.customTabBar.alpha = hidden ? 0 : 1;
        if (!hidden) {
            if ([self.customTabBar respondsToSelector:@selector(setSelectedIndex:)]) {
                [self.customTabBar setSelectedIndex:self.selectedIndex];
            }
        }
    }
}

- (void)setSelectedIndex:(NSUInteger)selectedIndex {
    [super setSelectedIndex:selectedIndex];
    if ([self.customTabBar respondsToSelector:@selector(setSelectedIndex:)]) {
        [self.customTabBar setSelectedIndex:selectedIndex];
    }
}

@end 