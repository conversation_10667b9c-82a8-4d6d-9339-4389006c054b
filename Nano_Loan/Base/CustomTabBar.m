#import "CustomTabBar.h"

@interface CustomTabBar ()
@property (nonatomic, strong) NSArray<UIButton *> *buttons;
@property (nonatomic, strong) UIImageView *bgImageView;
@property (nonatomic, assign) NSInteger currentIndex;
@end

@implementation CustomTabBar

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    // 浮岛宽高设置
    CGFloat screenWidth = [UIScreen mainScreen].bounds.size.width;
    CGFloat screenHeight = [UIScreen mainScreen].bounds.size.height;
    CGFloat horizontalMargin = 13.0; // 左右边距各13pt
    CGFloat tabBarWidth = screenWidth - (horizontalMargin * 2); // 宽度自适应
    CGFloat tabBarHeight = 80.0; // 固定高度80pt
    CGFloat btnWidth = tabBarWidth / 3.0; // 按钮宽度等分

    // 背景图片
    self.bgImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"icon_bar_gb"]];
    self.bgImageView.contentMode = UIViewContentModeScaleToFill;
    self.bgImageView.frame = CGRectMake(0, 0, tabBarWidth, tabBarHeight);
    self.bgImageView.layer.cornerRadius = 15.0;
    self.bgImageView.layer.masksToBounds = YES;
    [self addSubview:self.bgImageView];
    [self sendSubviewToBack:self.bgImageView];

    // 可选：如果背景图已包含阴影和圆角，可去掉下面的手动样式
    self.backgroundColor = [UIColor clearColor];

    // 如果仍需圆角阴影，保留以下自定义样式
    self.layer.cornerRadius = 15;
    self.layer.masksToBounds = NO;
    self.layer.shadowColor = [UIColor blackColor].CGColor;
    self.layer.shadowOpacity = 0.2;
    self.layer.shadowOffset = CGSizeMake(0, 4);
    self.layer.shadowRadius = 16;
    
    NSArray *iconNames = @[@"icon_home", @"icon_order", @"icon_profile"];
    NSArray *selectedIconNames = @[@"icon_home_s", @"icon_order_s", @"icon_profile_s"];
    NSMutableArray *btns = [NSMutableArray array];
    
    // 居中显示在底部，并预留安全区域
    CGFloat bottomSafeArea = 34; // iPhone X及以上机型底部安全区域高度，可根据实际情况调整
    if (@available(iOS 11.0, *)) {
        UIWindow *window = UIApplication.sharedApplication.windows.firstObject;
        bottomSafeArea = window.safeAreaInsets.bottom;
    }
    self.frame = CGRectMake(horizontalMargin, screenHeight - tabBarHeight - bottomSafeArea - 10, tabBarWidth, tabBarHeight);
    
    for (int i = 0; i < 3; i++) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        // 按钮宽度等分，高度填满
        btn.frame = CGRectMake(i * btnWidth, 0, btnWidth, tabBarHeight);
        
        // 只显示图标，无文字
        UIImage *normalImg = [UIImage imageNamed:iconNames[i]];
        UIImage *selectedImg = [UIImage imageNamed:selectedIconNames[i]];
        // 未选中高度41pt，选中高度80pt，宽度等比例缩放
        CGFloat normalHeight = 41.0;
        CGFloat selectedHeight = 75;
        CGSize normalSize = [self sizeForImage:normalImg targetHeight:normalHeight];
        CGSize selectedSize = [self sizeForImage:selectedImg targetHeight:selectedHeight];
        [btn setImage:[self image:normalImg scaledToSize:normalSize] forState:UIControlStateNormal];
        [btn setImage:[self image:selectedImg scaledToSize:selectedSize] forState:UIControlStateSelected];
        btn.titleLabel.text = @"";
        btn.titleEdgeInsets = UIEdgeInsetsZero;
        btn.imageEdgeInsets = UIEdgeInsetsZero;
        btn.imageView.contentMode = UIViewContentModeCenter;
        // 居中显示大图标
        btn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentCenter;
        btn.contentVerticalAlignment = UIControlContentVerticalAlignmentCenter;
        
        btn.tag = i;
        [btn addTarget:self action:@selector(tabBtnTapped:) forControlEvents:UIControlEventTouchUpInside];
        [self addSubview:btn];
        [btns addObject:btn];
    }
    self.buttons = btns;

    // 默认选中第一个
    [self setSelectedIndex:0];
}

- (void)tabBtnTapped:(UIButton *)sender {
    [self setSelectedIndex:sender.tag];
    if ([self.delegate respondsToSelector:@selector(customTabBarDidSelectIndex:)]) {
        [self.delegate customTabBarDidSelectIndex:sender.tag];
    }
}

- (void)setSelectedIndex:(NSInteger)index {
    // 修复：无论index是否等于currentIndex，都刷新按钮选中状态，保证外部调用时同步高亮
    self.currentIndex = index;
    for (UIButton *btn in self.buttons) {
        btn.selected = NO;
        btn.alpha = 0.7;
    }
    if (index >= 0 && index < self.buttons.count) {
        UIButton *selectedBtn = self.buttons[index];
        selectedBtn.selected = YES;
        selectedBtn.alpha = 1.0;
    }
}

// 工具方法：图片缩放到指定尺寸
- (UIImage *)image:(UIImage *)image scaledToSize:(CGSize)size {
    if (!image) return nil;
    UIGraphicsBeginImageContextWithOptions(size, NO, 0.0);
    [image drawInRect:CGRectMake(0, 0, size.width, size.height)];
    UIImage *newImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return newImage;
}

// 工具方法：根据目标高度等比例缩放图片
- (CGSize)sizeForImage:(UIImage *)image targetHeight:(CGFloat)targetHeight {
    if (!image) return CGSizeZero;
    CGFloat aspect = image.size.width / image.size.height;
    return CGSizeMake(targetHeight * aspect, targetHeight);
}

@end 
