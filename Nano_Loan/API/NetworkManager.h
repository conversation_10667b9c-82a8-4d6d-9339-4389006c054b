#import <Foundation/Foundation.h>

typedef void(^NetworkCompletion)(NSDictionary * _Nullable response, NSError * _Nullable error);

@interface NetworkManager : NSObject

/// 设置基础域名
+ (void)setBaseURL:(NSString *)baseURL;

/// 发送网络请求
/// @param apiPath API路径（不含域名）
/// @param params 参数字典
/// @param method 请求方式（GET/POST）
/// @param completion 完成回调，返回JSON字典或错误
+ (void)requestWithAPI:(NSString *)apiPath
               params:(NSDictionary *)params
               method:(NSString *)method
           completion:(NetworkCompletion)completion;

/// 发送POST表单请求（from-data）
+ (void)postFormWithAPI:(NSString *)apiPath
                params:(NSDictionary *)params
             completion:(NetworkCompletion)completion;

/// 使用multipart/form-data上传图片
/// @param apiPath API路径（不含域名）
/// @param params 参数字典
/// @param imageKey 图片参数名
/// @param imageData 图片数据
/// @param completion 完成回调，返回JSON字典或错误
+ (void)uploadImageWithAPI:(NSString *)apiPath
                   params:(NSDictionary *)params
                 imageKey:(NSString *)imageKey
                imageData:(NSData *)imageData
               completion:(NetworkCompletion)completion;

/// 为给定的 NSURLComponents 追加固定公共参数（App 版本、设备信息、SessionId 等）
/// @discussion 目前用于 H5WebViewController 以及其他需要手动拼接 URL 的场景。
+ (void)appendCommonParamsToComponents:(NSURLComponents *)components;

@end 