#import "NetworkManager.h"
#import <AFNetworking/AFNetworking.h>
#import <UIKit/UIKit.h>
#import <AdSupport/AdSupport.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import <Security/Security.h>
#import "DeviceIDManager.h"
#include <sys/utsname.h>

// MARK: - Device & App Info Helpers (static C functions)
static NSString *AppVersion(void) {
    return [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"] ?: @"";
}

static NSString *DeviceModel(void) {
    struct utsname systemInfo;
    uname(&systemInfo);
    return [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding] ?: @"";
}

static NSString *OSVersion(void) {
    return [[UIDevice currentDevice] systemVersion] ?: @"";
}

// 已移动到 DeviceIDManager 类中

static NSString *AdvertisingID(void) {
    // 如果未授权或关闭跟踪，返回空字符串
    if (@available(iOS 14.0, *)) {
        ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];
        if (status == ATTrackingManagerAuthorizationStatusAuthorized) {
            return [[[ASIdentifierManager sharedManager] advertisingIdentifier] UUIDString] ?: @"";
        }
    } else {
        if ([[ASIdentifierManager sharedManager] isAdvertisingTrackingEnabled]) {
            return [[[ASIdentifierManager sharedManager] advertisingIdentifier] UUIDString] ?: @"";
        }
    }
    return @"";
}

static void AppendCommonParamsToComponents(NSURLComponents *components) {
    if (!components) return;

    NSMutableArray<NSURLQueryItem *> *items = components.queryItems ? [components.queryItems mutableCopy] : [NSMutableArray array];
    NSSet<NSString *> *existingNames = [NSSet setWithArray:[items valueForKey:@"name"]];

    // 公共参数
    NSMutableDictionary<NSString *, NSString *> *common = [@{ @"youknow": AppVersion(),
                                                            @"hang": DeviceModel(),
                                                            @"tyou": [DeviceIDManager persistentIDFV],
                                                            @"wet": OSVersion(),
                                                            @"thrown": AdvertisingID() } mutableCopy];

    // token（SessionId），如果存在则一起拼接
    NSString *token = [[NSUserDefaults standardUserDefaults] stringForKey:@"token"];
    if (token.length > 0) {
        common[@"andwere"] = token;
    }

    // 将缺失的公共参数追加到 URL 中
    for (NSString *key in common) {
        if (![existingNames containsObject:key] && common[key].length > 0) {
            [items addObject:[NSURLQueryItem queryItemWithName:key value:common[key]]];
        }
    }
    components.queryItems = items;
}

@implementation NetworkManager

static NSString *kBaseURL = @"http://************:8330/youtoday";

+ (void)setBaseURL:(NSString *)baseURL {
    kBaseURL = [baseURL copy];
}

+ (void)appendCommonParamsToComponents:(NSURLComponents *)components {
    AppendCommonParamsToComponents(components);
}

+ (void)requestWithAPI:(NSString *)apiPath
                params:(NSDictionary *)params
                method:(NSString *)method
            completion:(NetworkCompletion)completion {
    if (!kBaseURL || kBaseURL.length == 0) {
        if (completion) {
            completion(nil, [NSError errorWithDomain:@"NetworkManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"基础域名未设置"}]);
        }
        return;
    }
    NSMutableDictionary *mutableParams = params ? [params mutableCopy] : [NSMutableDictionary dictionary];

    NSString *urlString = [kBaseURL stringByAppendingPathComponent:apiPath];

    // 拼接公共参数
    NSURLComponents *components = [NSURLComponents componentsWithString:urlString];
    AppendCommonParamsToComponents(components);
    urlString = components.URL.absoluteString;

    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    manager.responseSerializer = [AFJSONResponseSerializer serializer];
    manager.requestSerializer = [AFJSONRequestSerializer serializer];
    manager.requestSerializer.timeoutInterval = 15;
    NSLog(@"[NetworkManager] 请求: %@\n参数: %@", apiPath, mutableParams);
    void (^successBlock)(NSURLSessionDataTask *, id) = ^(NSURLSessionDataTask *task, id responseObject) {
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSLog(@"[NetworkManager] 响应: %@\n结果: %@", apiPath, responseObject);
            if (completion) completion(responseObject, nil);
        } else {
            NSLog(@"[NetworkManager] 响应格式错误: %@\n结果: %@", apiPath, responseObject);
            if (completion) completion(nil, [NSError errorWithDomain:@"NetworkManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: @"返回数据格式错误"}]);
        }
    };
    void (^failureBlock)(NSURLSessionDataTask *, NSError *) = ^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"[NetworkManager] 请求失败: %@\n错误: %@", apiPath, error);
        if (completion) completion(nil, error);
    };
    if ([[method uppercaseString] isEqualToString:@"GET"]) {
        [manager GET:urlString parameters:mutableParams headers:nil progress:nil success:successBlock failure:failureBlock];
    } else if ([[method uppercaseString] isEqualToString:@"POST"]) {
        [manager POST:urlString parameters:mutableParams headers:nil progress:nil success:successBlock failure:failureBlock];
    } else {
        if (completion) completion(nil, [NSError errorWithDomain:@"NetworkManager" code:-3 userInfo:@{NSLocalizedDescriptionKey: @"不支持的请求方式"}]);
    }
}

/// 发送POST表单请求（from-data）
/// @param apiPath API路径
/// @param params 参数字典
/// @param completion 完成回调
+ (void)postFormWithAPI:(NSString *)apiPath
                 params:(NSDictionary *)params
              completion:(NetworkCompletion)completion {
    if (!kBaseURL || kBaseURL.length == 0) {
        if (completion) {
            completion(nil, [NSError errorWithDomain:@"NetworkManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"基础域名未设置"}]);
        }
        return;
    }
    // no extra body params for form upload, 使用传入的 params 即可

    NSString *urlString = [kBaseURL stringByAppendingPathComponent:apiPath];
    NSURLComponents *components = [NSURLComponents componentsWithString:urlString];
    AppendCommonParamsToComponents(components);
    urlString = components.URL.absoluteString;
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    manager.responseSerializer = [AFJSONResponseSerializer serializer];
    manager.requestSerializer = [AFHTTPRequestSerializer serializer]; // 表单方式
    manager.requestSerializer.timeoutInterval = 15;
    NSLog(@"[NetworkManager] 表单请求: %@\n参数: %@", apiPath, params);
    [manager POST:urlString parameters:params headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSLog(@"[NetworkManager] 表单响应: %@\n结果: %@", apiPath, responseObject);
            if (completion) completion(responseObject, nil);
        } else {
            NSLog(@"[NetworkManager] 表单响应格式错误: %@\n结果: %@", apiPath, responseObject);
            if (completion) completion(nil, [NSError errorWithDomain:@"NetworkManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: @"返回数据格式错误"}]);
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        NSLog(@"[NetworkManager] 表单请求失败: %@\n错误: %@", apiPath, error);
        if (completion) completion(nil, error);
    }];
}

/// 使用multipart/form-data上传图片
/// @param apiPath API路径
/// @param params 参数字典
/// @param imageKey 图片参数名
/// @param imageData 图片数据
/// @param completion 完成回调
+ (void)uploadImageWithAPI:(NSString *)apiPath
                   params:(NSDictionary *)params
                 imageKey:(NSString *)imageKey
                imageData:(NSData *)imageData
               completion:(NetworkCompletion)completion {
    if (!kBaseURL || kBaseURL.length == 0) {
        if (completion) {
            completion(nil, [NSError errorWithDomain:@"NetworkManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"基础域名未设置"}]);
        }
        return;
    }
    
    // no extra body params for image upload
    
    NSString *urlString = [kBaseURL stringByAppendingPathComponent:apiPath];
    NSURLComponents *components = [NSURLComponents componentsWithString:urlString];
    AppendCommonParamsToComponents(components);
    urlString = components.URL.absoluteString;
    
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    manager.responseSerializer = [AFJSONResponseSerializer serializer];
    manager.requestSerializer = [AFHTTPRequestSerializer serializer]; // 表单方式
    manager.requestSerializer.timeoutInterval = 30; // 上传文件可能需要更长的超时时间
    
    NSLog(@"[NetworkManager] 图片上传请求: %@\n参数: %@", apiPath, params);
    
    [manager POST:urlString parameters:params headers:nil constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
        // 添加图片数据，使用当前时间戳作为文件名，避免重名
        NSString *fileName = [NSString stringWithFormat:@"image_%ld.jpg", (long)[[NSDate date] timeIntervalSince1970]];
        [formData appendPartWithFileData:imageData
                                    name:imageKey
                                fileName:fileName
                                mimeType:@"image/jpeg"];
        
    } progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSLog(@"[NetworkManager] 图片上传响应: %@\n结果: %@", apiPath, responseObject);
            if (completion) completion(responseObject, nil);
        } else {
            NSLog(@"[NetworkManager] 图片上传响应格式错误: %@\n结果: %@", apiPath, responseObject);
            if (completion) completion(nil, [NSError errorWithDomain:@"NetworkManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: @"返回数据格式错误"}]);
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        NSLog(@"[NetworkManager] 图片上传请求失败: %@\n错误: %@", apiPath, error);
        if (completion) completion(nil, error);
    }];
}

@end 
