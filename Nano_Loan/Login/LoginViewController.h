//
//  LoginViewController.h
//  Nano_Loan
//
//  Created by yongsheng ye on 2025/6/11.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface LoginViewController : UIViewController

@property (nonatomic, strong) UITextField *phoneTextField;   // 手机号输入框
@property (nonatomic, strong) UITextField *codeTextField;    // 验证码输入框
@property (nonatomic, strong) UIButton *getCodeButton;       // 获取验证码按钮
@property (nonatomic, strong) UIButton *getVoiceButton;      // 获取语音验证码按钮
@property (nonatomic, strong) NSTimer *timer;              // 倒计时定时器
@property (nonatomic, assign) NSInteger secondsLeft;       // 剩余秒数
@property (nonatomic, strong) UIButton *privacyButton;     // 隐私勾选按钮
@property (nonatomic, strong) UIButton *loginButton;       // 登录按钮

@end

NS_ASSUME_NONNULL_END

//登录页
//输入手机号页面
//调用数字键盘
//验证码页面（可能没有 具体根据UI ）
//可返回上一页"输入手机号"页
//进入页面自动获取验证码，获取验证码按钮进入倒计时60秒，按钮不可点击。验证码倒计时结束按钮可点击，点击发送验证码，发送成功，提示"后端返回的msg"。
//点击获取语音验证码，获取语音验证码，发送成功，提示"后端返回的msg"。
//协议默认勾选，如果未勾选提示 印尼：Harap membaca dan menyetujui kebijakan privasi   英语：Please read and agree to the Privacy Policy
//"登录成功"进入首页。"登录失败"Toast提示错误信息，验证码输入框抖动"0.5s"，并清空验证码输入框。
//注意：
//未输入手机号：提示：印尼：Silakan masukkan nomor telepon Anda 英语：Please enter your phone number
// 未输入验证码：提示：印尼：Silakan masukkan kode verifikasi          英语：Please enter the verification code
