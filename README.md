# Nano Loan

## 项目概述

Nano Loan 是一款面向个人用户的小额贷款管理应用，旨在提供快捷、安全的借贷及账务管理体验。

## 页面结构

| 页面/视图名称 | 用途 | 核心功能 | 技术实现 | 导航/用户流程 | 建议文件路径 |
|:--------:|:----:|:--------:|:--------:|:--------:|:--------:|
| SettingsView | 应用设置与账户管理 | Logout、Delete Account、自定义弹窗 | UIKit + AutoLayout | 主页 → 设置 → 弹窗交互 | Nano_Loan/Setting/SettingViewController.m |
| PersonalInformationView | 个人信息认证 | 教育、婚姻状态、邮箱、城市等字段填写与选择 | UIKit + UITableView | 认证页 -> 个人信息填写 -> 保存返回 | Nano_Loan/Certification/PersonalInformationAuthenticationViewController.m |
| IDTypePickerView | 证件类型选择 | 显示推荐与其他ID类型列表，选择后回调 | UIKit + AutoLayout | IdCardAuthenticationView -> 弹窗呈现 | Nano_Loan/Certification/IDTypePickerViewController.m |

## 开发状态跟踪

| 页面/组件名称 | 开发状态 | 文件路径 |
|:-------------:|:--------:|:------------:|
| SettingsView | 已完成（含退出登录/注销 API） | Nano_Loan/Setting/SettingViewController.m |
| PersonalInformationView | 进行中 | Nano_Loan/Certification/PersonalInformationAuthenticationViewController.m |
| IDTypePickerView | 进行中 | Nano_Loan/Certification/IDTypePickerViewController.m |

## 技术实现细节

### IDTypePickerView

#### UI设计方案

- 使用模糊背景 + 半透明遮罩，提升聚焦感。
- 内容区域为两个分组面板："Recommended ID Type" 与 "Other Types"，分别使用设计稿提供的上、下半背景图。
- 每个证件类型使用自定义按钮，背景图为 cell 背景切图，左侧标题，右侧箭头。
- 整体使用 UIStackView 垂直排列，支持内容过多时滚动。

#### 数据管理方案

- 通过初始化方法注入 `primaryIDs`、`secondaryIDs` 数组。
- 点击任意证件类型后回调 `selectionHandler` 传递所选类型。

#### 交互实现

- 页面以 `UIModalPresentationOverCurrentContext` 模式淡入呈现。
- 背景点击或选择完毕后淡出动画关闭。
- 点击证件类型立即关闭并回调。

#### iOS特性利用

- 使用 `UIVisualEffectView` 实现毛玻璃遮罩。
- 使用 AutoLayout + UIStackView 自适应布局。

#### 可访问性考虑

- 为按钮设置 `accessibilityLabel` 与 `accessibilityHint`。
- 保留系统动态字体兼容。

#### 组件复用

- 面板与按钮均封装为可重用方法，供后续类似弹窗调用。

#### 功能完整性检查表

 - 展示推荐 ID 列表
 - 展示其他 ID 列表
 - 支持滚动
 - 点击任一 ID 调用回调并关闭页面
 - 点击背景关闭页面 

# Photo Source Picker 实现指南

## 资源文件命名清单

为实现身份证上传选择弹窗，需要准备以下资源文件：

1. **photo_source_picker_bg.png**
   - 弹窗主背景图
   - 尺寸: 375 x 455 pts (设计稿尺寸)
   - 特点: 渐变紫色到浅蓝色背景

2. **photo_source_picker_close.png**
   - 右上角关闭按钮图标
   - 尺寸: 30 x 30 pts
   - 特点: X形状图标，白色

3. **photo_source_picker_id_illustration.png**
   - 身份证卡通图片
   - 尺寸: 约240 x 160 pts
   - 特点: 身份证样式图片，包含照片占位符

4. **photo_source_picker_tip_bg.png** (可选，当前使用纯色背景)
   - 提示文本背景
   - 尺寸: 约327 x 80 pts
   - 特点: 蓝色半透明背景

5. **photo_source_picker_camera_btn_bg.png**
   - 相机按钮背景
   - 尺寸: 约150 x 60 pts
   - 特点: 紫到蓝渐变按钮背景

6. **photo_source_picker_album_btn_bg.png**
   - 相册按钮背景
   - 尺寸: 约150 x 60 pts
   - 特点: 紫到蓝渐变按钮背景

## 实现说明

自定义弹窗已通过 `PhotoSourcePickerView` 类实现，替换了原有的系统弹窗。该弹窗有以下特点：

1. 全屏半透明背景，点击可关闭弹窗
2. 从屏幕底部弹出的卡片式面板，顶部为圆角设计
3. 适配底部安全区域，确保在全面屏设备上显示正常
4. 顶部标题和关闭按钮
5. 中间区域显示身份证示意图
6. 下方显示提示文本
7. 底部显示"Camera"和"Album"按钮（根据waydown参数决定是否显示相册选项）
8. 平滑的弹出与收回动画效果

## 使用方法

```objective-c
// 初始化并显示弹窗
PhotoSourcePickerView *pickerView = [[PhotoSourcePickerView alloc] initWithAllowAlbum:YES completion:^(PhotoSourceType selectedType) {
    // 处理用户选择
    if (selectedType == PhotoSourceTypeCamera) {
        // 处理相机选择
    } else {
        // 处理相册选择
    }
}];

[pickerView showInView:self.view animated:YES];
```

## 布局调整指南

当添加真实资源后，可能需要进行以下微调：

1. 调整背景图尺寸比例，确保显示正常
2. 调整身份证示意图的尺寸和位置，使其居中显示
3. 调整提示文本的大小和颜色，确保可读性
4. 调整按钮的大小、间距和文本样式，保持一致性
5. 检查底部安全区域适配，确保在不同型号的iPhone上显示正常 