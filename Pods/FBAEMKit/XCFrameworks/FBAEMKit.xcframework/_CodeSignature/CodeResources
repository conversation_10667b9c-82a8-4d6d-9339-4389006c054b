<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>LICENSE</key>
		<data>
		42oX9oAD1yj/wA6aqkh1wyx+qqA=
		</data>
		<key>ios-arm64/FBAEMKit.framework/FBAEMKit</key>
		<data>
		WTRlaeKyBLAO55fQbiWRcTpaOQk=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Headers/FBAEMKit-Swift.h</key>
		<data>
		4zf532sGeXalpVW5+571AH5HAm4=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Info.plist</key>
		<data>
		+WTgA+7knpfKPINVxRyDIWwkT7g=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		jIpzsulpmfli0PLQZ881X3f86+w=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		agq0dRMiRRRhBhYFXAWYfMLH4XM=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		XWt6Wn5P9A035TaMVR1dtbnSV5M=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		agq0dRMiRRRhBhYFXAWYfMLH4XM=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Modules/module.modulemap</key>
		<data>
		Ci3QIdviXpIzxSC88i1rGvW2cSs=
		</data>
		<key>ios-arm64/FBAEMKit.framework/PrivacyInfo.xcprivacy</key>
		<data>
		Fo7sebV/R02g8kqyPtqICO8eVyI=
		</data>
		<key>ios-arm64/FBAEMKit.framework/_CodeSignature/CodeResources</key>
		<data>
		16HEEFU8jB0nYr/8Ma3tFmRP0fY=
		</data>
		<key>ios-arm64/dSYMs/FBAEMKit.framework.dSYM/Contents/Info.plist</key>
		<data>
		1JG6jb8xWGFC2k9rN+LZtMMd+J0=
		</data>
		<key>ios-arm64/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/DWARF/FBAEMKit</key>
		<data>
		Pf3msVOgcJPxd4ukjevFVjT2yXc=
		</data>
		<key>ios-arm64/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBAEMKit.yml</key>
		<data>
		UUX1Y2nMXd+G7rDjsIAUa10VSeQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/FBAEMKit</key>
		<data>
		LyT8uIR6Tk6rp8t8ozQFNorxq7o=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Headers/FBAEMKit-Swift.h</key>
		<data>
		DSTW5xcVrndsThDrooe6v4sWabA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<data>
		jIpzsulpmfli0PLQZ881X3f86+w=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		xlwkYJnGmxqQD4oMqzXuXxvxAKM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<data>
		spVB3z8nYVVUwLv1ko0w2XVekH0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<data>
		xlwkYJnGmxqQD4oMqzXuXxvxAKM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<data>
		jIpzsulpmfli0PLQZ881X3f86+w=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		bRCEoKeVP944/H4214NNck4rguM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<data>
		gO4eTa3IKqfDjVw89NbSzzdDT+Q=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<data>
		bRCEoKeVP944/H4214NNck4rguM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		Ci3QIdviXpIzxSC88i1rGvW2cSs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Resources/Info.plist</key>
		<data>
		YVYwhM8oJSLwQ3NR4t7pa1kgRpk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<data>
		Fo7sebV/R02g8kqyPtqICO8eVyI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/_CodeSignature/CodeResources</key>
		<data>
		UfurAYBO2W8KecX7eekfG5MNfLs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Info.plist</key>
		<data>
		1JG6jb8xWGFC2k9rN+LZtMMd+J0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/DWARF/FBAEMKit</key>
		<data>
		Zyk+4iof1PsKfxZMBTpIBgSHdRs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBAEMKit.yml</key>
		<data>
		KZlTk7rl7jnIDHr+XwexavcW6wo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBAEMKit.yml</key>
		<data>
		BgM8N4dWtlU1z2zFwLcNz4YF4c8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/FBAEMKit</key>
		<data>
		QPVi4drWVJKovxpAvaHOhKRGgqY=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Headers/FBAEMKit-Swift.h</key>
		<data>
		DSTW5xcVrndsThDrooe6v4sWabA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Info.plist</key>
		<data>
		ZBuK065vx2Dan+mc+y4hEjYBoMs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		jIpzsulpmfli0PLQZ881X3f86+w=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		MTxAt6ZgnwDKZ7FFQWghq6D7PGs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		synp6/Qeas50FWdWiV/xTBRXXOA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		MTxAt6ZgnwDKZ7FFQWghq6D7PGs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		jIpzsulpmfli0PLQZ881X3f86+w=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		dYVvQfkyT0tPlgsdWIo0G3W1czw=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		ixyz4f13PEQwnPgnz+7ZHp1UFlw=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		dYVvQfkyT0tPlgsdWIo0G3W1czw=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/module.modulemap</key>
		<data>
		Ci3QIdviXpIzxSC88i1rGvW2cSs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/PrivacyInfo.xcprivacy</key>
		<data>
		Fo7sebV/R02g8kqyPtqICO8eVyI=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/_CodeSignature/CodeResources</key>
		<data>
		ggtr+WLTm5yZV8PfdvjF9LeLyH0=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Info.plist</key>
		<data>
		1JG6jb8xWGFC2k9rN+LZtMMd+J0=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/DWARF/FBAEMKit</key>
		<data>
		Nk4R2bG0k9oJSja99aJ5JDJxRT4=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBAEMKit.yml</key>
		<data>
		kqaR8cgMQ2ZPajiWTIxRz/vKXe4=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBAEMKit.yml</key>
		<data>
		1UAjztsnE/+H0AB5S838+5eATdc=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>LICENSE</key>
		<dict>
			<key>hash</key>
			<data>
			42oX9oAD1yj/wA6aqkh1wyx+qqA=
			</data>
			<key>hash2</key>
			<data>
			JGiNyKThXtEPUCL2A80E+FzHN+UTW+RkFoApZE8iHm8=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/FBAEMKit</key>
		<dict>
			<key>hash</key>
			<data>
			WTRlaeKyBLAO55fQbiWRcTpaOQk=
			</data>
			<key>hash2</key>
			<data>
			gA/otmj9Zz0g+caOpbbQaGdV8OoBcKTwXepE/Is972Q=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Headers/FBAEMKit-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			4zf532sGeXalpVW5+571AH5HAm4=
			</data>
			<key>hash2</key>
			<data>
			S+XkUgAnIX9HsxKjEhajisxTEcsmMilxmZPyrKuCxBA=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			+WTgA+7knpfKPINVxRyDIWwkT7g=
			</data>
			<key>hash2</key>
			<data>
			Ch/fZsF3K+d1gHFG9DOkGCvwxABO0zmwC2nkrv9Vx5Y=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			jIpzsulpmfli0PLQZ881X3f86+w=
			</data>
			<key>hash2</key>
			<data>
			2uD0lnw+T6Jd0pHhXMKuAg2PP77QPbLIloKG1FIqxYI=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			agq0dRMiRRRhBhYFXAWYfMLH4XM=
			</data>
			<key>hash2</key>
			<data>
			JQD2YAPbbhnu0EoAgszfFtARID5Xwjj9++xC/BZHMk8=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			XWt6Wn5P9A035TaMVR1dtbnSV5M=
			</data>
			<key>hash2</key>
			<data>
			hRqKOqvRufa7361kyALjHvjB5xO638aRbOC5mBTw7HY=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			agq0dRMiRRRhBhYFXAWYfMLH4XM=
			</data>
			<key>hash2</key>
			<data>
			JQD2YAPbbhnu0EoAgszfFtARID5Xwjj9++xC/BZHMk8=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			Ci3QIdviXpIzxSC88i1rGvW2cSs=
			</data>
			<key>hash2</key>
			<data>
			jq+nSulDKiN4tenILjgXg82TzM040TCurYFTCVRKXxM=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			Fo7sebV/R02g8kqyPtqICO8eVyI=
			</data>
			<key>hash2</key>
			<data>
			ZFIpWmrSklTJLGaAOPLGos/UQMB82oH4FOmWrCFbhBU=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			16HEEFU8jB0nYr/8Ma3tFmRP0fY=
			</data>
			<key>hash2</key>
			<data>
			VYOnjsZ7KwB0D9OJzIqGvoQya4T1fJOJX+afuUTOKnc=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBAEMKit.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			1JG6jb8xWGFC2k9rN+LZtMMd+J0=
			</data>
			<key>hash2</key>
			<data>
			pjMbQsi4lmbGLDGfnWY/TdC3qk0dRFwlgBWtKTNNRMc=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/DWARF/FBAEMKit</key>
		<dict>
			<key>hash</key>
			<data>
			Pf3msVOgcJPxd4ukjevFVjT2yXc=
			</data>
			<key>hash2</key>
			<data>
			fSRxxVjVmURmW5u8hP4jRQmENQoaSC/ZRLfeciCsUdg=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBAEMKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			UUX1Y2nMXd+G7rDjsIAUa10VSeQ=
			</data>
			<key>hash2</key>
			<data>
			ANhf7QAN9qNm2m0tO5jvvTllL9dqc9xSvENG/xWwD1k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/FBAEMKit</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/FBAEMKit</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/FBAEMKit</key>
		<dict>
			<key>hash</key>
			<data>
			LyT8uIR6Tk6rp8t8ozQFNorxq7o=
			</data>
			<key>hash2</key>
			<data>
			ZqVChHb//5OsA0IYmyMS1fwD8tuHeaMk2i2PMvcuUcA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Headers/FBAEMKit-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			DSTW5xcVrndsThDrooe6v4sWabA=
			</data>
			<key>hash2</key>
			<data>
			ExT0qOgPAxGylyRJEWPIwQ0mqWOmyszTJ2hdRUsEejM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			jIpzsulpmfli0PLQZ881X3f86+w=
			</data>
			<key>hash2</key>
			<data>
			2uD0lnw+T6Jd0pHhXMKuAg2PP77QPbLIloKG1FIqxYI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			xlwkYJnGmxqQD4oMqzXuXxvxAKM=
			</data>
			<key>hash2</key>
			<data>
			0AUAKHeF8Q9xow8TNwyQuzjTvfX6pt5PF7UHiHJopRw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			spVB3z8nYVVUwLv1ko0w2XVekH0=
			</data>
			<key>hash2</key>
			<data>
			yIQ9bAo1mmQRtiTSNdMM1jxnfJ2do/KenMGTNhI9lyo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			xlwkYJnGmxqQD4oMqzXuXxvxAKM=
			</data>
			<key>hash2</key>
			<data>
			0AUAKHeF8Q9xow8TNwyQuzjTvfX6pt5PF7UHiHJopRw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			jIpzsulpmfli0PLQZ881X3f86+w=
			</data>
			<key>hash2</key>
			<data>
			2uD0lnw+T6Jd0pHhXMKuAg2PP77QPbLIloKG1FIqxYI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			bRCEoKeVP944/H4214NNck4rguM=
			</data>
			<key>hash2</key>
			<data>
			mNbUxSF9uqvHPdhCZ5C7RuJyuBG4WRLX0HOXlCFSuY8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			gO4eTa3IKqfDjVw89NbSzzdDT+Q=
			</data>
			<key>hash2</key>
			<data>
			okJxVPaYPU/BWT9SsPLeVZtMTze5K9ODVjXdIci6jxQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			bRCEoKeVP944/H4214NNck4rguM=
			</data>
			<key>hash2</key>
			<data>
			mNbUxSF9uqvHPdhCZ5C7RuJyuBG4WRLX0HOXlCFSuY8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			Ci3QIdviXpIzxSC88i1rGvW2cSs=
			</data>
			<key>hash2</key>
			<data>
			jq+nSulDKiN4tenILjgXg82TzM040TCurYFTCVRKXxM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			YVYwhM8oJSLwQ3NR4t7pa1kgRpk=
			</data>
			<key>hash2</key>
			<data>
			amXpiEMyl6op/f10zO+rS2VcexyhNbTY12j6YxYUSPw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			Fo7sebV/R02g8kqyPtqICO8eVyI=
			</data>
			<key>hash2</key>
			<data>
			ZFIpWmrSklTJLGaAOPLGos/UQMB82oH4FOmWrCFbhBU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			UfurAYBO2W8KecX7eekfG5MNfLs=
			</data>
			<key>hash2</key>
			<data>
			+yLigzpsuH8RcFgDHyaCTAN0kEq6SyRRbzioXWTA7Nk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			1JG6jb8xWGFC2k9rN+LZtMMd+J0=
			</data>
			<key>hash2</key>
			<data>
			pjMbQsi4lmbGLDGfnWY/TdC3qk0dRFwlgBWtKTNNRMc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/DWARF/FBAEMKit</key>
		<dict>
			<key>hash</key>
			<data>
			Zyk+4iof1PsKfxZMBTpIBgSHdRs=
			</data>
			<key>hash2</key>
			<data>
			7h9fyX9LfPXaG0iWPYm00bGv7G5aut2qsNi67zG+IVM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBAEMKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			KZlTk7rl7jnIDHr+XwexavcW6wo=
			</data>
			<key>hash2</key>
			<data>
			8tkLHlQn2FUsXG1orzzqSlgpC9Uilw4ppQXlxFNVfFU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBAEMKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			BgM8N4dWtlU1z2zFwLcNz4YF4c8=
			</data>
			<key>hash2</key>
			<data>
			BYuYHDM2vZGMyTD2eivaYFmd1rxS18FnFTIRDQm8Qrk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/FBAEMKit</key>
		<dict>
			<key>hash</key>
			<data>
			QPVi4drWVJKovxpAvaHOhKRGgqY=
			</data>
			<key>hash2</key>
			<data>
			vS6wtxFsoA/QgRNHCWFHanAyfBLLBT9WNR4gvKvGJBE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Headers/FBAEMKit-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			DSTW5xcVrndsThDrooe6v4sWabA=
			</data>
			<key>hash2</key>
			<data>
			ExT0qOgPAxGylyRJEWPIwQ0mqWOmyszTJ2hdRUsEejM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			ZBuK065vx2Dan+mc+y4hEjYBoMs=
			</data>
			<key>hash2</key>
			<data>
			uBrngziNXh10Hn035YmckKy7jvU94jdHkdsPQjG7X+g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			jIpzsulpmfli0PLQZ881X3f86+w=
			</data>
			<key>hash2</key>
			<data>
			2uD0lnw+T6Jd0pHhXMKuAg2PP77QPbLIloKG1FIqxYI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			MTxAt6ZgnwDKZ7FFQWghq6D7PGs=
			</data>
			<key>hash2</key>
			<data>
			ZfIGN0Q/x3nVuxntAY6Gq/+n/1f40gT/bdJKmdy6oGc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			synp6/Qeas50FWdWiV/xTBRXXOA=
			</data>
			<key>hash2</key>
			<data>
			2VsMY1HNMbXii8ciNbwM4D35tKXE7R3nU3wL6B752gM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			MTxAt6ZgnwDKZ7FFQWghq6D7PGs=
			</data>
			<key>hash2</key>
			<data>
			ZfIGN0Q/x3nVuxntAY6Gq/+n/1f40gT/bdJKmdy6oGc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			jIpzsulpmfli0PLQZ881X3f86+w=
			</data>
			<key>hash2</key>
			<data>
			2uD0lnw+T6Jd0pHhXMKuAg2PP77QPbLIloKG1FIqxYI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			dYVvQfkyT0tPlgsdWIo0G3W1czw=
			</data>
			<key>hash2</key>
			<data>
			/h2l+NH0of7I/sMebx0224zca+82k/05hGMOGVMDG+E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			ixyz4f13PEQwnPgnz+7ZHp1UFlw=
			</data>
			<key>hash2</key>
			<data>
			VayeiXnrQLd37lGpjKQifOFM88Lh3NlzWj/DBHzd3Ms=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			dYVvQfkyT0tPlgsdWIo0G3W1czw=
			</data>
			<key>hash2</key>
			<data>
			/h2l+NH0of7I/sMebx0224zca+82k/05hGMOGVMDG+E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			Ci3QIdviXpIzxSC88i1rGvW2cSs=
			</data>
			<key>hash2</key>
			<data>
			jq+nSulDKiN4tenILjgXg82TzM040TCurYFTCVRKXxM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			Fo7sebV/R02g8kqyPtqICO8eVyI=
			</data>
			<key>hash2</key>
			<data>
			ZFIpWmrSklTJLGaAOPLGos/UQMB82oH4FOmWrCFbhBU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			ggtr+WLTm5yZV8PfdvjF9LeLyH0=
			</data>
			<key>hash2</key>
			<data>
			QbfDkujOjesVA2RiBH7Yyg369RVyWt5aj4vgbc6WYIU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			1JG6jb8xWGFC2k9rN+LZtMMd+J0=
			</data>
			<key>hash2</key>
			<data>
			pjMbQsi4lmbGLDGfnWY/TdC3qk0dRFwlgBWtKTNNRMc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/DWARF/FBAEMKit</key>
		<dict>
			<key>hash</key>
			<data>
			Nk4R2bG0k9oJSja99aJ5JDJxRT4=
			</data>
			<key>hash2</key>
			<data>
			ZWnhxwZGMN93RXeXUOq2w1E8gn66RE8gSrBMUUbNvbA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBAEMKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			kqaR8cgMQ2ZPajiWTIxRz/vKXe4=
			</data>
			<key>hash2</key>
			<data>
			Pku/PDfpjZbf1sCCXI5AbqEQioOpm3FC8Ab0R0X3eeY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBAEMKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			1UAjztsnE/+H0AB5S838+5eATdc=
			</data>
			<key>hash2</key>
			<data>
			fbQLyep8YO+xKCAzZVuz7lcprHHTXYU28ou6Eb+uxOA=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
