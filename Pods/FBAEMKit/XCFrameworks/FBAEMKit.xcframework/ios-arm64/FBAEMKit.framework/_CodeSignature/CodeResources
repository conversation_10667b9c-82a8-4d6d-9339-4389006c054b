<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FBAEMKit-Swift.h</key>
		<data>
		4zf532sGeXalpVW5+571AH5HAm4=
		</data>
		<key>Info.plist</key>
		<data>
		+WTgA+7knpfKPINVxRyDIWwkT7g=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		jIpzsulpmfli0PLQZ881X3f86+w=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		agq0dRMiRRRhBhYFXAWYfMLH4XM=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		XWt6Wn5P9A035TaMVR1dtbnSV5M=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		agq0dRMiRRRhBhYFXAWYfMLH4XM=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		Ci3QIdviXpIzxSC88i1rGvW2cSs=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		Fo7sebV/R02g8kqyPtqICO8eVyI=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FBAEMKit-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			S+XkUgAnIX9HsxKjEhajisxTEcsmMilxmZPyrKuCxBA=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2uD0lnw+T6Jd0pHhXMKuAg2PP77QPbLIloKG1FIqxYI=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			JQD2YAPbbhnu0EoAgszfFtARID5Xwjj9++xC/BZHMk8=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			hRqKOqvRufa7361kyALjHvjB5xO638aRbOC5mBTw7HY=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			JQD2YAPbbhnu0EoAgszfFtARID5Xwjj9++xC/BZHMk8=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			jq+nSulDKiN4tenILjgXg82TzM040TCurYFTCVRKXxM=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ZFIpWmrSklTJLGaAOPLGos/UQMB82oH4FOmWrCFbhBU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
