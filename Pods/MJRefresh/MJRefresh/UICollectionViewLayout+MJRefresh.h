//
//  UICollectionViewLayout+MJRefresh.h
//  
//  该类是用来解决 Footer 在底端加载完成后, 仍停留在原处的 bug.
//  此问题出现在 iOS 14 及以下系统上.
//  Reference: https://github.com/CoderMJLee/MJRefresh/issues/1552
//
//  Created by <PERSON><PERSON><PERSON> on 2021/11/15.
//  Copyright © 2021 小码哥. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UICollectionViewLayout (MJRefresh)

@end

NS_ASSUME_NONNULL_END
