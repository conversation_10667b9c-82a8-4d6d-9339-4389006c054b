// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXAggregateTarget section */
		28907903AF6DD0AD321D92CD660E7E23 /* FBSDKCoreKit */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = C320C79E1F381838923FC2BF4FAB74A1 /* Build configuration list for PBXAggregateTarget "FBSDKCoreKit" */;
			buildPhases = (
				C05801FB1115AB56CF9E73BB6786A73A /* [CP] Copy XCFrameworks */,
			);
			dependencies = (
				63F9A7FD383742B5297CAB1A76AEBBAE /* PBXTargetDependency */,
				166551A15307C14C93C75A280E83F037 /* PBXTargetDependency */,
			);
			name = FBSDKCoreKit;
		};
		479BDBD165CD08E2159181E58CD2078F /* FBAEMKit */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 442545805DAF10650DBA7C00DEAAD47B /* Build configuration list for PBXAggregateTarget "FBAEMKit" */;
			buildPhases = (
				A261CEA870C48433C3575F1CCC2BB166 /* [CP] Copy XCFrameworks */,
			);
			dependencies = (
				3AE4598BB741766C8A9F7423056CE411 /* PBXTargetDependency */,
			);
			name = FBAEMKit;
		};
		8EA62FEAB23070050EFD4D4508D659E5 /* FBSDKCoreKit_Basics */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = AB73C55C047FFED8C4B6CB08922D32ED /* Build configuration list for PBXAggregateTarget "FBSDKCoreKit_Basics" */;
			buildPhases = (
				A760E42366676CF529D7FA67FA8558B9 /* [CP] Copy XCFrameworks */,
			);
			dependencies = (
			);
			name = FBSDKCoreKit_Basics;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		013EF29548D769A6B936884AE5C2535A /* SDWebImageOptionsProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = A287ED27D4E42096ACC7083DED4FA0DF /* SDWebImageOptionsProcessor.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		0312D6B222E4A663DFE3ED35CAE14421 /* AFImageDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 578DD43BD787E90101B75BEF788BA255 /* AFImageDownloader.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		042D40751BD2F51FBE9FECD4707CBBE9 /* SDDeviceHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 6973C74BF1F22A9F975FC92306896A8F /* SDDeviceHelper.h */; settings = {ATTRIBUTES = (Private, ); }; };
		0453019EC6578A67B82CF569EC765546 /* SDFileAttributeHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = F4FB9E39D97BFFA46C61B3A9007DD77E /* SDFileAttributeHelper.h */; settings = {ATTRIBUTES = (Private, ); }; };
		05AE79C2626475CFDA15C800FFEAEC4B /* AFNetworkActivityIndicatorManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 46B98724BC7B3F413DF4AA065977E113 /* AFNetworkActivityIndicatorManager.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		05E2B7C1DB7528A0BBEA1521BE0DBAF1 /* MASViewAttribute.h in Headers */ = {isa = PBXBuildFile; fileRef = EAF2695AE98027DE219427774EA00F4C /* MASViewAttribute.h */; settings = {ATTRIBUTES = (Public, ); }; };
		08719ABCE689ED74FE7486B1E49DAA6C /* MJRefreshBackStateFooter.h in Headers */ = {isa = PBXBuildFile; fileRef = EF3F7035FE6372BC0DAD6729D5E56FDF /* MJRefreshBackStateFooter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		089F3C4BAA46A37EC5763DD312771021 /* SDImageIOAnimatedCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = A9B2427495CFF658EF228D79DC6D6749 /* SDImageIOAnimatedCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		08BCAAADA3FA095D1BD15B8263004228 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ********18AC31E3A631F72434F9EAC1 /* Foundation.framework */; };
		08D4F37046B05382A7823C78F89B0ACC /* MASConstraintMaker.m in Sources */ = {isa = PBXBuildFile; fileRef = 090F99E04964E3730A77393230B37658 /* MASConstraintMaker.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		0921DE69D3845C45AC5A01C8734D2751 /* SDImageLoader.m in Sources */ = {isa = PBXBuildFile; fileRef = D9FFC56D1B71CF4225F1020056F519FA /* SDImageLoader.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		09A2ACBC8CE1761652EAA20886AEFE10 /* SDImageCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = D2DF114CE345AFA2323BF027E9EC94E6 /* SDImageCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0A5C13680D1476AD861EE31A969378A9 /* UIImage+MemoryCacheCost.m in Sources */ = {isa = PBXBuildFile; fileRef = 36A6004F83217C098FE98F2CC5DD3F85 /* UIImage+MemoryCacheCost.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		0A681F5B1D3611879DAC60D8F5FA2DFD /* SDImageGIFCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 656A4AF3B8CCC51FCDB104A2D59BF3ED /* SDImageGIFCoder.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		0AFE4714903E897473AB28AD0D9E7B07 /* UIImageView+HighlightedWebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 30F34249C4EBD69AF03F67D07C9F4AD7 /* UIImageView+HighlightedWebCache.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		0F2F96E0860F1AB7064896E0D2B0901C /* SDWebImageDownloaderRequestModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = D38DA2F32B1FE67EAC59B5B3571FB671 /* SDWebImageDownloaderRequestModifier.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		10017B43AC38C3A89D7AC1376C6E7066 /* SDImageLoadersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = C17DCDE0A27B622E3B23EA0B0551331E /* SDImageLoadersManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		125C88474A190605BBD3FAE7E42E627A /* Masonry-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = D29D20F2B4EA95072041BBBE9BDFD161 /* Masonry-dummy.m */; };
		13F68E946EC25038A0F858F9A6BA5DF8 /* UIButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5F065F05B624EFE063B302D95CE170 /* UIButton+WebCache.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		14CA284AC4FF1EED75E785641EE98034 /* SDImageCacheConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 6902BD7A42E4D258F1245D623260283D /* SDImageCacheConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		152254941AEACDB6527E28D67508ABF5 /* SDFileAttributeHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = E91C86FE61FCDE43F30523D916FBA3FF /* SDFileAttributeHelper.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		15E4D04155F21B3C06E2914DCBEB1487 /* UIActivityIndicatorView+AFNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = A41F5F108B4DE69CA413C9B0149F76BC /* UIActivityIndicatorView+AFNetworking.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		165F1C9CBD621828C788A3018D0426C5 /* SDImageAPNGCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 2C788B033735C02C5668C07553CA91FA /* SDImageAPNGCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		16D7DCB7CC985C33EEC41B371C029C84 /* SDWebImage-SDWebImage in Resources */ = {isa = PBXBuildFile; fileRef = CF1281E58AA1045D4B7F33FC56691C42 /* SDWebImage-SDWebImage */; };
		1830558A4D2D63C8E76BC3136D8213F9 /* UIImage+ExtendedCacheData.h in Headers */ = {isa = PBXBuildFile; fileRef = 5910E5BF6CA1CE68DC180FD07454DA57 /* UIImage+ExtendedCacheData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		18AD90784D549657DF51BC8377DA3085 /* SDWebImageDownloaderResponseModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = 7342B4C7AEDEEE66ED1D6D3FE4971057 /* SDWebImageDownloaderResponseModifier.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1B6CE67196EE181E6B56788EFC7E00D3 /* SDImageGIFCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 538A95AED784C754FF197437BFCE6686 /* SDImageGIFCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1B8A9603A3F8D9A7BCD3B6B026448F84 /* SDWebImageError.m in Sources */ = {isa = PBXBuildFile; fileRef = 451768D7F5A52CCC3C00D92A8A6C245F /* SDWebImageError.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		1C8B70C74291A3076746C3B18781568E /* SDImageCachesManagerOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 49EC12F59B1E51AF51B523248388A148 /* SDImageCachesManagerOperation.h */; settings = {ATTRIBUTES = (Private, ); }; };
		1CBA380B90208FD5350F66ADEEC1A0F1 /* SDAnimatedImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 100AECAC3BB237B7815607A9620170E6 /* SDAnimatedImageView+WebCache.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		20D618EF3EA5E3BE96DA24D36E3CA9EF /* SDAsyncBlockOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 1EFBC7CEE060C7FDF33DC6282F25269F /* SDAsyncBlockOperation.h */; settings = {ATTRIBUTES = (Private, ); }; };
		22516EA77E7120000632C30BD9A03927 /* UIScrollView+MJExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = 25839EACDB320C59013B336E48C9C385 /* UIScrollView+MJExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		23C0A5C619043400F23B3F042BDA80D0 /* AFURLSessionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 10BE79645E38972233986B0051941E7D /* AFURLSessionManager.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		23CE18E13FAB699AD5A19CA3DDC7FB2E /* Pods-Nano_Loan-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 736D36DEEA2F92CE85F489EBEAB55B3A /* Pods-Nano_Loan-dummy.m */; };
		24B75682B8B059FD919A8407F7D38E5A /* SDImageCachesManager.m in Sources */ = {isa = PBXBuildFile; fileRef = D16C947CD037B530ACA64CF549B70870 /* SDImageCachesManager.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		28BA9702905AA2B4C1E9E4878032D4E4 /* MJRefreshConst.h in Headers */ = {isa = PBXBuildFile; fileRef = BF77135EF3E2C3897AD8A453DDCD2BAB /* MJRefreshConst.h */; settings = {ATTRIBUTES = (Public, ); }; };
		29F7F0E98FD26A96364DBACD7D5F237A /* SDWebImageDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AB98E5B2A7F5B2EB08A105196863D7E /* SDWebImageDownloader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2AF772FC9A2744BC34B5A8892FF2B945 /* MJRefreshBackNormalFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 559D20A4B808010925C4AEEF42CBC9F8 /* MJRefreshBackNormalFooter.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		2B84004AB1221A608BD462E8D4BA341C /* SDWebImagePrefetcher.m in Sources */ = {isa = PBXBuildFile; fileRef = 93819A9FA4A1C9364BC4CFAF2C21E59B /* SDWebImagePrefetcher.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		2BC106322877BDD113AD9391AA501613 /* SDWebImageIndicator.m in Sources */ = {isa = PBXBuildFile; fileRef = F785F6D19FBAEF6E55D8C96685444712 /* SDWebImageIndicator.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		2DDD48230ED9E8068C7E439D79B99A8E /* SDInternalMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = ABF45CB970D10C85DDD6F87EE3595DFA /* SDInternalMacros.h */; settings = {ATTRIBUTES = (Private, ); }; };
		2F5A2FD069FBABDE30591A84DF9FB88D /* SDWebImageDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = 76C91FE790D5E160FC8F3E26FB1E9853 /* SDWebImageDefine.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		2FC2D532909A53348881E18D363F57B0 /* UIView+WebCacheState.m in Sources */ = {isa = PBXBuildFile; fileRef = BAFB84126C2A92409B3357591908FE47 /* UIView+WebCacheState.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		3008B1104322076167E9533A611D1604 /* WKWebView+AFNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = C20507CBFBAC4CD433F3590977845574 /* WKWebView+AFNetworking.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		31A02B30F6A0125668E95F05DCB21A81 /* SDWebImageCompat.m in Sources */ = {isa = PBXBuildFile; fileRef = D78ECA51F4A5AAB9B6983388DF80A57D /* SDWebImageCompat.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		320CFA02302E98A8C408A0786C049821 /* SDDeviceHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = D62DCCA4E0332332E4B9024D47190CFB /* SDDeviceHelper.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		325CA20B9271F3E008234E1518B79061 /* MJRefresh-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = E88BBCCAB699750CA8370D016E6AA34D /* MJRefresh-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		327BA3DDA513422E632D3DA4A8FC60EC /* MJRefresh-MJRefresh.Privacy in Resources */ = {isa = PBXBuildFile; fileRef = 7E3097CFEFDA621E9FB0E62009FF87FC /* MJRefresh-MJRefresh.Privacy */; };
		328DC444E3E85145A1D3DF091E2C225B /* SDWebImageCacheKeyFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = 366488E341C4071AAD5962E9517BD973 /* SDWebImageCacheKeyFilter.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		3297535CDED0B0870EE0D89965656805 /* SDAnimatedImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = F07AC49F259A08F316B54C038F1CF2A1 /* SDAnimatedImageView.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		32ACEDCEBE0507A82D6323114A1C74F1 /* UIImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 20E960E84CE2327E5B7BA6948C92CFCE /* UIImageView+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		34B28D4F0168194B6EFAC0520EB7A7F4 /* NSImage+Compatibility.h in Headers */ = {isa = PBXBuildFile; fileRef = 4B954FC92C73D230D63697C0223C8528 /* NSImage+Compatibility.h */; settings = {ATTRIBUTES = (Public, ); }; };
		34BA423491607EBBD666F70032FCE826 /* MJRefreshComponent.m in Sources */ = {isa = PBXBuildFile; fileRef = D71FE77CEB4D8192D475D25D5199DEDF /* MJRefreshComponent.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		36F4B09E7C71DCC5CEC6057814033C37 /* UIView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 9B9368B43F10AB162CE33D27BE62BB8E /* UIView+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3777CD89D444CBBB48AE323B303F3FC7 /* ImageIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DDE8BFE78F9CD7EE55A53388621C5B35 /* ImageIO.framework */; };
		******************************** /* UIColor+SDHexString.m in Sources */ = {isa = PBXBuildFile; fileRef = 31AE83AF573F7CE65709672553568710 /* UIColor+SDHexString.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		37B890ABDC7DD441E6AA662325D412E6 /* MASConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 229416BAB314DD8212DA96597D6D6620 /* MASConstraint.h */; settings = {ATTRIBUTES = (Public, ); }; };
		37F1B9B70F767DA30596A3BA6644E41F /* UIImage+Metadata.m in Sources */ = {isa = PBXBuildFile; fileRef = 62F86770F9F19B6F695C8E60B1C5D5D9 /* UIImage+Metadata.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		3A1AD84C0DC3C256418CC46739024E96 /* SDmetamacros.h in Headers */ = {isa = PBXBuildFile; fileRef = C18BD45EC5652DDEFE3B39AA4266A1D7 /* SDmetamacros.h */; settings = {ATTRIBUTES = (Private, ); }; };
		3A2FCB914F6EADED828FF05F7E9132AE /* UIView+MJExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = 554EA878CA6FFC285AB0718A975AFD1B /* UIView+MJExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3B8EDFF69A68ABD3735E0C6931CA5C95 /* AFURLSessionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = D41021804D6338C082E57D172E3A8035 /* AFURLSessionManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		******************************** /* SDWebImageOptionsProcessor.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D22908AF899046E620B6B9CA46CA0F /* SDWebImageOptionsProcessor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3C7EAECB8C573E714C818BA04EB33773 /* UIImage+MultiFormat.h in Headers */ = {isa = PBXBuildFile; fileRef = 6DA0E3AD4FC1D6F3B6C93543690C92BA /* UIImage+MultiFormat.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3C8F2F868D0C361CAF43E53CDB8EB631 /* SDWebImageCacheSerializer.h in Headers */ = {isa = PBXBuildFile; fileRef = FFD7FCE84BFE35C3BAE280490C66B7DE /* SDWebImageCacheSerializer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3E0356FE057BF02F1430BD873F35B6B1 /* SDDisplayLink.m in Sources */ = {isa = PBXBuildFile; fileRef = 886538B601C2BFB290390681C7D2764D /* SDDisplayLink.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		3EB9FFA38676CB17C4149A603286FEFD /* MJRefreshConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = C940CCDF172766EA9C6444C47C443E02 /* MJRefreshConfig.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		40B4B682DBC23D33222753DA25D49641 /* UIImage+Transform.m in Sources */ = {isa = PBXBuildFile; fileRef = 5C0E534E1E01F40F44CF32572740B441 /* UIImage+Transform.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		42D19F96A647042273F246D0364C3B77 /* SDMemoryCache.m in Sources */ = {isa = PBXBuildFile; fileRef = BD91C34529A0E7613D392C3C61079114 /* SDMemoryCache.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		442F468E261A1106C291BF52BDBF9DB7 /* MJRefreshHeader.h in Headers */ = {isa = PBXBuildFile; fileRef = F3200DCC36729B205E9E18CAF440B2A9 /* MJRefreshHeader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		44CD842019B1CEA681F820F37A30B7C4 /* SDImageFramePool.h in Headers */ = {isa = PBXBuildFile; fileRef = 28CE361547B2B6EAD184FC4F74FFFE2C /* SDImageFramePool.h */; settings = {ATTRIBUTES = (Private, ); }; };
		4571A0EA37DC84F39E3830D38A1531AB /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5D42D5E2CF5F6BD84D4A821D89CC11D /* UIKit.framework */; };
		45E1583D7EF53489B82C4CA2AD1AD0CF /* MJRefreshBackFooter.h in Headers */ = {isa = PBXBuildFile; fileRef = 92E1B71DDBEC7BB36DCE14247966D279 /* MJRefreshBackFooter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4688743B7B845309486559EB7BD5D147 /* SDWebImageCompat.h in Headers */ = {isa = PBXBuildFile; fileRef = E33C1FBC36FFBCFACED96EB026CF2791 /* SDWebImageCompat.h */; settings = {ATTRIBUTES = (Public, ); }; };
		475B4F3E71C293065AAFDB1888696CF6 /* MJRefreshBackGifFooter.h in Headers */ = {isa = PBXBuildFile; fileRef = 88123D6FAA396D9AF75F25750F8D3EC1 /* MJRefreshBackGifFooter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4838DBCD6667728EAE284532D8B21E10 /* SDImageTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 993021AD23EF5ACC22BB15271B4EDF7D /* SDImageTransformer.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		49590B0067E8A602534A1AE38C2C27FB /* SDImageFramePool.m in Sources */ = {isa = PBXBuildFile; fileRef = F113E21168660F663B4A5426E37C33E8 /* SDImageFramePool.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		4A2D7878579537881DD3859B3359F702 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F2CA872A5138F1B8EF538FC1C4109152 /* CoreGraphics.framework */; };
		4B2C2AE16AE3DDA7417AFCF7952588F1 /* SDImageAssetManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 7E071087C7C250FD44AB3134AC332F07 /* SDImageAssetManager.h */; settings = {ATTRIBUTES = (Private, ); }; };
		4BC2CFC679C78670D7CCAB0CB5FEF32C /* UIRefreshControl+AFNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = 31534ED5D5D783AC06EBB8479FA2A640 /* UIRefreshControl+AFNetworking.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		4D02BAA1BD88757C9D6AC6951AFB668C /* SDImageHEICCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = E57CAA7DF42DCCC758F15642B6D17ED3 /* SDImageHEICCoder.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		4D459564B62CBFD801DB06B13C9EC93A /* MJRefreshStateHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 8687FBF3DABDC6266C65F5DBF9DA9F19 /* MJRefreshStateHeader.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		4DC7640E40A36308776A77D54DA5307E /* SDGraphicsImageRenderer.m in Sources */ = {isa = PBXBuildFile; fileRef = 05AE5833538310BC194D7C9EEC7FC83F /* SDGraphicsImageRenderer.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		4DCA75BFE1558CE59DFC56607E49B3D2 /* MJRefreshConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 5AB02EF7397807AB656C9DD19DA7AD29 /* MJRefreshConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4E01EC96AEC8B92FF3443CCAA2584DA6 /* MASConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 75A7F78D8D7CF1E9C7E6FBEA0EC09C95 /* MASConstraint.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		4E5CEF5269F568F729B61E77FA228958 /* SDImageCodersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0587A346147AAEABC7D97135E800FC85 /* SDImageCodersManager.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		4ED05DB3E43FF6AE1FA22130B2B50F05 /* UIImage+MemoryCacheCost.h in Headers */ = {isa = PBXBuildFile; fileRef = 522173517F8D5E0CFDB0813826D9B87E /* UIImage+MemoryCacheCost.h */; settings = {ATTRIBUTES = (Public, ); }; };
		50423028356F5C71A09C26626F681777 /* MBProgressHUD.m in Sources */ = {isa = PBXBuildFile; fileRef = 349A8C7FE5264237269403A5B812657A /* MBProgressHUD.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		506FC58999564A737C745F2590E9B4D5 /* AFHTTPSessionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 586C7D767724357618422B52C865DA28 /* AFHTTPSessionManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5163FC6D715F6881B1FA1AB13DCEF870 /* UICollectionViewLayout+MJRefresh.h in Headers */ = {isa = PBXBuildFile; fileRef = FCAC3C7409CC871BC8709618A577BE33 /* UICollectionViewLayout+MJRefresh.h */; settings = {ATTRIBUTES = (Public, ); }; };
		523235228A1C021C67F2E3776A922DC5 /* MJRefreshTrailer.h in Headers */ = {isa = PBXBuildFile; fileRef = 6155E668105C1B6E682E91C5E4BA598B /* MJRefreshTrailer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		52749628BF96B939F9125BB1FDDE3CE3 /* SDAnimatedImagePlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 45232534F89924F94768CE145BD803CF /* SDAnimatedImagePlayer.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		53433003112C4FE271EC985803862B61 /* SDWebImageCacheKeyFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = 023B4F18A51DEDE66731835742BD6088 /* SDWebImageCacheKeyFilter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		54A7D3D251FC888FA93ADDCE7865AE2F /* UIScrollView+MJExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 7096A2E55F0A442D8366AD0ABDE705E3 /* UIScrollView+MJExtension.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		561420A20DC0A84258A902E9EB69A15A /* MJRefreshAutoFooter.h in Headers */ = {isa = PBXBuildFile; fileRef = 5E7D3F2658C5FF9BC63C19C146238EF0 /* MJRefreshAutoFooter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		564D02061AE23EF6021261CB5C141E70 /* MJRefreshAutoNormalFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = DC0F93774456DC84A27D91E611DBD126 /* MJRefreshAutoNormalFooter.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		573B039F8868FFFCEF8CDCC4FB9819C8 /* UICollectionViewLayout+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = C5D8C13D979E5F303CCEDE97C2D9A8C1 /* UICollectionViewLayout+MJRefresh.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		58F7CE37BB4CB3BE806B68A502E6E1A7 /* SDWeakProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = F6B8FCBF3C86AD3E30D912CCA23848C4 /* SDWeakProxy.h */; settings = {ATTRIBUTES = (Private, ); }; };
		597B38597411E71CC422895F08A745AD /* UIButton+AFNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = EC81FB402799CE870531BA35336C6B62 /* UIButton+AFNetworking.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		5AF22814CD055B553AD9D78BE54B94E1 /* UIProgressView+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = CDF9C8AABF582CFFD2CFDF8FB53E3981 /* UIProgressView+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5BB6B99986FD7111B3AEBE931C7F507B /* MJRefreshAutoStateFooter.h in Headers */ = {isa = PBXBuildFile; fileRef = 9BF0B319ECC588E630BB061703763EFE /* MJRefreshAutoStateFooter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5C8279C226EB028B044C5A0F4AC5A91A /* SDAssociatedObject.h in Headers */ = {isa = PBXBuildFile; fileRef = 6E676D489B33C5238C15B404E6605BD9 /* SDAssociatedObject.h */; settings = {ATTRIBUTES = (Private, ); }; };
		5CDD4C746A3580335B6004AF7631A7CF /* MJRefreshBackFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 5CDE70A924EAFD031E709AA8BF825A1F /* MJRefreshBackFooter.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		5CFB9A7B5E7A245241F4AD04879437FF /* SDImageFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = 2A6CB6C698E97EE524E3D37B9CFBB489 /* SDImageFrame.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		5DCBA14510E091D6A1CE499B08B794B5 /* UIImage+Metadata.h in Headers */ = {isa = PBXBuildFile; fileRef = A610EBE491953793B751C4215E5AA105 /* UIImage+Metadata.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5DFCBADAC7D0FAC82C84A6C8E7BF1DA6 /* MJRefreshStateHeader.h in Headers */ = {isa = PBXBuildFile; fileRef = EF6DD8858AE767C8C8BB967DC9C7303B /* MJRefreshStateHeader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5E01DE8DD929768591CFA26F8659FFF0 /* UIScrollView+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = B0B6059943320F774EC2A322398B3F12 /* UIScrollView+MJRefresh.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		5E10328A83E05D0015D7459FAAEF121D /* SDGraphicsImageRenderer.h in Headers */ = {isa = PBXBuildFile; fileRef = 59D22431E34837621987CFA0E071C9C8 /* SDGraphicsImageRenderer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5F45735DF355530CC955066D3C007E19 /* MASViewConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 415AE4EBD078041AB7A08362BF1EC94C /* MASViewConstraint.h */; settings = {ATTRIBUTES = (Public, ); }; };
		61461B0D9D7B81C3F8D24066D9A19DCE /* MJRefreshGifHeader.h in Headers */ = {isa = PBXBuildFile; fileRef = E2EDFEF94C31FF176835B73A1AEA3F1D /* MJRefreshGifHeader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		61507E402F1F7C58BF119995A0479A22 /* NSArray+MASShorthandAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = FF97549E8708AB5DB55C6D68DA24E7E6 /* NSArray+MASShorthandAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		617B7E27B82DAACC48F39DDB1A1527CE /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 714D2BDD00D34C29FFD5B0A3D547DBD6 /* PrivacyInfo.xcprivacy */; };
		64A640196239E834DE6731EE7CB7A673 /* SDImageCachesManagerOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 4ADFEFE6DD12D5AF909630EE100337C5 /* SDImageCachesManagerOperation.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		676775CB29378BB6CA3CA5992E9C6A99 /* SDImageIOAnimatedCoderInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = 9710F83AEA028B961B1E26291C7FB804 /* SDImageIOAnimatedCoderInternal.h */; settings = {ATTRIBUTES = (Private, ); }; };
		67C0A2CDC97B42A303AE34BF316C2C8C /* SDAnimatedImage.m in Sources */ = {isa = PBXBuildFile; fileRef = 8F750F6FA6B5CD517C0919E7A5E1AFA8 /* SDAnimatedImage.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		69345CBCB31076EBF8A2C5885AF973AB /* MJRefreshComponent.h in Headers */ = {isa = PBXBuildFile; fileRef = FCE3EBF3E49A379F42253639B3871893 /* MJRefreshComponent.h */; settings = {ATTRIBUTES = (Public, ); }; };
		69AB6A513D5F36D7360FEF4FDA1D60D0 /* UIView+WebCacheState.h in Headers */ = {isa = PBXBuildFile; fileRef = 956F17F95447DE027B38056025CD7579 /* UIView+WebCacheState.h */; settings = {ATTRIBUTES = (Public, ); }; };
		69E353C99C6EEA3C93CCF2E526460B9D /* UIScrollView+MJRefresh.h in Headers */ = {isa = PBXBuildFile; fileRef = A71EAA491D66A6EEFC169902BD0F4070 /* UIScrollView+MJRefresh.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6A19379E3B0370EDA447743C9B1A1379 /* UIImageView+HighlightedWebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = D2FE687682EA2A0096442C12099F18D6 /* UIImageView+HighlightedWebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6B5C3592B5E911E833D067D0BC785B1A /* SDImageFrame.h in Headers */ = {isa = PBXBuildFile; fileRef = D70DB7D95CF4D6B925A4592F4D4F736F /* SDImageFrame.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C9286C64D83FA635B84DDF89EEE597B /* SDImageAssetManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 88AE48064E7607C59F3469C1A20DCF9B /* SDImageAssetManager.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		6E66305665DBCFBCF5B2480BF705D500 /* SDWebImageTransition.h in Headers */ = {isa = PBXBuildFile; fileRef = 0E3AC68774EBD70CA88496DBD3A19FF6 /* SDWebImageTransition.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6F4848A4A2336FAD34BEDCBA91BEEF49 /* NSBundle+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 0B7C6A23D76E609B238E5B495F86DA9F /* NSBundle+MJRefresh.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		7074EA7FCC90B4967A437F5C43496828 /* SDDisplayLink.h in Headers */ = {isa = PBXBuildFile; fileRef = D838629758902C8EAA9209E37E6D7986 /* SDDisplayLink.h */; settings = {ATTRIBUTES = (Private, ); }; };
		711D32EF4A9901567A488291603BF906 /* SDWebImage.h in Headers */ = {isa = PBXBuildFile; fileRef = 5DFB730C1C7E7CED7E6C55F3B9ADD4DB /* SDWebImage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		71BEB1D9532900291A5A24B1C038516F /* UIColor+SDHexString.h in Headers */ = {isa = PBXBuildFile; fileRef = 08C3D6EA978F6FF57BE3D91B257A18F6 /* UIColor+SDHexString.h */; settings = {ATTRIBUTES = (Private, ); }; };
		72225C63C4CA3F910FC7784DCE35940E /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = F10494B4D30C762999F0D759F798F5B2 /* PrivacyInfo.xcprivacy */; };
		723D1925C39764631641D0DBFDF6833B /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ********18AC31E3A631F72434F9EAC1 /* Foundation.framework */; };
		724991CA89C46BAFBC08264D94D86484 /* AFURLRequestSerialization.h in Headers */ = {isa = PBXBuildFile; fileRef = 524B06850BC1DB525CF4376D77312BE6 /* AFURLRequestSerialization.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7277143439291D1B631DFD7C5CA4481B /* SDWebImageDownloaderDecryptor.m in Sources */ = {isa = PBXBuildFile; fileRef = C869AA260FDC04C618F3773DAE02A5E0 /* SDWebImageDownloaderDecryptor.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		73A82F328FA7386A4D227872619BB283 /* MASCompositeConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = F31EA86CBB965ECA0C1C299704FBBB5A /* MASCompositeConstraint.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		740AB16421665B56B23671CEEC2C504D /* Pods-Nano_Loan-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 17CF85A57080E64F02970A5FA4D3A2C2 /* Pods-Nano_Loan-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		75771A97B77FA30A0175A81B480F80EF /* UIImage+ForceDecode.h in Headers */ = {isa = PBXBuildFile; fileRef = 6102ED22B4FE0206A781C9D7BBA72EC3 /* UIImage+ForceDecode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		76E7308986FD31C25E217252CF49056E /* MASViewAttribute.m in Sources */ = {isa = PBXBuildFile; fileRef = DEFAD2C68AFA34BC1B2B97D756B6D000 /* MASViewAttribute.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		772CF8E9CD02ECA4275B6173E2110E80 /* View+MASShorthandAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 1B686371EF9EBDD85A19E25539F568BA /* View+MASShorthandAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		786A94A677B3D65FF397A4CCE3034387 /* MJRefresh-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 4E5EF39885B5235C036ACF039D66460F /* MJRefresh-dummy.m */; };
		7902D28FC9EF5AFEB452F508C7F266B1 /* MJRefreshAutoNormalFooter.h in Headers */ = {isa = PBXBuildFile; fileRef = 5BAF6786F6992480D509FB4DD1205969 /* MJRefreshAutoNormalFooter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7989A6E79BFA78440C39F568D972305C /* MJRefresh.h in Headers */ = {isa = PBXBuildFile; fileRef = 0EE55DD0D556C460D50C97617AEF9F18 /* MJRefresh.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7ADAC982EF440C408C37CB327EF7A9D5 /* SDWebImageDownloaderConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 307C93B0A95C9A9FA6B6318B6071CA56 /* SDWebImageDownloaderConfig.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		7C45DBA62EE045C4922404182F6393B8 /* SDWebImageError.h in Headers */ = {isa = PBXBuildFile; fileRef = 2737C54349985D603F3EEA20519667C6 /* SDWebImageError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7C5505A2D3F2A697A5F324787061F4B7 /* MASConstraint+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = C5B8C8565AFDF6696C00088B4CB745E2 /* MASConstraint+Private.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7F886FC2763F0BF1625A24EE4F94C04D /* UIRefreshControl+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = 9B70D149FCB49334BC2AD62CB5D1AB5A /* UIRefreshControl+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7FF8A56511E71D6FEC966BF9FEE135B5 /* AFNetworkActivityIndicatorManager.h in Headers */ = {isa = PBXBuildFile; fileRef = F36091220F6F67E1E1AC4B5A2C36A12E /* AFNetworkActivityIndicatorManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		813BE4C96A6D39C13EC50C6CD164F0AF /* MASConstraintMaker.h in Headers */ = {isa = PBXBuildFile; fileRef = 7CC519CE74C273E3B96F42EFFE0C8A29 /* MASConstraintMaker.h */; settings = {ATTRIBUTES = (Public, ); }; };
		81A5635CEA2AD9623E30CAE9AFC3BF65 /* NSBundle+MJRefresh.h in Headers */ = {isa = PBXBuildFile; fileRef = 5AB8C89747B63EC60AC17506677B66DC /* NSBundle+MJRefresh.h */; settings = {ATTRIBUTES = (Public, ); }; };
		83A4F2816C1B3F072E1A26A34C3BC4AC /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ********18AC31E3A631F72434F9EAC1 /* Foundation.framework */; };
		8414CFEEB64ACA817EB88D2FEADDA3B3 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ********18AC31E3A631F72434F9EAC1 /* Foundation.framework */; };
		84E2CF954781CBD9C07190929149983A /* SDWebImage-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 223C9B7435D107017AA4F01B178FC6A6 /* SDWebImage-dummy.m */; };
		854807558DCB972EDDFC1D00032BA6E4 /* SDWebImageDownloaderConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 53FA74C38ADE749EAB807D3090EF1373 /* SDWebImageDownloaderConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		860CB3A5D2E13B946CD2EFB7F749C4CF /* UIActivityIndicatorView+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = ******************************** /* UIActivityIndicatorView+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		87697EDFD230BDF5B6DAA611AC08626D /* MJRefreshStateTrailer.m in Sources */ = {isa = PBXBuildFile; fileRef = A633B1FF01FBEA9F83E6016A9B19C0FB /* MJRefreshStateTrailer.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		88473AE7C22F952DACB39FA0758D1624 /* SDMemoryCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 95F8CFDAAC7BE0E2115AD6EB7D3AE08E /* SDMemoryCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8A16FBAC5F4A39084245E51D177A5FA9 /* SDWebImageCacheSerializer.m in Sources */ = {isa = PBXBuildFile; fileRef = 5A5AF98857754B424A3A624850EDA0FB /* SDWebImageCacheSerializer.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		8AF38EDB1E9BF0D334AEB23C488870B8 /* NSData+ImageContentType.h in Headers */ = {isa = PBXBuildFile; fileRef = DA4B25ED4A76B2949F4873C5DB345946 /* NSData+ImageContentType.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8B14EFC1D98F43F78BC38ECCD66A14DA /* UIImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 8BBD5C0F4322D4AA4696F1A3643C8E80 /* UIImageView+WebCache.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		8C6C7E25C5A24C936F81823978190E96 /* ViewController+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 1555F5583D3F4900FE77515D7DEF159A /* ViewController+MASAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8D7E57CF1EF86090C37EDCA040FB8EBA /* SDImageCacheDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = 941BCD2DB2DF9181C4F226A0B361883C /* SDImageCacheDefine.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		8D8AD606ECD8E1F247965CD43956D412 /* UIImage+Transform.h in Headers */ = {isa = PBXBuildFile; fileRef = 81793BE920E56B9034C38119DCA4E2FF /* UIImage+Transform.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8EC9EF51F559247D472BC27AD2582EB4 /* SDImageCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 893098B9AC07B83F3D1078371E67A353 /* SDImageCoder.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		91AAF555B286FBF53E4F98D092B406BD /* SDWebImageTransitionInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = B61B81FAFC4064B4204502632063AE46 /* SDWebImageTransitionInternal.h */; settings = {ATTRIBUTES = (Private, ); }; };
		91E8B94F8E02ABF5197DF5AE7D0B3934 /* SDWebImageDownloaderDecryptor.h in Headers */ = {isa = PBXBuildFile; fileRef = 31C1E203148A786A2C4BFD6ED5BA7DF4 /* SDWebImageDownloaderDecryptor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		92213E59C653C52367B079C66AF5F3F1 /* MJRefreshAutoFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 97BB8B85E2684936285EDA6DE35E2037 /* MJRefreshAutoFooter.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		928371B066E1211CE87089668D5BCB4C /* SDDiskCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 76B1AD75019B89219529E41B4D9787BA /* SDDiskCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		933B1C5909851CEBFE69E48514849EDC /* MJRefreshFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = ABF6BF2E51E9B8A6C13440ABFBF05186 /* MJRefreshFooter.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		93F4FED694DA572489D514D9DDFEF0C1 /* AFURLResponseSerialization.m in Sources */ = {isa = PBXBuildFile; fileRef = 55F43F7B1F5F3CEA54FB3C532B7C1EFB /* AFURLResponseSerialization.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		95147B9C545E9B66F6B9BC200EB35DF9 /* UIImage+MultiFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = 54D2BD3770D5C912EE835B9FE481F98B /* UIImage+MultiFormat.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		9A7FB1E975A5955C896E6B195C521804 /* MJRefreshBackNormalFooter.h in Headers */ = {isa = PBXBuildFile; fileRef = 7232FDE2F429C285FBF950DD8CABC55F /* MJRefreshBackNormalFooter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9B3420DEB8A0CCB9E1241A669AEFCA8E /* SDAnimatedImage.h in Headers */ = {isa = PBXBuildFile; fileRef = F5E5245F32D37981F8CD1435A74E9A9C /* SDAnimatedImage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9B9343E8599EE5196BA75E842DCB48B7 /* NSBezierPath+SDRoundedCorners.h in Headers */ = {isa = PBXBuildFile; fileRef = 1DEC8F932E25A416DEC3337AB32F1DB6 /* NSBezierPath+SDRoundedCorners.h */; settings = {ATTRIBUTES = (Private, ); }; };
		9D422527A25BAE6A207DEFE11958ABBC /* AFCompatibilityMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = DC6809D81689953D9097375854A01017 /* AFCompatibilityMacros.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9DF446F8CA5BC4D4098766EC9063012C /* SDWebImageOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = F85B84FEEDC22CE1693E773B11192ED5 /* SDWebImageOperation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9E4310021331C49FF3D3CB28A88ABC50 /* SDImageCacheConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 564BA9CF40661B3FCD7E41191921750C /* SDImageCacheConfig.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		9E74186C38FAB93E6209E9BF8D4870F5 /* AFHTTPSessionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 74D75DCA93CEAA4E2B3B3B9C44385947 /* AFHTTPSessionManager.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		A0B5717DFED2312163B6222F293C2B52 /* UIImage+ExtendedCacheData.m in Sources */ = {isa = PBXBuildFile; fileRef = 0081A57C73420FA6768D1DF16C510D4F /* UIImage+ExtendedCacheData.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		A1560247914C760D9EE5F7A2392CC06C /* UIImage+GIF.h in Headers */ = {isa = PBXBuildFile; fileRef = 4FAAD439B70F4518579093867C8A582F /* UIImage+GIF.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A160E2FF8CC42AF58486C0A4AE34AA35 /* UIView+MJExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 3F7DDBBBA1F1DE3A57E40BB04EA2E4FD /* UIView+MJExtension.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		A1A1266FD70CEB545FD7602CC33F2F7B /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 84D74C5AA71CCA8E912BDC9060B36F2D /* QuartzCore.framework */; };
		A839428F403C52D8AA3466B65E20C27A /* NSButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 3022DE9ECACD33B1B6954AFBE01D1D43 /* NSButton+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A861F677DC3D972226F683C0C553FCF5 /* ViewController+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 6BB5FD74D69F8D9F92FD56A887310AEF /* ViewController+MASAdditions.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		A9A49E4A3BE8882F60DF32BAF39DE191 /* SDWebImageManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 722F2B52D360C4FF95D2EE305238F134 /* SDWebImageManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A9B6C4C3ED3EEA4DD0FFE76EEBD44E43 /* UIProgressView+AFNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = 2B3FF7138CEC0FBBE3857CD3ECDB4831 /* UIProgressView+AFNetworking.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		AAA12F449ABE87694D43AA22271FF125 /* UIView+WebCacheOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 63A7FA2BEE803D9B01F922BB4BA6B397 /* UIView+WebCacheOperation.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		ABCB80C4813C849FC93D57676820C907 /* SDImageCacheDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 96D04E50579217816FBB63FB994A57BC /* SDImageCacheDefine.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AC14E56ECA7A4980A8E1CA68E800B12C /* SDWebImagePrefetcher.h in Headers */ = {isa = PBXBuildFile; fileRef = 958AA3429F2DCCF0665BBA30CD319856 /* SDWebImagePrefetcher.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AC951F4137A3411BA2F7E01D0508858C /* AFNetworkReachabilityManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 943515464B1E8AEB6731548AB14F0F90 /* AFNetworkReachabilityManager.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		ACD87CF4B1D7D0A31EE549089E86787F /* SDImageAWebPCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = D8264159EC9493FE0E54E21D38D4B568 /* SDImageAWebPCoder.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		AE7B02645B8F769CA5F215EE8F7CC5B0 /* View+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = C06F04758C4A9410D32E65A09EC66D97 /* View+MASAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AF25A814FF0A0D65FF79EC152EC71BE2 /* SDWebImageOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = CBEDB9628337222BB9BC7F98B66F0BB8 /* SDWebImageOperation.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		B030B558BE97E0225652EFB8C8FA431F /* AFAutoPurgingImageCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 24FDB8B29FE3DE461E78F8FB5F2905BD /* AFAutoPurgingImageCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B331CE2D3DEB461E738B886086A365F9 /* SDImageGraphics.h in Headers */ = {isa = PBXBuildFile; fileRef = 5892ED39C08A09AB1A6D272451BAE5E8 /* SDImageGraphics.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B4B7E71635CF3BD9872E9E58393C970C /* SDCallbackQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = 68A29641B41F195F072F310AA55BAD34 /* SDCallbackQueue.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		B4F231C5CBAB3D4A184699D0066E0E83 /* SDImageAWebPCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = C7236E2EEB4AA6D1846AD94028AEFC7D /* SDImageAWebPCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B59E60FBC9665FC1061B88B8E6FD9FAF /* Masonry-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 827B4AEF873209876171726B873943C3 /* Masonry-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B5AF87C11A465F666473F6191D173905 /* UIView+WebCacheOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 94A8BBB41D9D4550AE7ED049F1312581 /* UIView+WebCacheOperation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B66356D4E7E43B3D15324569AA7EBB05 /* SDWebImageDownloaderOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = F917441BA05D3158473DBA00A2A559BE /* SDWebImageDownloaderOperation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B680C2604BD8BC9644AE7C67BC46B9BB /* MASLayoutConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = ED7DC2C78A9694F09928BCCD132D0888 /* MASLayoutConstraint.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B741DBE2A466E6211F879EF997D9322D /* SDImageCodersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = DA95C7182540A3485F8F19EA9AC04CC6 /* SDImageCodersManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B7AE3054B7362DB2A0D78FDB4581EA5D /* MJRefreshConst.m in Sources */ = {isa = PBXBuildFile; fileRef = 55306CC7388014041F4BB9FB1F889CC2 /* MJRefreshConst.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		B9A69C90CCB9BCF725EBE0B7F9CAFE83 /* MJRefreshGifHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 9523759946AEA807A7FC3992EEE6A188 /* MJRefreshGifHeader.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		BA904ABA8ED36CC4E5EB2B2004CA1F18 /* MASCompositeConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 5E979675DF7D8C7C19A7C033EAFD8D0A /* MASCompositeConstraint.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BADA31750A2136D073EDA4461DBE1EEA /* UIButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 985C7EB42A1E7AAF6BB549FA115E971A /* UIButton+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BB4AFA8BD0A33167A5A2003A6627BFCE /* AFAutoPurgingImageCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 7BC7C2C2B94D1AD15FC72C72DB784368 /* AFAutoPurgingImageCache.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		BC2F9B1D6986FEB23B4FB1288B512538 /* MJRefreshNormalTrailer.h in Headers */ = {isa = PBXBuildFile; fileRef = 03D0F1A648550047C82BAC391D8F3CCB /* MJRefreshNormalTrailer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BC4DBB0F7860378B09B9C0FF861B2B50 /* SDWebImageDownloaderResponseModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = BCB66A7577E1BEC2FA620327B38ECF36 /* SDWebImageDownloaderResponseModifier.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		BC5458210A973BC7A29D1F45D458A14B /* AFNetworking-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 999AD72F3AB19F6B2C208A4F0F891C4D /* AFNetworking-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BCEFDE57BB0E0B36731C8D39FFA1BE2C /* SDWebImageDownloaderRequestModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = FD8631AF5229AEDEE24C1FDCFA4A33D9 /* SDWebImageDownloaderRequestModifier.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BD144751C3F13099EC9FC35B0E2B0CE0 /* MJRefreshAutoGifFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 2D1C98D35AAE74C3EF13B6F460EEDFA3 /* MJRefreshAutoGifFooter.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		BDBE494BAC544843982C3CA96A6C41DD /* SDAnimatedImagePlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 4BFCCF7837E110AD3AEA7D06538FF77E /* SDAnimatedImagePlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BF22D137EF6324675FA50080C5D93C00 /* NSArray+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 7BC9D7B5E24A570D4B15C828C283C110 /* NSArray+MASAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C07995A22123B943C61586D2F20837CC /* NSBezierPath+SDRoundedCorners.m in Sources */ = {isa = PBXBuildFile; fileRef = EAB5C056CF351D2765636FC735C56EDF /* NSBezierPath+SDRoundedCorners.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		C0D7926E41A294ACA98D7B033B283919 /* WKWebView+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = E2EEB1D439A43D9E505CD6101ED63DBC /* WKWebView+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C1DD8C6A64F948E4C53560C76B995DA4 /* SDAnimatedImageView.h in Headers */ = {isa = PBXBuildFile; fileRef = 1EDC0981E6F227433F178D728CC017A0 /* SDAnimatedImageView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C2068AEACC2D9C7F1FFE41AA25B12A68 /* MASUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = 475C74E72914FC29457D226142280BAB /* MASUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C2127875C73D2410098AB80F5E4B5A47 /* SDImageGraphics.m in Sources */ = {isa = PBXBuildFile; fileRef = E8F3DD417554E55B35CC25168BFD9471 /* SDImageGraphics.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		C6A100159974349FEAAC99B82BE0F872 /* SDImageLoader.h in Headers */ = {isa = PBXBuildFile; fileRef = AE7ADC7EC924839420F617F3FC59A4C9 /* SDImageLoader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C6C25434FA32BED77FB193984A8F0210 /* View+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = F7FE5D93FEFB9568DE757E468C8E3EF4 /* View+MASAdditions.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		C75D6F34DD6408428E6E843DDA7CB82F /* SDWeakProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = 682D9EDF95080B223D70F2FE88D0230B /* SDWeakProxy.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		C993A4D0CFA51E567F458CA65C1298AA /* MBProgressHUD-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 463E1010678231673BB1278F51A1B3A0 /* MBProgressHUD-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C9D162799D509B23026D3F86CC987578 /* SDAnimatedImageRep.m in Sources */ = {isa = PBXBuildFile; fileRef = FF17D24562FC7A3B69EC310B4A1AF066 /* SDAnimatedImageRep.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		CD9A33B79474CBDBE3A7571612FB052F /* SDWebImageDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 97979716DD9806B4792C15A5D39D8333 /* SDWebImageDownloader.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		CDDDA24076474BFEE9EC40F1E1A0B17F /* MJRefreshBackGifFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = A4F0B132AAD230F5797450132A241F48 /* MJRefreshBackGifFooter.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		CF7AD9106444B9F4C82D46AC684D26A3 /* SDWebImageManager.m in Sources */ = {isa = PBXBuildFile; fileRef = D8A58FA6313F2D0A1541C2945A374091 /* SDWebImageManager.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		CFB4828D54C4831F7B05B02FDEE77313 /* SDWebImageTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = 3304CFA7E872D5643B5B02C72BAEAB07 /* SDWebImageTransition.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		D0D19B844154C5747894F960B6C0BF5A /* SDImageAPNGCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 547A586690048889779EA64FC1DB5B1A /* SDImageAPNGCoder.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		D2CD8848F856EC9942A76610AAE66F0A /* SDImageIOCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 149D5BCEBF0EE0AB27A71762B0A0E63B /* SDImageIOCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D3D32F17B82FC5CB56B5C4A472ACD9DF /* SDAssociatedObject.m in Sources */ = {isa = PBXBuildFile; fileRef = B1CF8F5D5FFE6E807F77474954F0D603 /* SDAssociatedObject.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		D3EEDC1A8AAA55DF23CDB729D3E2E0EE /* AFNetworking-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = E5C50857692780902AB615556CCBEADF /* AFNetworking-dummy.m */; };
		D506F4595C868442A852AECC5FF083B7 /* UIImage+ForceDecode.m in Sources */ = {isa = PBXBuildFile; fileRef = A45316D7B79AA72FA0B7C77B0DCBBA54 /* UIImage+ForceDecode.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		D62A672EEB252581BD972DDA862BE1DD /* SDWebImage-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 7C01C6CC53D312AF131AB282EA14947C /* SDWebImage-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D662C83ECE8BEDA5FFB52F3575CA3E1A /* SDImageCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 9AB6CF4CE580A096EAF2EFDD0FBCC4A8 /* SDImageCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D663837F4347AF58660EE6F7FD426ECE /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ********18AC31E3A631F72434F9EAC1 /* Foundation.framework */; };
		D7B3E8948DB04BD8FB6748419DA03EA9 /* SDAnimatedImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 46491D498B464BEBE0EC959D23192818 /* SDAnimatedImageView+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D8DA999E71BF3CA7665BC59ED50AAD5D /* SDImageIOAnimatedCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 0437A147EC9E9B4E9CC5F1F063AB0B93 /* SDImageIOAnimatedCoder.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		D90DED0F5638B1C44F4B6C62D600D240 /* MJRefreshFooter.h in Headers */ = {isa = PBXBuildFile; fileRef = 9790197BECAE968B2658D60B705AFC08 /* MJRefreshFooter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D90DF1376DF5E2EA644313BCD2E03058 /* MJRefresh.bundle in Resources */ = {isa = PBXBuildFile; fileRef = D2BFA139BE3968E4C9CE45F9CFFB42F8 /* MJRefresh.bundle */; };
		D969F44324C5A550A4F9E52D94A97CE8 /* UIImageView+AFNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = D416291597A3B1584F853B95842D122A /* UIImageView+AFNetworking.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		D96E15F93436B1DB960DDE9AB510B6BD /* NSButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F9B3CFDE5CF3B5EDB2FDF119FAEB0909 /* NSButton+WebCache.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		DBA9500CBBA5FF6FCBBA115AE4D12152 /* NSLayoutConstraint+MASDebugAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = FDBA4E418E7FE6B322DCD03CB1007ECB /* NSLayoutConstraint+MASDebugAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DBD9152526A180771BF7D7CD209B957E /* AFSecurityPolicy.h in Headers */ = {isa = PBXBuildFile; fileRef = 46AB11EE9F25F2B4A06C60EB74D589C0 /* AFSecurityPolicy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DCAE35CC587C05DB52C8AFD750D1283E /* UIView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = E5DA28F82FA67E32883164279342B1C5 /* UIView+WebCache.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		DDA16FB9C21AD941442357DAE6939530 /* UIKit+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = 92A4C50F7C210F62C7B0B00E50420B9D /* UIKit+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DE1CCE35189DBF6186C484FD0821BE35 /* AFURLRequestSerialization.m in Sources */ = {isa = PBXBuildFile; fileRef = 3B165C9C87A05B5D2B56C064216B3F53 /* AFURLRequestSerialization.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		DEA09692CF813A23899CD4949A9B6801 /* SDAnimatedImageRep.h in Headers */ = {isa = PBXBuildFile; fileRef = AAFC26F4DD4F9E0979BE8DFF3500D769 /* SDAnimatedImageRep.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E1BA4F623ABACB4BF2C5308666DC16C7 /* NSLayoutConstraint+MASDebugAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 5AFBA17DDB5A92C6CBE588966C50DA5A /* NSLayoutConstraint+MASDebugAdditions.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		E1BF615DD0422B06C97542F03C879D41 /* AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = 8F3A6BFB7DF2F4D1FCF9A9C094140645 /* AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E22E41C2FFFDC3CF4A97D20721F914AA /* MASLayoutConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C70894986E131C08150C40ED432DEAD /* MASLayoutConstraint.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		E2F1C542596DA1C119025B09006515DF /* SDWebImageDownloaderOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 2461423ABCA1353E9AFCAF72B1A06BDA /* SDWebImageDownloaderOperation.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		E3FC6BEE41652C0500F57E0CB83B347F /* UIButton+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = 61E0DDC8E4BB60D1261A51923E08930A /* UIButton+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E457126A7666DED46B9686263B99D2FC /* SDImageLoadersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 62D435F0D5C3E87CC68C0BF082F6E0E1 /* SDImageLoadersManager.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		E62B22EE7D6F55848B2AB98656A2C57B /* AFSecurityPolicy.m in Sources */ = {isa = PBXBuildFile; fileRef = 94261EA85639B90596EA529A46C865B4 /* AFSecurityPolicy.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		E63F1BC0601BA84CB6A57C6C5AD6FF0A /* MJRefreshTrailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 89BC8B491110B74DD22065F31A45E4FA /* MJRefreshTrailer.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		E76969F9B01139118427505B18F9CD21 /* SDImageHEICCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = E6FBC862BCA3841DA4EB10CFE49BF37E /* SDImageHEICCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E8141F27937D3C60DF2415E091D71FDC /* SDImageCache.m in Sources */ = {isa = PBXBuildFile; fileRef = BF66F6E35F90B08E6DDF6563A536B83D /* SDImageCache.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		EA82B6D97C9C5D0558047AF552D63203 /* SDWebImageDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 42C095FCD5E6908F2A488DCD557967FD /* SDWebImageDefine.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EA94446AB5D7EC5D6A61B8F381B31099 /* NSImage+Compatibility.m in Sources */ = {isa = PBXBuildFile; fileRef = 41E3C62DE6443E9515E490AE0C89BCA6 /* NSImage+Compatibility.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		EABCB60A26B06BF576E50BBD2F89A385 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ********18AC31E3A631F72434F9EAC1 /* Foundation.framework */; };
		EB3DF628891F7D6AB114718AF760CB2A /* UIImageView+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = 1B80FC420B441C164ED5526EABFB6768 /* UIImageView+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EC3BD13BEEE1C2C70EC415A5385E3F96 /* SDDiskCache.m in Sources */ = {isa = PBXBuildFile; fileRef = D1A0A439782F1962B984760F777978B3 /* SDDiskCache.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		EC3E287CFC2FAF33318835692104B5A3 /* MJRefreshHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = B43D44D3BBECD4BA8EB63397DCDAF2B0 /* MJRefreshHeader.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		EC8E84A8FFADDCA562A8608D141D9027 /* MJRefreshAutoGifFooter.h in Headers */ = {isa = PBXBuildFile; fileRef = D252DF203C2311D237C3C3E611BFCBEF /* MJRefreshAutoGifFooter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EC9B34262AED632D7EFB49804337648E /* Masonry.h in Headers */ = {isa = PBXBuildFile; fileRef = 50B5002BAAFA612BE71E41791FC7DA5D /* Masonry.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ED8F64FF98CFAE0B12CF60A1B0E6BAF8 /* SDCallbackQueue.h in Headers */ = {isa = PBXBuildFile; fileRef = 5963BA984D603144437C80A5A3B695CF /* SDCallbackQueue.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE6E8FE636D2C02E3D2FC1E8555B4612 /* MJRefreshNormalHeader.h in Headers */ = {isa = PBXBuildFile; fileRef = 3EB099BAF1D47C58C076552D6AB59EAE /* MJRefreshNormalHeader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EEAF8007F9311797284A7E74B481777A /* NSArray+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 376187B2E18A36D1C1CECAC6856BA657 /* NSArray+MASAdditions.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		EF6A6C725598F572A70C5FCEE328C184 /* SDImageTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = 0B5E74804F609475F7372460A78DE8B1 /* SDImageTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EF9F754349CB7CE18D611FE17C8E8304 /* MJRefreshNormalHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = F4C3BD86F9B8643726AB3BE8CF5D1CA7 /* MJRefreshNormalHeader.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		F1B08CBF7344D57B22F2EFBBCC770118 /* UIImage+GIF.m in Sources */ = {isa = PBXBuildFile; fileRef = 3BCEF0C6D95330B8BB6E9DAB91371274 /* UIImage+GIF.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		F1D845E22D5B8FC6AFC3C2E41DA1B6DF /* AFNetworkReachabilityManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 0C4B6DB752E9541A04467C2B617217C5 /* AFNetworkReachabilityManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F284AB7A34BEE825EB8C313437F0C400 /* SDImageIOCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 79FE42DD72DB4329A1DD91BDA95CCF39 /* SDImageIOCoder.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		F2AD91050B1FE3C8BC78567F1FDE3ED5 /* AFURLResponseSerialization.h in Headers */ = {isa = PBXBuildFile; fileRef = F9EC47F507A90874FBE790D58D846835 /* AFURLResponseSerialization.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F2C9F9038FBEFD951516694AE873A2B9 /* MBProgressHUD.h in Headers */ = {isa = PBXBuildFile; fileRef = 0109270CA3F4007C62D52BEB2C2AA82D /* MBProgressHUD.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F49CB22863CCFEC7817D259F27F91C57 /* SDWebImageIndicator.h in Headers */ = {isa = PBXBuildFile; fileRef = F5A610DA785C65043A60D604CF3685A1 /* SDWebImageIndicator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F53BE4449AE5896F76325E4DCB6D0B13 /* SDImageCachesManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 7F05B4565F31794F1F01F92F73471962 /* SDImageCachesManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F60F90EAF35CFF40DF1C33557965787D /* MJRefreshStateTrailer.h in Headers */ = {isa = PBXBuildFile; fileRef = AC7B0BA8555EF48ED3BE0E8F436BE98D /* MJRefreshStateTrailer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F62C7836BEF965F234AAC70294EA29B3 /* SDAsyncBlockOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 4371426C5985EFEF4104C3FB4AA1CD57 /* SDAsyncBlockOperation.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		F68889CD481716EE5D6B75EBD8FD53A6 /* SDImageCoderHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = B55AB1316AD7B1BB02FCC223DE1A5670 /* SDImageCoderHelper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F74B0737895AD6DC8F3C2D9778A4E6D8 /* SDImageCoderHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 2D010C730376F25C4204BE83C3AAD783 /* SDImageCoderHelper.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		F90D1B7A3985536ABBC7F5C4CFF91C6B /* MJRefreshBackStateFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 8B6C1075FC4103587CE36B00E10346A6 /* MJRefreshBackStateFooter.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		F926674D7D29E89BDFB085520CC4CDFC /* MASViewConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = D6031285ADB535394FE605C0C753CF45 /* MASViewConstraint.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		F93ED4D1045DA563CC1D3087C7238E4F /* NSData+ImageContentType.m in Sources */ = {isa = PBXBuildFile; fileRef = BB4E57E6F34C04827AD32F18DB5ED56D /* NSData+ImageContentType.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		F95520AA120144213BF44DA77158DF38 /* MBProgressHUD-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 0EC3AE8E59C05BEF1EC291160DFE04EA /* MBProgressHUD-dummy.m */; };
		FC93205E60C3C950572EC83697CF2662 /* SDInternalMacros.m in Sources */ = {isa = PBXBuildFile; fileRef = C2281BD83BF2BF5BB474029720C9DF5D /* SDInternalMacros.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		FDACBA49610EA6F39CABB7FE44B137D1 /* AFImageDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 05814BE6A5C0F238469868A5DEA97CCF /* AFImageDownloader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FE05C5A4B556B0BDD376B2CC0C8A2054 /* MJRefreshAutoStateFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 57A95C170B0A7F681E33FAE6FA961820 /* MJRefreshAutoStateFooter.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
		FE112C1E57DAF79DD883E3E9B8006E7B /* MJRefreshNormalTrailer.m in Sources */ = {isa = PBXBuildFile; fileRef = C0E585FAC02532C13FF9B3C1663456DC /* MJRefreshNormalTrailer.m */; settings = {COMPILER_FLAGS = "-w -Xanalyzer -analyzer-disable-all-checks"; }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		0D5556361B032ECE56C2000CF77B4838 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8EA62FEAB23070050EFD4D4508D659E5;
			remoteInfo = FBSDKCoreKit_Basics;
		};
		1091B66A7DD14CAC562909F218C5307D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3847153A6E5EEFB86565BA840768F429;
			remoteInfo = SDWebImage;
		};
		2B3AA200267FB4CD1A593D18DA38F626 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 28907903AF6DD0AD321D92CD660E7E23;
			remoteInfo = FBSDKCoreKit;
		};
		41CFEEA77C98E40881A18930DE32E907 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B26054DF1DEA11585A231AF6D1D80D5E;
			remoteInfo = "MJRefresh-MJRefresh.Privacy";
		};
		66596F3590FAE8DF8059ECD650A5EDFF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6868056D761E163D10FDAF8CF1C4D9B8;
			remoteInfo = MJRefresh;
		};
		78E76CA90215151AA2C33C51BCF862C4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 55AF53E6C77A10ED4985E04D74A8878E;
			remoteInfo = Masonry;
		};
		89710304AB044E533C928C864D0E804D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0130B3724283586C0E9D2A112D4F2AA1;
			remoteInfo = AFNetworking;
		};
		9145D864B4E2F1363956581CD8712005 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8EA62FEAB23070050EFD4D4508D659E5;
			remoteInfo = FBSDKCoreKit_Basics;
		};
		B7291C21D0E8234F35EA7B3CA26D01AF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 94CFBA7D633ECA58DF85C327B035E6A3;
			remoteInfo = "SDWebImage-SDWebImage";
		};
		C9464F204F473A476B98E179BF693C89 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 479BDBD165CD08E2159181E58CD2078F;
			remoteInfo = FBAEMKit;
		};
		D2291A5AD3B3F70AA59B0508EFAA7AA0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8EA62FEAB23070050EFD4D4508D659E5;
			remoteInfo = FBSDKCoreKit_Basics;
		};
		F44F0F009949752F9B43812F21818368 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 82B0A41D3031FF27D78E17B0A9A46FB0;
			remoteInfo = MBProgressHUD;
		};
		FCD223F85A2C9E016876F441ED25FA87 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 479BDBD165CD08E2159181E58CD2078F;
			remoteInfo = FBAEMKit;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		007ADD6FF507592CA39ADDB8D6027C4B /* MJRefresh.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = MJRefresh.release.xcconfig; sourceTree = "<group>"; };
		0081A57C73420FA6768D1DF16C510D4F /* UIImage+ExtendedCacheData.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+ExtendedCacheData.m"; path = "SDWebImage/Core/UIImage+ExtendedCacheData.m"; sourceTree = "<group>"; };
		0109270CA3F4007C62D52BEB2C2AA82D /* MBProgressHUD.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = MBProgressHUD.h; sourceTree = "<group>"; };
		023B4F18A51DEDE66731835742BD6088 /* SDWebImageCacheKeyFilter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageCacheKeyFilter.h; path = SDWebImage/Core/SDWebImageCacheKeyFilter.h; sourceTree = "<group>"; };
		027B64F3DA1EFB1393F8E2671667D578 /* Masonry-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Masonry-prefix.pch"; sourceTree = "<group>"; };
		03D0F1A648550047C82BAC391D8F3CCB /* MJRefreshNormalTrailer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshNormalTrailer.h; path = MJRefresh/Custom/Trailer/MJRefreshNormalTrailer.h; sourceTree = "<group>"; };
		0437A147EC9E9B4E9CC5F1F063AB0B93 /* SDImageIOAnimatedCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageIOAnimatedCoder.m; path = SDWebImage/Core/SDImageIOAnimatedCoder.m; sourceTree = "<group>"; };
		05814BE6A5C0F238469868A5DEA97CCF /* AFImageDownloader.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFImageDownloader.h; path = "UIKit+AFNetworking/AFImageDownloader.h"; sourceTree = "<group>"; };
		0587A346147AAEABC7D97135E800FC85 /* SDImageCodersManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCodersManager.m; path = SDWebImage/Core/SDImageCodersManager.m; sourceTree = "<group>"; };
		05AE5833538310BC194D7C9EEC7FC83F /* SDGraphicsImageRenderer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDGraphicsImageRenderer.m; path = SDWebImage/Core/SDGraphicsImageRenderer.m; sourceTree = "<group>"; };
		08C3D6EA978F6FF57BE3D91B257A18F6 /* UIColor+SDHexString.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIColor+SDHexString.h"; path = "SDWebImage/Private/UIColor+SDHexString.h"; sourceTree = "<group>"; };
		090F99E04964E3730A77393230B37658 /* MASConstraintMaker.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MASConstraintMaker.m; path = Masonry/MASConstraintMaker.m; sourceTree = "<group>"; };
		0B5E74804F609475F7372460A78DE8B1 /* SDImageTransformer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageTransformer.h; path = SDWebImage/Core/SDImageTransformer.h; sourceTree = "<group>"; };
		0B7C6A23D76E609B238E5B495F86DA9F /* NSBundle+MJRefresh.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSBundle+MJRefresh.m"; path = "MJRefresh/NSBundle+MJRefresh.m"; sourceTree = "<group>"; };
		0C4B6DB752E9541A04467C2B617217C5 /* AFNetworkReachabilityManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFNetworkReachabilityManager.h; path = AFNetworking/AFNetworkReachabilityManager.h; sourceTree = "<group>"; };
		0CF2537BD561882CC27737B366DE2B42 /* AFNetworking-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AFNetworking-prefix.pch"; sourceTree = "<group>"; };
		0E3AC68774EBD70CA88496DBD3A19FF6 /* SDWebImageTransition.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageTransition.h; path = SDWebImage/Core/SDWebImageTransition.h; sourceTree = "<group>"; };
		0EC3AE8E59C05BEF1EC291160DFE04EA /* MBProgressHUD-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "MBProgressHUD-dummy.m"; sourceTree = "<group>"; };
		0EE55DD0D556C460D50C97617AEF9F18 /* MJRefresh.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefresh.h; path = MJRefresh/MJRefresh.h; sourceTree = "<group>"; };
		100AECAC3BB237B7815607A9620170E6 /* SDAnimatedImageView+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "SDAnimatedImageView+WebCache.m"; path = "SDWebImage/Core/SDAnimatedImageView+WebCache.m"; sourceTree = "<group>"; };
		105ADDBD5121C299E136A86C6823E6BB /* MBProgressHUD.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = MBProgressHUD.debug.xcconfig; sourceTree = "<group>"; };
		10BE79645E38972233986B0051941E7D /* AFURLSessionManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFURLSessionManager.m; path = AFNetworking/AFURLSessionManager.m; sourceTree = "<group>"; };
		1228C9454A54DBE46799F2B283099983 /* Masonry.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Masonry.release.xcconfig; sourceTree = "<group>"; };
		149D5BCEBF0EE0AB27A71762B0A0E63B /* SDImageIOCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageIOCoder.h; path = SDWebImage/Core/SDImageIOCoder.h; sourceTree = "<group>"; };
		1555F5583D3F4900FE77515D7DEF159A /* ViewController+MASAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "ViewController+MASAdditions.h"; path = "Masonry/ViewController+MASAdditions.h"; sourceTree = "<group>"; };
		1781C65558CA244C8CC01555D717EE86 /* MJRefresh-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "MJRefresh-Info.plist"; sourceTree = "<group>"; };
		17CF85A57080E64F02970A5FA4D3A2C2 /* Pods-Nano_Loan-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-Nano_Loan-umbrella.h"; sourceTree = "<group>"; };
		17E35AFC29F73D366F8DC32D199561B6 /* FBSDKCoreKit.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = FBSDKCoreKit.release.xcconfig; sourceTree = "<group>"; };
		1AB98E5B2A7F5B2EB08A105196863D7E /* SDWebImageDownloader.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloader.h; path = SDWebImage/Core/SDWebImageDownloader.h; sourceTree = "<group>"; };
		1B686371EF9EBDD85A19E25539F568BA /* View+MASShorthandAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "View+MASShorthandAdditions.h"; path = "Masonry/View+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		1B80FC420B441C164ED5526EABFB6768 /* UIImageView+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImageView+AFNetworking.h"; path = "UIKit+AFNetworking/UIImageView+AFNetworking.h"; sourceTree = "<group>"; };
		1DEC8F932E25A416DEC3337AB32F1DB6 /* NSBezierPath+SDRoundedCorners.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSBezierPath+SDRoundedCorners.h"; path = "SDWebImage/Private/NSBezierPath+SDRoundedCorners.h"; sourceTree = "<group>"; };
		1EDC0981E6F227433F178D728CC017A0 /* SDAnimatedImageView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAnimatedImageView.h; path = SDWebImage/Core/SDAnimatedImageView.h; sourceTree = "<group>"; };
		1EFBC7CEE060C7FDF33DC6282F25269F /* SDAsyncBlockOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAsyncBlockOperation.h; path = SDWebImage/Private/SDAsyncBlockOperation.h; sourceTree = "<group>"; };
		1FFED36A657123030ABB700256D73F15 /* Masonry */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = Masonry; path = Masonry.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		20E960E84CE2327E5B7BA6948C92CFCE /* UIImageView+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImageView+WebCache.h"; path = "SDWebImage/Core/UIImageView+WebCache.h"; sourceTree = "<group>"; };
		223C9B7435D107017AA4F01B178FC6A6 /* SDWebImage-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "SDWebImage-dummy.m"; sourceTree = "<group>"; };
		229416BAB314DD8212DA96597D6D6620 /* MASConstraint.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASConstraint.h; path = Masonry/MASConstraint.h; sourceTree = "<group>"; };
		2461423ABCA1353E9AFCAF72B1A06BDA /* SDWebImageDownloaderOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderOperation.m; path = SDWebImage/Core/SDWebImageDownloaderOperation.m; sourceTree = "<group>"; };
		24FDB8B29FE3DE461E78F8FB5F2905BD /* AFAutoPurgingImageCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFAutoPurgingImageCache.h; path = "UIKit+AFNetworking/AFAutoPurgingImageCache.h"; sourceTree = "<group>"; };
		257FECACB47F5E238F43124258E43313 /* Pods-Nano_Loan-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-Nano_Loan-frameworks.sh"; sourceTree = "<group>"; };
		25839EACDB320C59013B336E48C9C385 /* UIScrollView+MJExtension.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIScrollView+MJExtension.h"; path = "MJRefresh/UIScrollView+MJExtension.h"; sourceTree = "<group>"; };
		2737C54349985D603F3EEA20519667C6 /* SDWebImageError.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageError.h; path = SDWebImage/Core/SDWebImageError.h; sourceTree = "<group>"; };
		28CE361547B2B6EAD184FC4F74FFFE2C /* SDImageFramePool.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageFramePool.h; path = SDWebImage/Private/SDImageFramePool.h; sourceTree = "<group>"; };
		2A6CB6C698E97EE524E3D37B9CFBB489 /* SDImageFrame.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageFrame.m; path = SDWebImage/Core/SDImageFrame.m; sourceTree = "<group>"; };
		2B3FF7138CEC0FBBE3857CD3ECDB4831 /* UIProgressView+AFNetworking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIProgressView+AFNetworking.m"; path = "UIKit+AFNetworking/UIProgressView+AFNetworking.m"; sourceTree = "<group>"; };
		2C788B033735C02C5668C07553CA91FA /* SDImageAPNGCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageAPNGCoder.h; path = SDWebImage/Core/SDImageAPNGCoder.h; sourceTree = "<group>"; };
		2D010C730376F25C4204BE83C3AAD783 /* SDImageCoderHelper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCoderHelper.m; path = SDWebImage/Core/SDImageCoderHelper.m; sourceTree = "<group>"; };
		2D1C98D35AAE74C3EF13B6F460EEDFA3 /* MJRefreshAutoGifFooter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshAutoGifFooter.m; path = MJRefresh/Custom/Footer/Auto/MJRefreshAutoGifFooter.m; sourceTree = "<group>"; };
		2FF5D582ADB501231D1BFAEB7E8AA0FE /* Masonry-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Masonry-Info.plist"; sourceTree = "<group>"; };
		3022DE9ECACD33B1B6954AFBE01D1D43 /* NSButton+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSButton+WebCache.h"; path = "SDWebImage/Core/NSButton+WebCache.h"; sourceTree = "<group>"; };
		307C93B0A95C9A9FA6B6318B6071CA56 /* SDWebImageDownloaderConfig.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderConfig.m; path = SDWebImage/Core/SDWebImageDownloaderConfig.m; sourceTree = "<group>"; };
		30F34249C4EBD69AF03F67D07C9F4AD7 /* UIImageView+HighlightedWebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImageView+HighlightedWebCache.m"; path = "SDWebImage/Core/UIImageView+HighlightedWebCache.m"; sourceTree = "<group>"; };
		31534ED5D5D783AC06EBB8479FA2A640 /* UIRefreshControl+AFNetworking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIRefreshControl+AFNetworking.m"; path = "UIKit+AFNetworking/UIRefreshControl+AFNetworking.m"; sourceTree = "<group>"; };
		31AE83AF573F7CE65709672553568710 /* UIColor+SDHexString.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIColor+SDHexString.m"; path = "SDWebImage/Private/UIColor+SDHexString.m"; sourceTree = "<group>"; };
		31C1E203148A786A2C4BFD6ED5BA7DF4 /* SDWebImageDownloaderDecryptor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderDecryptor.h; path = SDWebImage/Core/SDWebImageDownloaderDecryptor.h; sourceTree = "<group>"; };
		3304CFA7E872D5643B5B02C72BAEAB07 /* SDWebImageTransition.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageTransition.m; path = SDWebImage/Core/SDWebImageTransition.m; sourceTree = "<group>"; };
		349A8C7FE5264237269403A5B812657A /* MBProgressHUD.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = MBProgressHUD.m; sourceTree = "<group>"; };
		******************************** /* UIActivityIndicatorView+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIActivityIndicatorView+AFNetworking.h"; path = "UIKit+AFNetworking/UIActivityIndicatorView+AFNetworking.h"; sourceTree = "<group>"; };
		366488E341C4071AAD5962E9517BD973 /* SDWebImageCacheKeyFilter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageCacheKeyFilter.m; path = SDWebImage/Core/SDWebImageCacheKeyFilter.m; sourceTree = "<group>"; };
		36A6004F83217C098FE98F2CC5DD3F85 /* UIImage+MemoryCacheCost.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+MemoryCacheCost.m"; path = "SDWebImage/Core/UIImage+MemoryCacheCost.m"; sourceTree = "<group>"; };
		376187B2E18A36D1C1CECAC6856BA657 /* NSArray+MASAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSArray+MASAdditions.m"; path = "Masonry/NSArray+MASAdditions.m"; sourceTree = "<group>"; };
		3B165C9C87A05B5D2B56C064216B3F53 /* AFURLRequestSerialization.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFURLRequestSerialization.m; path = AFNetworking/AFURLRequestSerialization.m; sourceTree = "<group>"; };
		3BCEF0C6D95330B8BB6E9DAB91371274 /* UIImage+GIF.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+GIF.m"; path = "SDWebImage/Core/UIImage+GIF.m"; sourceTree = "<group>"; };
		3C70894986E131C08150C40ED432DEAD /* MASLayoutConstraint.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MASLayoutConstraint.m; path = Masonry/MASLayoutConstraint.m; sourceTree = "<group>"; };
		3EB099BAF1D47C58C076552D6AB59EAE /* MJRefreshNormalHeader.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshNormalHeader.h; path = MJRefresh/Custom/Header/MJRefreshNormalHeader.h; sourceTree = "<group>"; };
		3F7DDBBBA1F1DE3A57E40BB04EA2E4FD /* UIView+MJExtension.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIView+MJExtension.m"; path = "MJRefresh/UIView+MJExtension.m"; sourceTree = "<group>"; };
		406D93EFB2E434C2D0A7D810CFEBBB90 /* MBProgressHUD.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = MBProgressHUD.modulemap; sourceTree = "<group>"; };
		415AE4EBD078041AB7A08362BF1EC94C /* MASViewConstraint.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASViewConstraint.h; path = Masonry/MASViewConstraint.h; sourceTree = "<group>"; };
		41E3C62DE6443E9515E490AE0C89BCA6 /* NSImage+Compatibility.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSImage+Compatibility.m"; path = "SDWebImage/Core/NSImage+Compatibility.m"; sourceTree = "<group>"; };
		42C095FCD5E6908F2A488DCD557967FD /* SDWebImageDefine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDefine.h; path = SDWebImage/Core/SDWebImageDefine.h; sourceTree = "<group>"; };
		4371426C5985EFEF4104C3FB4AA1CD57 /* SDAsyncBlockOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAsyncBlockOperation.m; path = SDWebImage/Private/SDAsyncBlockOperation.m; sourceTree = "<group>"; };
		451768D7F5A52CCC3C00D92A8A6C245F /* SDWebImageError.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageError.m; path = SDWebImage/Core/SDWebImageError.m; sourceTree = "<group>"; };
		45232534F89924F94768CE145BD803CF /* SDAnimatedImagePlayer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAnimatedImagePlayer.m; path = SDWebImage/Core/SDAnimatedImagePlayer.m; sourceTree = "<group>"; };
		463E1010678231673BB1278F51A1B3A0 /* MBProgressHUD-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "MBProgressHUD-umbrella.h"; sourceTree = "<group>"; };
		46491D498B464BEBE0EC959D23192818 /* SDAnimatedImageView+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "SDAnimatedImageView+WebCache.h"; path = "SDWebImage/Core/SDAnimatedImageView+WebCache.h"; sourceTree = "<group>"; };
		46AB11EE9F25F2B4A06C60EB74D589C0 /* AFSecurityPolicy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFSecurityPolicy.h; path = AFNetworking/AFSecurityPolicy.h; sourceTree = "<group>"; };
		46B98724BC7B3F413DF4AA065977E113 /* AFNetworkActivityIndicatorManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFNetworkActivityIndicatorManager.m; path = "UIKit+AFNetworking/AFNetworkActivityIndicatorManager.m"; sourceTree = "<group>"; };
		475C74E72914FC29457D226142280BAB /* MASUtilities.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASUtilities.h; path = Masonry/MASUtilities.h; sourceTree = "<group>"; };
		47DBE4444D8F2A5F189BE961DDFCFCB1 /* AFNetworking-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "AFNetworking-Info.plist"; sourceTree = "<group>"; };
		49EC12F59B1E51AF51B523248388A148 /* SDImageCachesManagerOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCachesManagerOperation.h; path = SDWebImage/Private/SDImageCachesManagerOperation.h; sourceTree = "<group>"; };
		4ADFEFE6DD12D5AF909630EE100337C5 /* SDImageCachesManagerOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCachesManagerOperation.m; path = SDWebImage/Private/SDImageCachesManagerOperation.m; sourceTree = "<group>"; };
		4B5F5635CE0A988D76396CF3ECD0F1E0 /* FBSDKCoreKit_Basics.xcframework */ = {isa = PBXFileReference; includeInIndex = 1; name = FBSDKCoreKit_Basics.xcframework; path = XCFrameworks/FBSDKCoreKit_Basics.xcframework; sourceTree = "<group>"; };
		4B954FC92C73D230D63697C0223C8528 /* NSImage+Compatibility.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSImage+Compatibility.h"; path = "SDWebImage/Core/NSImage+Compatibility.h"; sourceTree = "<group>"; };
		4BFCCF7837E110AD3AEA7D06538FF77E /* SDAnimatedImagePlayer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAnimatedImagePlayer.h; path = SDWebImage/Core/SDAnimatedImagePlayer.h; sourceTree = "<group>"; };
		4E5EF39885B5235C036ACF039D66460F /* MJRefresh-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "MJRefresh-dummy.m"; sourceTree = "<group>"; };
		4FAAD439B70F4518579093867C8A582F /* UIImage+GIF.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+GIF.h"; path = "SDWebImage/Core/UIImage+GIF.h"; sourceTree = "<group>"; };
		50B5002BAAFA612BE71E41791FC7DA5D /* Masonry.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = Masonry.h; path = Masonry/Masonry.h; sourceTree = "<group>"; };
		5216AD9C10B699BF9FE794C8C04B7506 /* AFNetworking.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AFNetworking.debug.xcconfig; sourceTree = "<group>"; };
		522173517F8D5E0CFDB0813826D9B87E /* UIImage+MemoryCacheCost.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+MemoryCacheCost.h"; path = "SDWebImage/Core/UIImage+MemoryCacheCost.h"; sourceTree = "<group>"; };
		524B06850BC1DB525CF4376D77312BE6 /* AFURLRequestSerialization.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFURLRequestSerialization.h; path = AFNetworking/AFURLRequestSerialization.h; sourceTree = "<group>"; };
		533B13EF4F7BE90AC47FC7AF4F7CD7B5 /* AFNetworking.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = AFNetworking.modulemap; sourceTree = "<group>"; };
		538A95AED784C754FF197437BFCE6686 /* SDImageGIFCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageGIFCoder.h; path = SDWebImage/Core/SDImageGIFCoder.h; sourceTree = "<group>"; };
		53FA74C38ADE749EAB807D3090EF1373 /* SDWebImageDownloaderConfig.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderConfig.h; path = SDWebImage/Core/SDWebImageDownloaderConfig.h; sourceTree = "<group>"; };
		547A586690048889779EA64FC1DB5B1A /* SDImageAPNGCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageAPNGCoder.m; path = SDWebImage/Core/SDImageAPNGCoder.m; sourceTree = "<group>"; };
		54D2BD3770D5C912EE835B9FE481F98B /* UIImage+MultiFormat.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+MultiFormat.m"; path = "SDWebImage/Core/UIImage+MultiFormat.m"; sourceTree = "<group>"; };
		55306CC7388014041F4BB9FB1F889CC2 /* MJRefreshConst.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshConst.m; path = MJRefresh/MJRefreshConst.m; sourceTree = "<group>"; };
		554EA878CA6FFC285AB0718A975AFD1B /* UIView+MJExtension.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIView+MJExtension.h"; path = "MJRefresh/UIView+MJExtension.h"; sourceTree = "<group>"; };
		559D20A4B808010925C4AEEF42CBC9F8 /* MJRefreshBackNormalFooter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshBackNormalFooter.m; path = MJRefresh/Custom/Footer/Back/MJRefreshBackNormalFooter.m; sourceTree = "<group>"; };
		55F43F7B1F5F3CEA54FB3C532B7C1EFB /* AFURLResponseSerialization.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFURLResponseSerialization.m; path = AFNetworking/AFURLResponseSerialization.m; sourceTree = "<group>"; };
		564BA9CF40661B3FCD7E41191921750C /* SDImageCacheConfig.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCacheConfig.m; path = SDWebImage/Core/SDImageCacheConfig.m; sourceTree = "<group>"; };
		578DD43BD787E90101B75BEF788BA255 /* AFImageDownloader.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFImageDownloader.m; path = "UIKit+AFNetworking/AFImageDownloader.m"; sourceTree = "<group>"; };
		57A95C170B0A7F681E33FAE6FA961820 /* MJRefreshAutoStateFooter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshAutoStateFooter.m; path = MJRefresh/Custom/Footer/Auto/MJRefreshAutoStateFooter.m; sourceTree = "<group>"; };
		586C7D767724357618422B52C865DA28 /* AFHTTPSessionManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFHTTPSessionManager.h; path = AFNetworking/AFHTTPSessionManager.h; sourceTree = "<group>"; };
		5892ED39C08A09AB1A6D272451BAE5E8 /* SDImageGraphics.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageGraphics.h; path = SDWebImage/Core/SDImageGraphics.h; sourceTree = "<group>"; };
		5910E5BF6CA1CE68DC180FD07454DA57 /* UIImage+ExtendedCacheData.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+ExtendedCacheData.h"; path = "SDWebImage/Core/UIImage+ExtendedCacheData.h"; sourceTree = "<group>"; };
		5963BA984D603144437C80A5A3B695CF /* SDCallbackQueue.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDCallbackQueue.h; path = SDWebImage/Core/SDCallbackQueue.h; sourceTree = "<group>"; };
		59D22431E34837621987CFA0E071C9C8 /* SDGraphicsImageRenderer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDGraphicsImageRenderer.h; path = SDWebImage/Core/SDGraphicsImageRenderer.h; sourceTree = "<group>"; };
		5A5AF98857754B424A3A624850EDA0FB /* SDWebImageCacheSerializer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageCacheSerializer.m; path = SDWebImage/Core/SDWebImageCacheSerializer.m; sourceTree = "<group>"; };
		5A94511790704271ECE7B1239B313843 /* Pods-Nano_Loan.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Nano_Loan.release.xcconfig"; sourceTree = "<group>"; };
		5AB02EF7397807AB656C9DD19DA7AD29 /* MJRefreshConfig.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshConfig.h; path = MJRefresh/MJRefreshConfig.h; sourceTree = "<group>"; };
		5AB8C89747B63EC60AC17506677B66DC /* NSBundle+MJRefresh.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSBundle+MJRefresh.h"; path = "MJRefresh/NSBundle+MJRefresh.h"; sourceTree = "<group>"; };
		5AFBA17DDB5A92C6CBE588966C50DA5A /* NSLayoutConstraint+MASDebugAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSLayoutConstraint+MASDebugAdditions.m"; path = "Masonry/NSLayoutConstraint+MASDebugAdditions.m"; sourceTree = "<group>"; };
		5BAF6786F6992480D509FB4DD1205969 /* MJRefreshAutoNormalFooter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshAutoNormalFooter.h; path = MJRefresh/Custom/Footer/Auto/MJRefreshAutoNormalFooter.h; sourceTree = "<group>"; };
		5C0E534E1E01F40F44CF32572740B441 /* UIImage+Transform.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+Transform.m"; path = "SDWebImage/Core/UIImage+Transform.m"; sourceTree = "<group>"; };
		5CDE70A924EAFD031E709AA8BF825A1F /* MJRefreshBackFooter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshBackFooter.m; path = MJRefresh/Base/MJRefreshBackFooter.m; sourceTree = "<group>"; };
		5DFB730C1C7E7CED7E6C55F3B9ADD4DB /* SDWebImage.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImage.h; path = WebImage/SDWebImage.h; sourceTree = "<group>"; };
		5E7D3F2658C5FF9BC63C19C146238EF0 /* MJRefreshAutoFooter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshAutoFooter.h; path = MJRefresh/Base/MJRefreshAutoFooter.h; sourceTree = "<group>"; };
		5E979675DF7D8C7C19A7C033EAFD8D0A /* MASCompositeConstraint.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASCompositeConstraint.h; path = Masonry/MASCompositeConstraint.h; sourceTree = "<group>"; };
		6102ED22B4FE0206A781C9D7BBA72EC3 /* UIImage+ForceDecode.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+ForceDecode.h"; path = "SDWebImage/Core/UIImage+ForceDecode.h"; sourceTree = "<group>"; };
		6155E668105C1B6E682E91C5E4BA598B /* MJRefreshTrailer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshTrailer.h; path = MJRefresh/Base/MJRefreshTrailer.h; sourceTree = "<group>"; };
		61E0DDC8E4BB60D1261A51923E08930A /* UIButton+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIButton+AFNetworking.h"; path = "UIKit+AFNetworking/UIButton+AFNetworking.h"; sourceTree = "<group>"; };
		62D435F0D5C3E87CC68C0BF082F6E0E1 /* SDImageLoadersManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageLoadersManager.m; path = SDWebImage/Core/SDImageLoadersManager.m; sourceTree = "<group>"; };
		62F86770F9F19B6F695C8E60B1C5D5D9 /* UIImage+Metadata.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+Metadata.m"; path = "SDWebImage/Core/UIImage+Metadata.m"; sourceTree = "<group>"; };
		63A7FA2BEE803D9B01F922BB4BA6B397 /* UIView+WebCacheOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIView+WebCacheOperation.m"; path = "SDWebImage/Core/UIView+WebCacheOperation.m"; sourceTree = "<group>"; };
		64B2A4106E1694958F7E52A03FD4D307 /* FBSDKCoreKit.xcframework */ = {isa = PBXFileReference; includeInIndex = 1; name = FBSDKCoreKit.xcframework; path = XCFrameworks/FBSDKCoreKit.xcframework; sourceTree = "<group>"; };
		656A4AF3B8CCC51FCDB104A2D59BF3ED /* SDImageGIFCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageGIFCoder.m; path = SDWebImage/Core/SDImageGIFCoder.m; sourceTree = "<group>"; };
		65D65A306338246A5219CC28799C7C62 /* Pods-Nano_Loan.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Nano_Loan.debug.xcconfig"; sourceTree = "<group>"; };
		682D9EDF95080B223D70F2FE88D0230B /* SDWeakProxy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWeakProxy.m; path = SDWebImage/Private/SDWeakProxy.m; sourceTree = "<group>"; };
		68A29641B41F195F072F310AA55BAD34 /* SDCallbackQueue.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDCallbackQueue.m; path = SDWebImage/Core/SDCallbackQueue.m; sourceTree = "<group>"; };
		6902BD7A42E4D258F1245D623260283D /* SDImageCacheConfig.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCacheConfig.h; path = SDWebImage/Core/SDImageCacheConfig.h; sourceTree = "<group>"; };
		6973C74BF1F22A9F975FC92306896A8F /* SDDeviceHelper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDDeviceHelper.h; path = SDWebImage/Private/SDDeviceHelper.h; sourceTree = "<group>"; };
		69C77890227C35C3CBE090C193908421 /* FBSDKCoreKit_Basics-xcframeworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "FBSDKCoreKit_Basics-xcframeworks.sh"; sourceTree = "<group>"; };
		6BB5FD74D69F8D9F92FD56A887310AEF /* ViewController+MASAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "ViewController+MASAdditions.m"; path = "Masonry/ViewController+MASAdditions.m"; sourceTree = "<group>"; };
		6C5DFC15211FCFAE28B79354EE4CEE3F /* FBSDKCoreKit_Basics.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = FBSDKCoreKit_Basics.release.xcconfig; sourceTree = "<group>"; };
		6DA0E3AD4FC1D6F3B6C93543690C92BA /* UIImage+MultiFormat.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+MultiFormat.h"; path = "SDWebImage/Core/UIImage+MultiFormat.h"; sourceTree = "<group>"; };
		6E676D489B33C5238C15B404E6605BD9 /* SDAssociatedObject.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAssociatedObject.h; path = SDWebImage/Private/SDAssociatedObject.h; sourceTree = "<group>"; };
		706FC369D990F5523A89FB1F021642DE /* SDWebImage-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "SDWebImage-Info.plist"; sourceTree = "<group>"; };
		7096A2E55F0A442D8366AD0ABDE705E3 /* UIScrollView+MJExtension.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIScrollView+MJExtension.m"; path = "MJRefresh/UIScrollView+MJExtension.m"; sourceTree = "<group>"; };
		70D48B4765C3F81648F303123A6CFF49 /* ResourceBundle-SDWebImage-SDWebImage-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-SDWebImage-SDWebImage-Info.plist"; sourceTree = "<group>"; };
		70D4DA719BD7B77C17376AF0A5648950 /* SDWebImage.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SDWebImage.debug.xcconfig; sourceTree = "<group>"; };
		714D2BDD00D34C29FFD5B0A3D547DBD6 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = WebImage/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		71570934EAD5CD99703EEEE8805DBDF6 /* Pods-Nano_Loan */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-Nano_Loan"; path = Pods_Nano_Loan.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		722F2B52D360C4FF95D2EE305238F134 /* SDWebImageManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageManager.h; path = SDWebImage/Core/SDWebImageManager.h; sourceTree = "<group>"; };
		7232FDE2F429C285FBF950DD8CABC55F /* MJRefreshBackNormalFooter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshBackNormalFooter.h; path = MJRefresh/Custom/Footer/Back/MJRefreshBackNormalFooter.h; sourceTree = "<group>"; };
		7342B4C7AEDEEE66ED1D6D3FE4971057 /* SDWebImageDownloaderResponseModifier.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderResponseModifier.h; path = SDWebImage/Core/SDWebImageDownloaderResponseModifier.h; sourceTree = "<group>"; };
		736D36DEEA2F92CE85F489EBEAB55B3A /* Pods-Nano_Loan-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-Nano_Loan-dummy.m"; sourceTree = "<group>"; };
		74D75DCA93CEAA4E2B3B3B9C44385947 /* AFHTTPSessionManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFHTTPSessionManager.m; path = AFNetworking/AFHTTPSessionManager.m; sourceTree = "<group>"; };
		75A7F78D8D7CF1E9C7E6FBEA0EC09C95 /* MASConstraint.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MASConstraint.m; path = Masonry/MASConstraint.m; sourceTree = "<group>"; };
		76B1AD75019B89219529E41B4D9787BA /* SDDiskCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDDiskCache.h; path = SDWebImage/Core/SDDiskCache.h; sourceTree = "<group>"; };
		76C91FE790D5E160FC8F3E26FB1E9853 /* SDWebImageDefine.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDefine.m; path = SDWebImage/Core/SDWebImageDefine.m; sourceTree = "<group>"; };
		79B51AC8232F3628B5CB3E6486BD64C5 /* FBAEMKit-xcframeworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "FBAEMKit-xcframeworks.sh"; sourceTree = "<group>"; };
		79FE42DD72DB4329A1DD91BDA95CCF39 /* SDImageIOCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageIOCoder.m; path = SDWebImage/Core/SDImageIOCoder.m; sourceTree = "<group>"; };
		7BC7C2C2B94D1AD15FC72C72DB784368 /* AFAutoPurgingImageCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFAutoPurgingImageCache.m; path = "UIKit+AFNetworking/AFAutoPurgingImageCache.m"; sourceTree = "<group>"; };
		7BC9D7B5E24A570D4B15C828C283C110 /* NSArray+MASAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSArray+MASAdditions.h"; path = "Masonry/NSArray+MASAdditions.h"; sourceTree = "<group>"; };
		7C01C6CC53D312AF131AB282EA14947C /* SDWebImage-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SDWebImage-umbrella.h"; sourceTree = "<group>"; };
		7CC519CE74C273E3B96F42EFFE0C8A29 /* MASConstraintMaker.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASConstraintMaker.h; path = Masonry/MASConstraintMaker.h; sourceTree = "<group>"; };
		7E071087C7C250FD44AB3134AC332F07 /* SDImageAssetManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageAssetManager.h; path = SDWebImage/Private/SDImageAssetManager.h; sourceTree = "<group>"; };
		7E3097CFEFDA621E9FB0E62009FF87FC /* MJRefresh-MJRefresh.Privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "MJRefresh-MJRefresh.Privacy"; path = MJRefresh.Privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		7EE0B708208DA54C7792D0858F97CE53 /* Masonry.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Masonry.debug.xcconfig; sourceTree = "<group>"; };
		7F05B4565F31794F1F01F92F73471962 /* SDImageCachesManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCachesManager.h; path = SDWebImage/Core/SDImageCachesManager.h; sourceTree = "<group>"; };
		807409C5D7B136690C2D2F02306755F2 /* MJRefresh.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = MJRefresh.debug.xcconfig; sourceTree = "<group>"; };
		80BFBA2426AE7F22BE7D7BA238CD63A1 /* SDWebImage.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = SDWebImage.modulemap; sourceTree = "<group>"; };
		81793BE920E56B9034C38119DCA4E2FF /* UIImage+Transform.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+Transform.h"; path = "SDWebImage/Core/UIImage+Transform.h"; sourceTree = "<group>"; };
		827B4AEF873209876171726B873943C3 /* Masonry-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Masonry-umbrella.h"; sourceTree = "<group>"; };
		84D74C5AA71CCA8E912BDC9060B36F2D /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/QuartzCore.framework; sourceTree = DEVELOPER_DIR; };
		8687FBF3DABDC6266C65F5DBF9DA9F19 /* MJRefreshStateHeader.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshStateHeader.m; path = MJRefresh/Custom/Header/MJRefreshStateHeader.m; sourceTree = "<group>"; };
		88123D6FAA396D9AF75F25750F8D3EC1 /* MJRefreshBackGifFooter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshBackGifFooter.h; path = MJRefresh/Custom/Footer/Back/MJRefreshBackGifFooter.h; sourceTree = "<group>"; };
		886538B601C2BFB290390681C7D2764D /* SDDisplayLink.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDDisplayLink.m; path = SDWebImage/Private/SDDisplayLink.m; sourceTree = "<group>"; };
		88AE48064E7607C59F3469C1A20DCF9B /* SDImageAssetManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageAssetManager.m; path = SDWebImage/Private/SDImageAssetManager.m; sourceTree = "<group>"; };
		893098B9AC07B83F3D1078371E67A353 /* SDImageCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCoder.m; path = SDWebImage/Core/SDImageCoder.m; sourceTree = "<group>"; };
		89BC8B491110B74DD22065F31A45E4FA /* MJRefreshTrailer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshTrailer.m; path = MJRefresh/Base/MJRefreshTrailer.m; sourceTree = "<group>"; };
		8B6C1075FC4103587CE36B00E10346A6 /* MJRefreshBackStateFooter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshBackStateFooter.m; path = MJRefresh/Custom/Footer/Back/MJRefreshBackStateFooter.m; sourceTree = "<group>"; };
		8B8FAB0D627B17EDE1366984278705D9 /* MBProgressHUD */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = MBProgressHUD; path = MBProgressHUD.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		8BBD5C0F4322D4AA4696F1A3643C8E80 /* UIImageView+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImageView+WebCache.m"; path = "SDWebImage/Core/UIImageView+WebCache.m"; sourceTree = "<group>"; };
		8F3A6BFB7DF2F4D1FCF9A9C094140645 /* AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFNetworking.h; path = AFNetworking/AFNetworking.h; sourceTree = "<group>"; };
		8F750F6FA6B5CD517C0919E7A5E1AFA8 /* SDAnimatedImage.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAnimatedImage.m; path = SDWebImage/Core/SDAnimatedImage.m; sourceTree = "<group>"; };
		92A4C50F7C210F62C7B0B00E50420B9D /* UIKit+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIKit+AFNetworking.h"; path = "UIKit+AFNetworking/UIKit+AFNetworking.h"; sourceTree = "<group>"; };
		92E1B71DDBEC7BB36DCE14247966D279 /* MJRefreshBackFooter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshBackFooter.h; path = MJRefresh/Base/MJRefreshBackFooter.h; sourceTree = "<group>"; };
		93819A9FA4A1C9364BC4CFAF2C21E59B /* SDWebImagePrefetcher.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImagePrefetcher.m; path = SDWebImage/Core/SDWebImagePrefetcher.m; sourceTree = "<group>"; };
		941BCD2DB2DF9181C4F226A0B361883C /* SDImageCacheDefine.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCacheDefine.m; path = SDWebImage/Core/SDImageCacheDefine.m; sourceTree = "<group>"; };
		94261EA85639B90596EA529A46C865B4 /* AFSecurityPolicy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFSecurityPolicy.m; path = AFNetworking/AFSecurityPolicy.m; sourceTree = "<group>"; };
		943515464B1E8AEB6731548AB14F0F90 /* AFNetworkReachabilityManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFNetworkReachabilityManager.m; path = AFNetworking/AFNetworkReachabilityManager.m; sourceTree = "<group>"; };
		9496CC3367BB58851215CE8B727D8DDF /* MBProgressHUD-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "MBProgressHUD-Info.plist"; sourceTree = "<group>"; };
		94A8BBB41D9D4550AE7ED049F1312581 /* UIView+WebCacheOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIView+WebCacheOperation.h"; path = "SDWebImage/Core/UIView+WebCacheOperation.h"; sourceTree = "<group>"; };
		9523759946AEA807A7FC3992EEE6A188 /* MJRefreshGifHeader.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshGifHeader.m; path = MJRefresh/Custom/Header/MJRefreshGifHeader.m; sourceTree = "<group>"; };
		956F17F95447DE027B38056025CD7579 /* UIView+WebCacheState.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIView+WebCacheState.h"; path = "SDWebImage/Core/UIView+WebCacheState.h"; sourceTree = "<group>"; };
		958AA3429F2DCCF0665BBA30CD319856 /* SDWebImagePrefetcher.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImagePrefetcher.h; path = SDWebImage/Core/SDWebImagePrefetcher.h; sourceTree = "<group>"; };
		95F8CFDAAC7BE0E2115AD6EB7D3AE08E /* SDMemoryCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDMemoryCache.h; path = SDWebImage/Core/SDMemoryCache.h; sourceTree = "<group>"; };
		96D04E50579217816FBB63FB994A57BC /* SDImageCacheDefine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCacheDefine.h; path = SDWebImage/Core/SDImageCacheDefine.h; sourceTree = "<group>"; };
		9710F83AEA028B961B1E26291C7FB804 /* SDImageIOAnimatedCoderInternal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageIOAnimatedCoderInternal.h; path = SDWebImage/Private/SDImageIOAnimatedCoderInternal.h; sourceTree = "<group>"; };
		9790197BECAE968B2658D60B705AFC08 /* MJRefreshFooter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshFooter.h; path = MJRefresh/Base/MJRefreshFooter.h; sourceTree = "<group>"; };
		97979716DD9806B4792C15A5D39D8333 /* SDWebImageDownloader.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloader.m; path = SDWebImage/Core/SDWebImageDownloader.m; sourceTree = "<group>"; };
		97BB8B85E2684936285EDA6DE35E2037 /* MJRefreshAutoFooter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshAutoFooter.m; path = MJRefresh/Base/MJRefreshAutoFooter.m; sourceTree = "<group>"; };
		985C7EB42A1E7AAF6BB549FA115E971A /* UIButton+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIButton+WebCache.h"; path = "SDWebImage/Core/UIButton+WebCache.h"; sourceTree = "<group>"; };
		993021AD23EF5ACC22BB15271B4EDF7D /* SDImageTransformer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageTransformer.m; path = SDWebImage/Core/SDImageTransformer.m; sourceTree = "<group>"; };
		999AD72F3AB19F6B2C208A4F0F891C4D /* AFNetworking-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AFNetworking-umbrella.h"; sourceTree = "<group>"; };
		9A64A52A59C5953A138794E44AD80357 /* ResourceBundle-MJRefresh.Privacy-MJRefresh-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-MJRefresh.Privacy-MJRefresh-Info.plist"; sourceTree = "<group>"; };
		9AB6CF4CE580A096EAF2EFDD0FBCC4A8 /* SDImageCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCache.h; path = SDWebImage/Core/SDImageCache.h; sourceTree = "<group>"; };
		9B70D149FCB49334BC2AD62CB5D1AB5A /* UIRefreshControl+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIRefreshControl+AFNetworking.h"; path = "UIKit+AFNetworking/UIRefreshControl+AFNetworking.h"; sourceTree = "<group>"; };
		9B9368B43F10AB162CE33D27BE62BB8E /* UIView+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIView+WebCache.h"; path = "SDWebImage/Core/UIView+WebCache.h"; sourceTree = "<group>"; };
		9BB4F7879AC7F4550C0C2CDF8AD10959 /* Masonry.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = Masonry.modulemap; sourceTree = "<group>"; };
		9BF0B319ECC588E630BB061703763EFE /* MJRefreshAutoStateFooter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshAutoStateFooter.h; path = MJRefresh/Custom/Footer/Auto/MJRefreshAutoStateFooter.h; sourceTree = "<group>"; };
		9CEEC64A52F086D3BD98E7E5176352A1 /* Pods-Nano_Loan.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-Nano_Loan.modulemap"; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A287ED27D4E42096ACC7083DED4FA0DF /* SDWebImageOptionsProcessor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageOptionsProcessor.m; path = SDWebImage/Core/SDWebImageOptionsProcessor.m; sourceTree = "<group>"; };
		A41F5F108B4DE69CA413C9B0149F76BC /* UIActivityIndicatorView+AFNetworking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIActivityIndicatorView+AFNetworking.m"; path = "UIKit+AFNetworking/UIActivityIndicatorView+AFNetworking.m"; sourceTree = "<group>"; };
		A45316D7B79AA72FA0B7C77B0DCBBA54 /* UIImage+ForceDecode.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+ForceDecode.m"; path = "SDWebImage/Core/UIImage+ForceDecode.m"; sourceTree = "<group>"; };
		A4F0B132AAD230F5797450132A241F48 /* MJRefreshBackGifFooter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshBackGifFooter.m; path = MJRefresh/Custom/Footer/Back/MJRefreshBackGifFooter.m; sourceTree = "<group>"; };
		A4FA15D44DF6BAC7550EDEED10862AA3 /* AFNetworking */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = AFNetworking; path = AFNetworking.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A610EBE491953793B751C4215E5AA105 /* UIImage+Metadata.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+Metadata.h"; path = "SDWebImage/Core/UIImage+Metadata.h"; sourceTree = "<group>"; };
		A633B1FF01FBEA9F83E6016A9B19C0FB /* MJRefreshStateTrailer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshStateTrailer.m; path = MJRefresh/Custom/Trailer/MJRefreshStateTrailer.m; sourceTree = "<group>"; };
		A71EAA491D66A6EEFC169902BD0F4070 /* UIScrollView+MJRefresh.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIScrollView+MJRefresh.h"; path = "MJRefresh/UIScrollView+MJRefresh.h"; sourceTree = "<group>"; };
		A9B2427495CFF658EF228D79DC6D6749 /* SDImageIOAnimatedCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageIOAnimatedCoder.h; path = SDWebImage/Core/SDImageIOAnimatedCoder.h; sourceTree = "<group>"; };
		A9E6AD8E16A70E3319E981898B7BA652 /* MJRefresh.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = MJRefresh.modulemap; sourceTree = "<group>"; };
		AAFC26F4DD4F9E0979BE8DFF3500D769 /* SDAnimatedImageRep.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAnimatedImageRep.h; path = SDWebImage/Core/SDAnimatedImageRep.h; sourceTree = "<group>"; };
		ABF45CB970D10C85DDD6F87EE3595DFA /* SDInternalMacros.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDInternalMacros.h; path = SDWebImage/Private/SDInternalMacros.h; sourceTree = "<group>"; };
		ABF6BF2E51E9B8A6C13440ABFBF05186 /* MJRefreshFooter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshFooter.m; path = MJRefresh/Base/MJRefreshFooter.m; sourceTree = "<group>"; };
		AC7B0BA8555EF48ED3BE0E8F436BE98D /* MJRefreshStateTrailer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshStateTrailer.h; path = MJRefresh/Custom/Trailer/MJRefreshStateTrailer.h; sourceTree = "<group>"; };
		AD25DC83A3EBCFB7D606A43458EF66F2 /* SDWebImage-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SDWebImage-prefix.pch"; sourceTree = "<group>"; };
		AD5F065F05B624EFE063B302D95CE170 /* UIButton+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIButton+WebCache.m"; path = "SDWebImage/Core/UIButton+WebCache.m"; sourceTree = "<group>"; };
		AE7ADC7EC924839420F617F3FC59A4C9 /* SDImageLoader.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageLoader.h; path = SDWebImage/Core/SDImageLoader.h; sourceTree = "<group>"; };
		B0685ACA61B3EA89BF7D3B2E043062DD /* Pods-Nano_Loan-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Nano_Loan-acknowledgements.plist"; sourceTree = "<group>"; };
		B0B214D775196BA7CA8E17E53048A493 /* SDWebImage */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = SDWebImage; path = SDWebImage.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0B6059943320F774EC2A322398B3F12 /* UIScrollView+MJRefresh.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIScrollView+MJRefresh.m"; path = "MJRefresh/UIScrollView+MJRefresh.m"; sourceTree = "<group>"; };
		B1CF8F5D5FFE6E807F77474954F0D603 /* SDAssociatedObject.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAssociatedObject.m; path = SDWebImage/Private/SDAssociatedObject.m; sourceTree = "<group>"; };
		B43D44D3BBECD4BA8EB63397DCDAF2B0 /* MJRefreshHeader.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshHeader.m; path = MJRefresh/Base/MJRefreshHeader.m; sourceTree = "<group>"; };
		B55AB1316AD7B1BB02FCC223DE1A5670 /* SDImageCoderHelper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCoderHelper.h; path = SDWebImage/Core/SDImageCoderHelper.h; sourceTree = "<group>"; };
		B5D42D5E2CF5F6BD84D4A821D89CC11D /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		B61B81FAFC4064B4204502632063AE46 /* SDWebImageTransitionInternal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageTransitionInternal.h; path = SDWebImage/Private/SDWebImageTransitionInternal.h; sourceTree = "<group>"; };
		B8E113D5B2CC5F4AFBB17F4B14FB0553 /* AFNetworking.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AFNetworking.release.xcconfig; sourceTree = "<group>"; };
		BAFB84126C2A92409B3357591908FE47 /* UIView+WebCacheState.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIView+WebCacheState.m"; path = "SDWebImage/Core/UIView+WebCacheState.m"; sourceTree = "<group>"; };
		BB4E57E6F34C04827AD32F18DB5ED56D /* NSData+ImageContentType.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSData+ImageContentType.m"; path = "SDWebImage/Core/NSData+ImageContentType.m"; sourceTree = "<group>"; };
		BCB66A7577E1BEC2FA620327B38ECF36 /* SDWebImageDownloaderResponseModifier.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderResponseModifier.m; path = SDWebImage/Core/SDWebImageDownloaderResponseModifier.m; sourceTree = "<group>"; };
		BD91C34529A0E7613D392C3C61079114 /* SDMemoryCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDMemoryCache.m; path = SDWebImage/Core/SDMemoryCache.m; sourceTree = "<group>"; };
		BF66F6E35F90B08E6DDF6563A536B83D /* SDImageCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCache.m; path = SDWebImage/Core/SDImageCache.m; sourceTree = "<group>"; };
		BF77135EF3E2C3897AD8A453DDCD2BAB /* MJRefreshConst.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshConst.h; path = MJRefresh/MJRefreshConst.h; sourceTree = "<group>"; };
		C06F04758C4A9410D32E65A09EC66D97 /* View+MASAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "View+MASAdditions.h"; path = "Masonry/View+MASAdditions.h"; sourceTree = "<group>"; };
		C0E585FAC02532C13FF9B3C1663456DC /* MJRefreshNormalTrailer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshNormalTrailer.m; path = MJRefresh/Custom/Trailer/MJRefreshNormalTrailer.m; sourceTree = "<group>"; };
		C17DCDE0A27B622E3B23EA0B0551331E /* SDImageLoadersManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageLoadersManager.h; path = SDWebImage/Core/SDImageLoadersManager.h; sourceTree = "<group>"; };
		C18BD45EC5652DDEFE3B39AA4266A1D7 /* SDmetamacros.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDmetamacros.h; path = SDWebImage/Private/SDmetamacros.h; sourceTree = "<group>"; };
		C19E5924F9CA03210415C0A516139AEA /* Pods-Nano_Loan-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-Nano_Loan-acknowledgements.markdown"; sourceTree = "<group>"; };
		C20507CBFBAC4CD433F3590977845574 /* WKWebView+AFNetworking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "WKWebView+AFNetworking.m"; path = "UIKit+AFNetworking/WKWebView+AFNetworking.m"; sourceTree = "<group>"; };
		C2281BD83BF2BF5BB474029720C9DF5D /* SDInternalMacros.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDInternalMacros.m; path = SDWebImage/Private/SDInternalMacros.m; sourceTree = "<group>"; };
		C5B8C8565AFDF6696C00088B4CB745E2 /* MASConstraint+Private.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "MASConstraint+Private.h"; path = "Masonry/MASConstraint+Private.h"; sourceTree = "<group>"; };
		C5D8C13D979E5F303CCEDE97C2D9A8C1 /* UICollectionViewLayout+MJRefresh.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UICollectionViewLayout+MJRefresh.m"; path = "MJRefresh/UICollectionViewLayout+MJRefresh.m"; sourceTree = "<group>"; };
		C7236E2EEB4AA6D1846AD94028AEFC7D /* SDImageAWebPCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageAWebPCoder.h; path = SDWebImage/Core/SDImageAWebPCoder.h; sourceTree = "<group>"; };
		C757B72EF0D6C73CA87D858AFC5A3323 /* Pods-Nano_Loan-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Nano_Loan-Info.plist"; sourceTree = "<group>"; };
		C869AA260FDC04C618F3773DAE02A5E0 /* SDWebImageDownloaderDecryptor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderDecryptor.m; path = SDWebImage/Core/SDWebImageDownloaderDecryptor.m; sourceTree = "<group>"; };
		C940CCDF172766EA9C6444C47C443E02 /* MJRefreshConfig.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshConfig.m; path = MJRefresh/MJRefreshConfig.m; sourceTree = "<group>"; };
		CA1CAAE1C179643E221552C3E0C1295B /* FBAEMKit.xcframework */ = {isa = PBXFileReference; includeInIndex = 1; name = FBAEMKit.xcframework; path = XCFrameworks/FBAEMKit.xcframework; sourceTree = "<group>"; };
		CA413C8E2996E5305CFF922E51E27757 /* SDWebImage.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SDWebImage.release.xcconfig; sourceTree = "<group>"; };
		CBEDB9628337222BB9BC7F98B66F0BB8 /* SDWebImageOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageOperation.m; path = SDWebImage/Core/SDWebImageOperation.m; sourceTree = "<group>"; };
		CD5E1ACF7FF01A08ACC0B1D8D449367A /* MBProgressHUD-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "MBProgressHUD-prefix.pch"; sourceTree = "<group>"; };
		CDF9C8AABF582CFFD2CFDF8FB53E3981 /* UIProgressView+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIProgressView+AFNetworking.h"; path = "UIKit+AFNetworking/UIProgressView+AFNetworking.h"; sourceTree = "<group>"; };
		CF1281E58AA1045D4B7F33FC56691C42 /* SDWebImage-SDWebImage */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "SDWebImage-SDWebImage"; path = SDWebImage.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		D072783E4919417ACCE7F10E11C833A8 /* FBSDKCoreKit-xcframeworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "FBSDKCoreKit-xcframeworks.sh"; sourceTree = "<group>"; };
		D16C947CD037B530ACA64CF549B70870 /* SDImageCachesManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCachesManager.m; path = SDWebImage/Core/SDImageCachesManager.m; sourceTree = "<group>"; };
		D1A0A439782F1962B984760F777978B3 /* SDDiskCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDDiskCache.m; path = SDWebImage/Core/SDDiskCache.m; sourceTree = "<group>"; };
		D252DF203C2311D237C3C3E611BFCBEF /* MJRefreshAutoGifFooter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshAutoGifFooter.h; path = MJRefresh/Custom/Footer/Auto/MJRefreshAutoGifFooter.h; sourceTree = "<group>"; };
		D29D20F2B4EA95072041BBBE9BDFD161 /* Masonry-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Masonry-dummy.m"; sourceTree = "<group>"; };
		D2BFA139BE3968E4C9CE45F9CFFB42F8 /* MJRefresh.bundle */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = "wrapper.plug-in"; name = MJRefresh.bundle; path = MJRefresh/MJRefresh.bundle; sourceTree = "<group>"; };
		D2DF114CE345AFA2323BF027E9EC94E6 /* SDImageCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCoder.h; path = SDWebImage/Core/SDImageCoder.h; sourceTree = "<group>"; };
		D2FE687682EA2A0096442C12099F18D6 /* UIImageView+HighlightedWebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImageView+HighlightedWebCache.h"; path = "SDWebImage/Core/UIImageView+HighlightedWebCache.h"; sourceTree = "<group>"; };
		D38DA2F32B1FE67EAC59B5B3571FB671 /* SDWebImageDownloaderRequestModifier.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderRequestModifier.m; path = SDWebImage/Core/SDWebImageDownloaderRequestModifier.m; sourceTree = "<group>"; };
		D41021804D6338C082E57D172E3A8035 /* AFURLSessionManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFURLSessionManager.h; path = AFNetworking/AFURLSessionManager.h; sourceTree = "<group>"; };
		D416291597A3B1584F853B95842D122A /* UIImageView+AFNetworking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImageView+AFNetworking.m"; path = "UIKit+AFNetworking/UIImageView+AFNetworking.m"; sourceTree = "<group>"; };
		D6031285ADB535394FE605C0C753CF45 /* MASViewConstraint.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MASViewConstraint.m; path = Masonry/MASViewConstraint.m; sourceTree = "<group>"; };
		D62DCCA4E0332332E4B9024D47190CFB /* SDDeviceHelper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDDeviceHelper.m; path = SDWebImage/Private/SDDeviceHelper.m; sourceTree = "<group>"; };
		D6CCE9C4E8840BD93B5740E8D805624D /* MJRefresh-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "MJRefresh-prefix.pch"; sourceTree = "<group>"; };
		D70DB7D95CF4D6B925A4592F4D4F736F /* SDImageFrame.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageFrame.h; path = SDWebImage/Core/SDImageFrame.h; sourceTree = "<group>"; };
		D71FE77CEB4D8192D475D25D5199DEDF /* MJRefreshComponent.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshComponent.m; path = MJRefresh/Base/MJRefreshComponent.m; sourceTree = "<group>"; };
		D78ECA51F4A5AAB9B6983388DF80A57D /* SDWebImageCompat.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageCompat.m; path = SDWebImage/Core/SDWebImageCompat.m; sourceTree = "<group>"; };
		D8264159EC9493FE0E54E21D38D4B568 /* SDImageAWebPCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageAWebPCoder.m; path = SDWebImage/Core/SDImageAWebPCoder.m; sourceTree = "<group>"; };
		D838629758902C8EAA9209E37E6D7986 /* SDDisplayLink.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDDisplayLink.h; path = SDWebImage/Private/SDDisplayLink.h; sourceTree = "<group>"; };
		D8A58FA6313F2D0A1541C2945A374091 /* SDWebImageManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageManager.m; path = SDWebImage/Core/SDWebImageManager.m; sourceTree = "<group>"; };
		D9FFC56D1B71CF4225F1020056F519FA /* SDImageLoader.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageLoader.m; path = SDWebImage/Core/SDImageLoader.m; sourceTree = "<group>"; };
		DA4B25ED4A76B2949F4873C5DB345946 /* NSData+ImageContentType.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSData+ImageContentType.h"; path = "SDWebImage/Core/NSData+ImageContentType.h"; sourceTree = "<group>"; };
		DA95C7182540A3485F8F19EA9AC04CC6 /* SDImageCodersManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCodersManager.h; path = SDWebImage/Core/SDImageCodersManager.h; sourceTree = "<group>"; };
		DAA02086A5396C2AA0002DFA34B0634A /* MBProgressHUD.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = MBProgressHUD.release.xcconfig; sourceTree = "<group>"; };
		DC0F93774456DC84A27D91E611DBD126 /* MJRefreshAutoNormalFooter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshAutoNormalFooter.m; path = MJRefresh/Custom/Footer/Auto/MJRefreshAutoNormalFooter.m; sourceTree = "<group>"; };
		DC6809D81689953D9097375854A01017 /* AFCompatibilityMacros.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFCompatibilityMacros.h; path = AFNetworking/AFCompatibilityMacros.h; sourceTree = "<group>"; };
		DDE8BFE78F9CD7EE55A53388621C5B35 /* ImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ImageIO.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/ImageIO.framework; sourceTree = DEVELOPER_DIR; };
		DEFAD2C68AFA34BC1B2B97D756B6D000 /* MASViewAttribute.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MASViewAttribute.m; path = Masonry/MASViewAttribute.m; sourceTree = "<group>"; };
		DFBCAAB37C2CEBD6FDCABDF38D0C83E7 /* FBAEMKit.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = FBAEMKit.debug.xcconfig; sourceTree = "<group>"; };
		E2EDFEF94C31FF176835B73A1AEA3F1D /* MJRefreshGifHeader.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshGifHeader.h; path = MJRefresh/Custom/Header/MJRefreshGifHeader.h; sourceTree = "<group>"; };
		E2EEB1D439A43D9E505CD6101ED63DBC /* WKWebView+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "WKWebView+AFNetworking.h"; path = "UIKit+AFNetworking/WKWebView+AFNetworking.h"; sourceTree = "<group>"; };
		E33C1FBC36FFBCFACED96EB026CF2791 /* SDWebImageCompat.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageCompat.h; path = SDWebImage/Core/SDWebImageCompat.h; sourceTree = "<group>"; };
		E37230CAD386F393FE18754CFB5B04C7 /* FBAEMKit.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = FBAEMKit.release.xcconfig; sourceTree = "<group>"; };
		E49D6D248DD1CEE584E6776B9164A1B2 /* MJRefresh */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = MJRefresh; path = MJRefresh.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		E57CAA7DF42DCCC758F15642B6D17ED3 /* SDImageHEICCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageHEICCoder.m; path = SDWebImage/Core/SDImageHEICCoder.m; sourceTree = "<group>"; };
		E5C50857692780902AB615556CCBEADF /* AFNetworking-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "AFNetworking-dummy.m"; sourceTree = "<group>"; };
		E5DA28F82FA67E32883164279342B1C5 /* UIView+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIView+WebCache.m"; path = "SDWebImage/Core/UIView+WebCache.m"; sourceTree = "<group>"; };
		E6FBC862BCA3841DA4EB10CFE49BF37E /* SDImageHEICCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageHEICCoder.h; path = SDWebImage/Core/SDImageHEICCoder.h; sourceTree = "<group>"; };
		E88BBCCAB699750CA8370D016E6AA34D /* MJRefresh-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "MJRefresh-umbrella.h"; sourceTree = "<group>"; };
		E8F3DD417554E55B35CC25168BFD9471 /* SDImageGraphics.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageGraphics.m; path = SDWebImage/Core/SDImageGraphics.m; sourceTree = "<group>"; };
		E91C86FE61FCDE43F30523D916FBA3FF /* SDFileAttributeHelper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDFileAttributeHelper.m; path = SDWebImage/Private/SDFileAttributeHelper.m; sourceTree = "<group>"; };
		EAB5C056CF351D2765636FC735C56EDF /* NSBezierPath+SDRoundedCorners.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSBezierPath+SDRoundedCorners.m"; path = "SDWebImage/Private/NSBezierPath+SDRoundedCorners.m"; sourceTree = "<group>"; };
		EADA52AD6202CAF4A1C888B13C187276 /* FBSDKCoreKit_Basics.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = FBSDKCoreKit_Basics.debug.xcconfig; sourceTree = "<group>"; };
		EAF2695AE98027DE219427774EA00F4C /* MASViewAttribute.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASViewAttribute.h; path = Masonry/MASViewAttribute.h; sourceTree = "<group>"; };
		EC81FB402799CE870531BA35336C6B62 /* UIButton+AFNetworking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIButton+AFNetworking.m"; path = "UIKit+AFNetworking/UIButton+AFNetworking.m"; sourceTree = "<group>"; };
		ED7DC2C78A9694F09928BCCD132D0888 /* MASLayoutConstraint.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MASLayoutConstraint.h; path = Masonry/MASLayoutConstraint.h; sourceTree = "<group>"; };
		EF3F7035FE6372BC0DAD6729D5E56FDF /* MJRefreshBackStateFooter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshBackStateFooter.h; path = MJRefresh/Custom/Footer/Back/MJRefreshBackStateFooter.h; sourceTree = "<group>"; };
		EF6DD8858AE767C8C8BB967DC9C7303B /* MJRefreshStateHeader.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshStateHeader.h; path = MJRefresh/Custom/Header/MJRefreshStateHeader.h; sourceTree = "<group>"; };
		F07AC49F259A08F316B54C038F1CF2A1 /* SDAnimatedImageView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAnimatedImageView.m; path = SDWebImage/Core/SDAnimatedImageView.m; sourceTree = "<group>"; };
		F10494B4D30C762999F0D759F798F5B2 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = MJRefresh/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		F113E21168660F663B4A5426E37C33E8 /* SDImageFramePool.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageFramePool.m; path = SDWebImage/Private/SDImageFramePool.m; sourceTree = "<group>"; };
		F2CA872A5138F1B8EF538FC1C4109152 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreGraphics.framework; sourceTree = DEVELOPER_DIR; };
		F31EA86CBB965ECA0C1C299704FBBB5A /* MASCompositeConstraint.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MASCompositeConstraint.m; path = Masonry/MASCompositeConstraint.m; sourceTree = "<group>"; };
		F3200DCC36729B205E9E18CAF440B2A9 /* MJRefreshHeader.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshHeader.h; path = MJRefresh/Base/MJRefreshHeader.h; sourceTree = "<group>"; };
		********18AC31E3A631F72434F9EAC1 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		F36091220F6F67E1E1AC4B5A2C36A12E /* AFNetworkActivityIndicatorManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFNetworkActivityIndicatorManager.h; path = "UIKit+AFNetworking/AFNetworkActivityIndicatorManager.h"; sourceTree = "<group>"; };
		F4C3BD86F9B8643726AB3BE8CF5D1CA7 /* MJRefreshNormalHeader.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJRefreshNormalHeader.m; path = MJRefresh/Custom/Header/MJRefreshNormalHeader.m; sourceTree = "<group>"; };
		F4FB9E39D97BFFA46C61B3A9007DD77E /* SDFileAttributeHelper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDFileAttributeHelper.h; path = SDWebImage/Private/SDFileAttributeHelper.h; sourceTree = "<group>"; };
		F5A610DA785C65043A60D604CF3685A1 /* SDWebImageIndicator.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageIndicator.h; path = SDWebImage/Core/SDWebImageIndicator.h; sourceTree = "<group>"; };
		F5E5245F32D37981F8CD1435A74E9A9C /* SDAnimatedImage.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAnimatedImage.h; path = SDWebImage/Core/SDAnimatedImage.h; sourceTree = "<group>"; };
		F6B8FCBF3C86AD3E30D912CCA23848C4 /* SDWeakProxy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWeakProxy.h; path = SDWebImage/Private/SDWeakProxy.h; sourceTree = "<group>"; };
		F785F6D19FBAEF6E55D8C96685444712 /* SDWebImageIndicator.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageIndicator.m; path = SDWebImage/Core/SDWebImageIndicator.m; sourceTree = "<group>"; };
		F7FE5D93FEFB9568DE757E468C8E3EF4 /* View+MASAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "View+MASAdditions.m"; path = "Masonry/View+MASAdditions.m"; sourceTree = "<group>"; };
		F85B84FEEDC22CE1693E773B11192ED5 /* SDWebImageOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageOperation.h; path = SDWebImage/Core/SDWebImageOperation.h; sourceTree = "<group>"; };
		F8D22908AF899046E620B6B9CA46CA0F /* SDWebImageOptionsProcessor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageOptionsProcessor.h; path = SDWebImage/Core/SDWebImageOptionsProcessor.h; sourceTree = "<group>"; };
		F917441BA05D3158473DBA00A2A559BE /* SDWebImageDownloaderOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderOperation.h; path = SDWebImage/Core/SDWebImageDownloaderOperation.h; sourceTree = "<group>"; };
		F9B3CFDE5CF3B5EDB2FDF119FAEB0909 /* NSButton+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSButton+WebCache.m"; path = "SDWebImage/Core/NSButton+WebCache.m"; sourceTree = "<group>"; };
		F9EC47F507A90874FBE790D58D846835 /* AFURLResponseSerialization.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFURLResponseSerialization.h; path = AFNetworking/AFURLResponseSerialization.h; sourceTree = "<group>"; };
		FC09B20DF07A51D85D8FC96DC354D860 /* FBSDKCoreKit.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = FBSDKCoreKit.debug.xcconfig; sourceTree = "<group>"; };
		FCAC3C7409CC871BC8709618A577BE33 /* UICollectionViewLayout+MJRefresh.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UICollectionViewLayout+MJRefresh.h"; path = "MJRefresh/UICollectionViewLayout+MJRefresh.h"; sourceTree = "<group>"; };
		FCE3EBF3E49A379F42253639B3871893 /* MJRefreshComponent.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJRefreshComponent.h; path = MJRefresh/Base/MJRefreshComponent.h; sourceTree = "<group>"; };
		FD8631AF5229AEDEE24C1FDCFA4A33D9 /* SDWebImageDownloaderRequestModifier.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderRequestModifier.h; path = SDWebImage/Core/SDWebImageDownloaderRequestModifier.h; sourceTree = "<group>"; };
		FDBA4E418E7FE6B322DCD03CB1007ECB /* NSLayoutConstraint+MASDebugAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSLayoutConstraint+MASDebugAdditions.h"; path = "Masonry/NSLayoutConstraint+MASDebugAdditions.h"; sourceTree = "<group>"; };
		FF17D24562FC7A3B69EC310B4A1AF066 /* SDAnimatedImageRep.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAnimatedImageRep.m; path = SDWebImage/Core/SDAnimatedImageRep.m; sourceTree = "<group>"; };
		FF97549E8708AB5DB55C6D68DA24E7E6 /* NSArray+MASShorthandAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSArray+MASShorthandAdditions.h"; path = "Masonry/NSArray+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		FFD7FCE84BFE35C3BAE280490C66B7DE /* SDWebImageCacheSerializer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageCacheSerializer.h; path = SDWebImage/Core/SDWebImageCacheSerializer.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0BAA7D30DDCC9CA5671B4C061A66BD02 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		11690A588400BBB164423D5F86311C35 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				83A4F2816C1B3F072E1A26A34C3BC4AC /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		12A799DC8ABB2C283ADDDED4421A5EAB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D663837F4347AF58660EE6F7FD426ECE /* Foundation.framework in Frameworks */,
				4571A0EA37DC84F39E3830D38A1531AB /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		12E99800F48CD09032DD519E22C85EE4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		37145BAEB1B97BA7ADD7D6C3E86E99BD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EABCB60A26B06BF576E50BBD2F89A385 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3A5330E1BD187252F408EBB46F1BDC42 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8414CFEEB64ACA817EB88D2FEADDA3B3 /* Foundation.framework in Frameworks */,
				3777CD89D444CBBB48AE323B303F3FC7 /* ImageIO.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		771C25D2FDF8CD53A4785F702BAF1F1C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				08BCAAADA3FA095D1BD15B8263004228 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CA6527CF424B1AA12AF1D0BB36EBAF73 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4A2D7878579537881DD3859B3359F702 /* CoreGraphics.framework in Frameworks */,
				723D1925C39764631641D0DBFDF6833B /* Foundation.framework in Frameworks */,
				A1A1266FD70CEB545FD7602CC33F2F7B /* QuartzCore.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		025B553D18DEFB6CA42CB1E612378177 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				64B2A4106E1694958F7E52A03FD4D307 /* FBSDKCoreKit.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		0914259B5AE9890B4C9D31575E6DBD7C /* Core */ = {
			isa = PBXGroup;
			children = (
				1DEC8F932E25A416DEC3337AB32F1DB6 /* NSBezierPath+SDRoundedCorners.h */,
				EAB5C056CF351D2765636FC735C56EDF /* NSBezierPath+SDRoundedCorners.m */,
				3022DE9ECACD33B1B6954AFBE01D1D43 /* NSButton+WebCache.h */,
				F9B3CFDE5CF3B5EDB2FDF119FAEB0909 /* NSButton+WebCache.m */,
				DA4B25ED4A76B2949F4873C5DB345946 /* NSData+ImageContentType.h */,
				BB4E57E6F34C04827AD32F18DB5ED56D /* NSData+ImageContentType.m */,
				4B954FC92C73D230D63697C0223C8528 /* NSImage+Compatibility.h */,
				41E3C62DE6443E9515E490AE0C89BCA6 /* NSImage+Compatibility.m */,
				F5E5245F32D37981F8CD1435A74E9A9C /* SDAnimatedImage.h */,
				8F750F6FA6B5CD517C0919E7A5E1AFA8 /* SDAnimatedImage.m */,
				4BFCCF7837E110AD3AEA7D06538FF77E /* SDAnimatedImagePlayer.h */,
				45232534F89924F94768CE145BD803CF /* SDAnimatedImagePlayer.m */,
				AAFC26F4DD4F9E0979BE8DFF3500D769 /* SDAnimatedImageRep.h */,
				FF17D24562FC7A3B69EC310B4A1AF066 /* SDAnimatedImageRep.m */,
				1EDC0981E6F227433F178D728CC017A0 /* SDAnimatedImageView.h */,
				F07AC49F259A08F316B54C038F1CF2A1 /* SDAnimatedImageView.m */,
				46491D498B464BEBE0EC959D23192818 /* SDAnimatedImageView+WebCache.h */,
				100AECAC3BB237B7815607A9620170E6 /* SDAnimatedImageView+WebCache.m */,
				6E676D489B33C5238C15B404E6605BD9 /* SDAssociatedObject.h */,
				B1CF8F5D5FFE6E807F77474954F0D603 /* SDAssociatedObject.m */,
				1EFBC7CEE060C7FDF33DC6282F25269F /* SDAsyncBlockOperation.h */,
				4371426C5985EFEF4104C3FB4AA1CD57 /* SDAsyncBlockOperation.m */,
				5963BA984D603144437C80A5A3B695CF /* SDCallbackQueue.h */,
				68A29641B41F195F072F310AA55BAD34 /* SDCallbackQueue.m */,
				6973C74BF1F22A9F975FC92306896A8F /* SDDeviceHelper.h */,
				D62DCCA4E0332332E4B9024D47190CFB /* SDDeviceHelper.m */,
				76B1AD75019B89219529E41B4D9787BA /* SDDiskCache.h */,
				D1A0A439782F1962B984760F777978B3 /* SDDiskCache.m */,
				D838629758902C8EAA9209E37E6D7986 /* SDDisplayLink.h */,
				886538B601C2BFB290390681C7D2764D /* SDDisplayLink.m */,
				F4FB9E39D97BFFA46C61B3A9007DD77E /* SDFileAttributeHelper.h */,
				E91C86FE61FCDE43F30523D916FBA3FF /* SDFileAttributeHelper.m */,
				59D22431E34837621987CFA0E071C9C8 /* SDGraphicsImageRenderer.h */,
				05AE5833538310BC194D7C9EEC7FC83F /* SDGraphicsImageRenderer.m */,
				2C788B033735C02C5668C07553CA91FA /* SDImageAPNGCoder.h */,
				547A586690048889779EA64FC1DB5B1A /* SDImageAPNGCoder.m */,
				7E071087C7C250FD44AB3134AC332F07 /* SDImageAssetManager.h */,
				88AE48064E7607C59F3469C1A20DCF9B /* SDImageAssetManager.m */,
				C7236E2EEB4AA6D1846AD94028AEFC7D /* SDImageAWebPCoder.h */,
				D8264159EC9493FE0E54E21D38D4B568 /* SDImageAWebPCoder.m */,
				9AB6CF4CE580A096EAF2EFDD0FBCC4A8 /* SDImageCache.h */,
				BF66F6E35F90B08E6DDF6563A536B83D /* SDImageCache.m */,
				6902BD7A42E4D258F1245D623260283D /* SDImageCacheConfig.h */,
				564BA9CF40661B3FCD7E41191921750C /* SDImageCacheConfig.m */,
				96D04E50579217816FBB63FB994A57BC /* SDImageCacheDefine.h */,
				941BCD2DB2DF9181C4F226A0B361883C /* SDImageCacheDefine.m */,
				7F05B4565F31794F1F01F92F73471962 /* SDImageCachesManager.h */,
				D16C947CD037B530ACA64CF549B70870 /* SDImageCachesManager.m */,
				49EC12F59B1E51AF51B523248388A148 /* SDImageCachesManagerOperation.h */,
				4ADFEFE6DD12D5AF909630EE100337C5 /* SDImageCachesManagerOperation.m */,
				D2DF114CE345AFA2323BF027E9EC94E6 /* SDImageCoder.h */,
				893098B9AC07B83F3D1078371E67A353 /* SDImageCoder.m */,
				B55AB1316AD7B1BB02FCC223DE1A5670 /* SDImageCoderHelper.h */,
				2D010C730376F25C4204BE83C3AAD783 /* SDImageCoderHelper.m */,
				DA95C7182540A3485F8F19EA9AC04CC6 /* SDImageCodersManager.h */,
				0587A346147AAEABC7D97135E800FC85 /* SDImageCodersManager.m */,
				D70DB7D95CF4D6B925A4592F4D4F736F /* SDImageFrame.h */,
				2A6CB6C698E97EE524E3D37B9CFBB489 /* SDImageFrame.m */,
				28CE361547B2B6EAD184FC4F74FFFE2C /* SDImageFramePool.h */,
				F113E21168660F663B4A5426E37C33E8 /* SDImageFramePool.m */,
				538A95AED784C754FF197437BFCE6686 /* SDImageGIFCoder.h */,
				656A4AF3B8CCC51FCDB104A2D59BF3ED /* SDImageGIFCoder.m */,
				5892ED39C08A09AB1A6D272451BAE5E8 /* SDImageGraphics.h */,
				E8F3DD417554E55B35CC25168BFD9471 /* SDImageGraphics.m */,
				E6FBC862BCA3841DA4EB10CFE49BF37E /* SDImageHEICCoder.h */,
				E57CAA7DF42DCCC758F15642B6D17ED3 /* SDImageHEICCoder.m */,
				A9B2427495CFF658EF228D79DC6D6749 /* SDImageIOAnimatedCoder.h */,
				0437A147EC9E9B4E9CC5F1F063AB0B93 /* SDImageIOAnimatedCoder.m */,
				9710F83AEA028B961B1E26291C7FB804 /* SDImageIOAnimatedCoderInternal.h */,
				149D5BCEBF0EE0AB27A71762B0A0E63B /* SDImageIOCoder.h */,
				79FE42DD72DB4329A1DD91BDA95CCF39 /* SDImageIOCoder.m */,
				AE7ADC7EC924839420F617F3FC59A4C9 /* SDImageLoader.h */,
				D9FFC56D1B71CF4225F1020056F519FA /* SDImageLoader.m */,
				C17DCDE0A27B622E3B23EA0B0551331E /* SDImageLoadersManager.h */,
				62D435F0D5C3E87CC68C0BF082F6E0E1 /* SDImageLoadersManager.m */,
				0B5E74804F609475F7372460A78DE8B1 /* SDImageTransformer.h */,
				993021AD23EF5ACC22BB15271B4EDF7D /* SDImageTransformer.m */,
				ABF45CB970D10C85DDD6F87EE3595DFA /* SDInternalMacros.h */,
				C2281BD83BF2BF5BB474029720C9DF5D /* SDInternalMacros.m */,
				95F8CFDAAC7BE0E2115AD6EB7D3AE08E /* SDMemoryCache.h */,
				BD91C34529A0E7613D392C3C61079114 /* SDMemoryCache.m */,
				C18BD45EC5652DDEFE3B39AA4266A1D7 /* SDmetamacros.h */,
				F6B8FCBF3C86AD3E30D912CCA23848C4 /* SDWeakProxy.h */,
				682D9EDF95080B223D70F2FE88D0230B /* SDWeakProxy.m */,
				5DFB730C1C7E7CED7E6C55F3B9ADD4DB /* SDWebImage.h */,
				023B4F18A51DEDE66731835742BD6088 /* SDWebImageCacheKeyFilter.h */,
				366488E341C4071AAD5962E9517BD973 /* SDWebImageCacheKeyFilter.m */,
				FFD7FCE84BFE35C3BAE280490C66B7DE /* SDWebImageCacheSerializer.h */,
				5A5AF98857754B424A3A624850EDA0FB /* SDWebImageCacheSerializer.m */,
				E33C1FBC36FFBCFACED96EB026CF2791 /* SDWebImageCompat.h */,
				D78ECA51F4A5AAB9B6983388DF80A57D /* SDWebImageCompat.m */,
				42C095FCD5E6908F2A488DCD557967FD /* SDWebImageDefine.h */,
				76C91FE790D5E160FC8F3E26FB1E9853 /* SDWebImageDefine.m */,
				1AB98E5B2A7F5B2EB08A105196863D7E /* SDWebImageDownloader.h */,
				97979716DD9806B4792C15A5D39D8333 /* SDWebImageDownloader.m */,
				53FA74C38ADE749EAB807D3090EF1373 /* SDWebImageDownloaderConfig.h */,
				307C93B0A95C9A9FA6B6318B6071CA56 /* SDWebImageDownloaderConfig.m */,
				31C1E203148A786A2C4BFD6ED5BA7DF4 /* SDWebImageDownloaderDecryptor.h */,
				C869AA260FDC04C618F3773DAE02A5E0 /* SDWebImageDownloaderDecryptor.m */,
				F917441BA05D3158473DBA00A2A559BE /* SDWebImageDownloaderOperation.h */,
				2461423ABCA1353E9AFCAF72B1A06BDA /* SDWebImageDownloaderOperation.m */,
				FD8631AF5229AEDEE24C1FDCFA4A33D9 /* SDWebImageDownloaderRequestModifier.h */,
				D38DA2F32B1FE67EAC59B5B3571FB671 /* SDWebImageDownloaderRequestModifier.m */,
				7342B4C7AEDEEE66ED1D6D3FE4971057 /* SDWebImageDownloaderResponseModifier.h */,
				BCB66A7577E1BEC2FA620327B38ECF36 /* SDWebImageDownloaderResponseModifier.m */,
				2737C54349985D603F3EEA20519667C6 /* SDWebImageError.h */,
				451768D7F5A52CCC3C00D92A8A6C245F /* SDWebImageError.m */,
				F5A610DA785C65043A60D604CF3685A1 /* SDWebImageIndicator.h */,
				F785F6D19FBAEF6E55D8C96685444712 /* SDWebImageIndicator.m */,
				722F2B52D360C4FF95D2EE305238F134 /* SDWebImageManager.h */,
				D8A58FA6313F2D0A1541C2945A374091 /* SDWebImageManager.m */,
				F85B84FEEDC22CE1693E773B11192ED5 /* SDWebImageOperation.h */,
				CBEDB9628337222BB9BC7F98B66F0BB8 /* SDWebImageOperation.m */,
				F8D22908AF899046E620B6B9CA46CA0F /* SDWebImageOptionsProcessor.h */,
				A287ED27D4E42096ACC7083DED4FA0DF /* SDWebImageOptionsProcessor.m */,
				958AA3429F2DCCF0665BBA30CD319856 /* SDWebImagePrefetcher.h */,
				93819A9FA4A1C9364BC4CFAF2C21E59B /* SDWebImagePrefetcher.m */,
				0E3AC68774EBD70CA88496DBD3A19FF6 /* SDWebImageTransition.h */,
				3304CFA7E872D5643B5B02C72BAEAB07 /* SDWebImageTransition.m */,
				B61B81FAFC4064B4204502632063AE46 /* SDWebImageTransitionInternal.h */,
				985C7EB42A1E7AAF6BB549FA115E971A /* UIButton+WebCache.h */,
				AD5F065F05B624EFE063B302D95CE170 /* UIButton+WebCache.m */,
				08C3D6EA978F6FF57BE3D91B257A18F6 /* UIColor+SDHexString.h */,
				31AE83AF573F7CE65709672553568710 /* UIColor+SDHexString.m */,
				5910E5BF6CA1CE68DC180FD07454DA57 /* UIImage+ExtendedCacheData.h */,
				0081A57C73420FA6768D1DF16C510D4F /* UIImage+ExtendedCacheData.m */,
				6102ED22B4FE0206A781C9D7BBA72EC3 /* UIImage+ForceDecode.h */,
				A45316D7B79AA72FA0B7C77B0DCBBA54 /* UIImage+ForceDecode.m */,
				4FAAD439B70F4518579093867C8A582F /* UIImage+GIF.h */,
				3BCEF0C6D95330B8BB6E9DAB91371274 /* UIImage+GIF.m */,
				522173517F8D5E0CFDB0813826D9B87E /* UIImage+MemoryCacheCost.h */,
				36A6004F83217C098FE98F2CC5DD3F85 /* UIImage+MemoryCacheCost.m */,
				A610EBE491953793B751C4215E5AA105 /* UIImage+Metadata.h */,
				62F86770F9F19B6F695C8E60B1C5D5D9 /* UIImage+Metadata.m */,
				6DA0E3AD4FC1D6F3B6C93543690C92BA /* UIImage+MultiFormat.h */,
				54D2BD3770D5C912EE835B9FE481F98B /* UIImage+MultiFormat.m */,
				81793BE920E56B9034C38119DCA4E2FF /* UIImage+Transform.h */,
				5C0E534E1E01F40F44CF32572740B441 /* UIImage+Transform.m */,
				D2FE687682EA2A0096442C12099F18D6 /* UIImageView+HighlightedWebCache.h */,
				30F34249C4EBD69AF03F67D07C9F4AD7 /* UIImageView+HighlightedWebCache.m */,
				20E960E84CE2327E5B7BA6948C92CFCE /* UIImageView+WebCache.h */,
				8BBD5C0F4322D4AA4696F1A3643C8E80 /* UIImageView+WebCache.m */,
				9B9368B43F10AB162CE33D27BE62BB8E /* UIView+WebCache.h */,
				E5DA28F82FA67E32883164279342B1C5 /* UIView+WebCache.m */,
				94A8BBB41D9D4550AE7ED049F1312581 /* UIView+WebCacheOperation.h */,
				63A7FA2BEE803D9B01F922BB4BA6B397 /* UIView+WebCacheOperation.m */,
				956F17F95447DE027B38056025CD7579 /* UIView+WebCacheState.h */,
				BAFB84126C2A92409B3357591908FE47 /* UIView+WebCacheState.m */,
				A6396B691804E3FCE7F1D0DADA83AE31 /* Resources */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		09B83432ACBB7648C9E063C3C88A5001 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				CB8DB140EBDB63B3344658CAC5E042FA /* Pods-Nano_Loan */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		0D908119C7388E327B558D34B20EE2F5 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				70D48B4765C3F81648F303123A6CFF49 /* ResourceBundle-SDWebImage-SDWebImage-Info.plist */,
				80BFBA2426AE7F22BE7D7BA238CD63A1 /* SDWebImage.modulemap */,
				223C9B7435D107017AA4F01B178FC6A6 /* SDWebImage-dummy.m */,
				706FC369D990F5523A89FB1F021642DE /* SDWebImage-Info.plist */,
				AD25DC83A3EBCFB7D606A43458EF66F2 /* SDWebImage-prefix.pch */,
				7C01C6CC53D312AF131AB282EA14947C /* SDWebImage-umbrella.h */,
				70D4DA719BD7B77C17376AF0A5648950 /* SDWebImage.debug.xcconfig */,
				CA413C8E2996E5305CFF922E51E27757 /* SDWebImage.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/SDWebImage";
			sourceTree = "<group>";
		};
		10EE111EBED87615340763285D5831A0 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				9BB4F7879AC7F4550C0C2CDF8AD10959 /* Masonry.modulemap */,
				D29D20F2B4EA95072041BBBE9BDFD161 /* Masonry-dummy.m */,
				2FF5D582ADB501231D1BFAEB7E8AA0FE /* Masonry-Info.plist */,
				027B64F3DA1EFB1393F8E2671667D578 /* Masonry-prefix.pch */,
				827B4AEF873209876171726B873943C3 /* Masonry-umbrella.h */,
				7EE0B708208DA54C7792D0858F97CE53 /* Masonry.debug.xcconfig */,
				1228C9454A54DBE46799F2B283099983 /* Masonry.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/Masonry";
			sourceTree = "<group>";
		};
		270FCD8CFFD80BAABD8A2184D92E0518 /* FBAEMKit */ = {
			isa = PBXGroup;
			children = (
				78EA81DF954747C2318E04A79F42A5D5 /* Frameworks */,
				7145D079DFF454063D93920F52F171C0 /* Support Files */,
			);
			name = FBAEMKit;
			path = FBAEMKit;
			sourceTree = "<group>";
		};
		2E787D6A7AAD63EFD4E1421BCEA7C2CE /* Resources */ = {
			isa = PBXGroup;
			children = (
				D2BFA139BE3968E4C9CE45F9CFFB42F8 /* MJRefresh.bundle */,
				F10494B4D30C762999F0D759F798F5B2 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		2EB53983A0A1F060AE48075ABFED5334 /* Pods */ = {
			isa = PBXGroup;
			children = (
				643904F9066AFED32F006D6557583531 /* AFNetworking */,
				270FCD8CFFD80BAABD8A2184D92E0518 /* FBAEMKit */,
				CA2A7E61F3531B3FEAE59BBE5FCAD802 /* FBSDKCoreKit */,
				C24E1F740D04A24AEBF315CA8C76F193 /* FBSDKCoreKit_Basics */,
				6C20396D96482D9776110CC54C316FE3 /* Masonry */,
				CF10BE13D5D0BB9A7EC7E352B8B19A56 /* MBProgressHUD */,
				72AB1F70FF105E302A13B6744F2807E4 /* MJRefresh */,
				C102EAE1313A5BFD44AFF4970BF04C43 /* SDWebImage */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		3C405BEA34ECC59B9610A65B08D6594B /* UIKit */ = {
			isa = PBXGroup;
			children = (
				24FDB8B29FE3DE461E78F8FB5F2905BD /* AFAutoPurgingImageCache.h */,
				7BC7C2C2B94D1AD15FC72C72DB784368 /* AFAutoPurgingImageCache.m */,
				05814BE6A5C0F238469868A5DEA97CCF /* AFImageDownloader.h */,
				578DD43BD787E90101B75BEF788BA255 /* AFImageDownloader.m */,
				F36091220F6F67E1E1AC4B5A2C36A12E /* AFNetworkActivityIndicatorManager.h */,
				46B98724BC7B3F413DF4AA065977E113 /* AFNetworkActivityIndicatorManager.m */,
				******************************** /* UIActivityIndicatorView+AFNetworking.h */,
				A41F5F108B4DE69CA413C9B0149F76BC /* UIActivityIndicatorView+AFNetworking.m */,
				61E0DDC8E4BB60D1261A51923E08930A /* UIButton+AFNetworking.h */,
				EC81FB402799CE870531BA35336C6B62 /* UIButton+AFNetworking.m */,
				1B80FC420B441C164ED5526EABFB6768 /* UIImageView+AFNetworking.h */,
				D416291597A3B1584F853B95842D122A /* UIImageView+AFNetworking.m */,
				92A4C50F7C210F62C7B0B00E50420B9D /* UIKit+AFNetworking.h */,
				CDF9C8AABF582CFFD2CFDF8FB53E3981 /* UIProgressView+AFNetworking.h */,
				2B3FF7138CEC0FBBE3857CD3ECDB4831 /* UIProgressView+AFNetworking.m */,
				9B70D149FCB49334BC2AD62CB5D1AB5A /* UIRefreshControl+AFNetworking.h */,
				31534ED5D5D783AC06EBB8479FA2A640 /* UIRefreshControl+AFNetworking.m */,
				E2EEB1D439A43D9E505CD6101ED63DBC /* WKWebView+AFNetworking.h */,
				C20507CBFBAC4CD433F3590977845574 /* WKWebView+AFNetworking.m */,
			);
			name = UIKit;
			sourceTree = "<group>";
		};
		46132104B74743DA66F9F1632FDA5507 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				A9E6AD8E16A70E3319E981898B7BA652 /* MJRefresh.modulemap */,
				4E5EF39885B5235C036ACF039D66460F /* MJRefresh-dummy.m */,
				1781C65558CA244C8CC01555D717EE86 /* MJRefresh-Info.plist */,
				D6CCE9C4E8840BD93B5740E8D805624D /* MJRefresh-prefix.pch */,
				E88BBCCAB699750CA8370D016E6AA34D /* MJRefresh-umbrella.h */,
				807409C5D7B136690C2D2F02306755F2 /* MJRefresh.debug.xcconfig */,
				007ADD6FF507592CA39ADDB8D6027C4B /* MJRefresh.release.xcconfig */,
				9A64A52A59C5953A138794E44AD80357 /* ResourceBundle-MJRefresh.Privacy-MJRefresh-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/MJRefresh";
			sourceTree = "<group>";
		};
		493455650400203D8C7203AB68754273 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				69C77890227C35C3CBE090C193908421 /* FBSDKCoreKit_Basics-xcframeworks.sh */,
				EADA52AD6202CAF4A1C888B13C187276 /* FBSDKCoreKit_Basics.debug.xcconfig */,
				6C5DFC15211FCFAE28B79354EE4CEE3F /* FBSDKCoreKit_Basics.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/FBSDKCoreKit_Basics";
			sourceTree = "<group>";
		};
		4B08D2DF79CD4680496B21C5DE312877 /* Reachability */ = {
			isa = PBXGroup;
			children = (
				0C4B6DB752E9541A04467C2B617217C5 /* AFNetworkReachabilityManager.h */,
				943515464B1E8AEB6731548AB14F0F90 /* AFNetworkReachabilityManager.m */,
			);
			name = Reachability;
			sourceTree = "<group>";
		};
		56584B13CF060E3663140E3FC31499DA /* Support Files */ = {
			isa = PBXGroup;
			children = (
				406D93EFB2E434C2D0A7D810CFEBBB90 /* MBProgressHUD.modulemap */,
				0EC3AE8E59C05BEF1EC291160DFE04EA /* MBProgressHUD-dummy.m */,
				9496CC3367BB58851215CE8B727D8DDF /* MBProgressHUD-Info.plist */,
				CD5E1ACF7FF01A08ACC0B1D8D449367A /* MBProgressHUD-prefix.pch */,
				463E1010678231673BB1278F51A1B3A0 /* MBProgressHUD-umbrella.h */,
				105ADDBD5121C299E136A86C6823E6BB /* MBProgressHUD.debug.xcconfig */,
				DAA02086A5396C2AA0002DFA34B0634A /* MBProgressHUD.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/MBProgressHUD";
			sourceTree = "<group>";
		};
		643904F9066AFED32F006D6557583531 /* AFNetworking */ = {
			isa = PBXGroup;
			children = (
				8F3A6BFB7DF2F4D1FCF9A9C094140645 /* AFNetworking.h */,
				9DCF2718AA67035B1213555CF85E9CA1 /* NSURLSession */,
				4B08D2DF79CD4680496B21C5DE312877 /* Reachability */,
				6807B0641D6C8C2CC3746FAB60576275 /* Security */,
				C7AC1754179F2E9A816E9A818324B58F /* Serialization */,
				D87F7B5CDE70BE09BE53D3BD8103232C /* Support Files */,
				3C405BEA34ECC59B9610A65B08D6594B /* UIKit */,
			);
			name = AFNetworking;
			path = AFNetworking;
			sourceTree = "<group>";
		};
		6807B0641D6C8C2CC3746FAB60576275 /* Security */ = {
			isa = PBXGroup;
			children = (
				46AB11EE9F25F2B4A06C60EB74D589C0 /* AFSecurityPolicy.h */,
				94261EA85639B90596EA529A46C865B4 /* AFSecurityPolicy.m */,
			);
			name = Security;
			sourceTree = "<group>";
		};
		6C20396D96482D9776110CC54C316FE3 /* Masonry */ = {
			isa = PBXGroup;
			children = (
				5E979675DF7D8C7C19A7C033EAFD8D0A /* MASCompositeConstraint.h */,
				F31EA86CBB965ECA0C1C299704FBBB5A /* MASCompositeConstraint.m */,
				229416BAB314DD8212DA96597D6D6620 /* MASConstraint.h */,
				75A7F78D8D7CF1E9C7E6FBEA0EC09C95 /* MASConstraint.m */,
				C5B8C8565AFDF6696C00088B4CB745E2 /* MASConstraint+Private.h */,
				7CC519CE74C273E3B96F42EFFE0C8A29 /* MASConstraintMaker.h */,
				090F99E04964E3730A77393230B37658 /* MASConstraintMaker.m */,
				ED7DC2C78A9694F09928BCCD132D0888 /* MASLayoutConstraint.h */,
				3C70894986E131C08150C40ED432DEAD /* MASLayoutConstraint.m */,
				50B5002BAAFA612BE71E41791FC7DA5D /* Masonry.h */,
				475C74E72914FC29457D226142280BAB /* MASUtilities.h */,
				EAF2695AE98027DE219427774EA00F4C /* MASViewAttribute.h */,
				DEFAD2C68AFA34BC1B2B97D756B6D000 /* MASViewAttribute.m */,
				415AE4EBD078041AB7A08362BF1EC94C /* MASViewConstraint.h */,
				D6031285ADB535394FE605C0C753CF45 /* MASViewConstraint.m */,
				7BC9D7B5E24A570D4B15C828C283C110 /* NSArray+MASAdditions.h */,
				376187B2E18A36D1C1CECAC6856BA657 /* NSArray+MASAdditions.m */,
				FF97549E8708AB5DB55C6D68DA24E7E6 /* NSArray+MASShorthandAdditions.h */,
				FDBA4E418E7FE6B322DCD03CB1007ECB /* NSLayoutConstraint+MASDebugAdditions.h */,
				5AFBA17DDB5A92C6CBE588966C50DA5A /* NSLayoutConstraint+MASDebugAdditions.m */,
				C06F04758C4A9410D32E65A09EC66D97 /* View+MASAdditions.h */,
				F7FE5D93FEFB9568DE757E468C8E3EF4 /* View+MASAdditions.m */,
				1B686371EF9EBDD85A19E25539F568BA /* View+MASShorthandAdditions.h */,
				1555F5583D3F4900FE77515D7DEF159A /* ViewController+MASAdditions.h */,
				6BB5FD74D69F8D9F92FD56A887310AEF /* ViewController+MASAdditions.m */,
				10EE111EBED87615340763285D5831A0 /* Support Files */,
			);
			name = Masonry;
			path = Masonry;
			sourceTree = "<group>";
		};
		7145D079DFF454063D93920F52F171C0 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				79B51AC8232F3628B5CB3E6486BD64C5 /* FBAEMKit-xcframeworks.sh */,
				DFBCAAB37C2CEBD6FDCABDF38D0C83E7 /* FBAEMKit.debug.xcconfig */,
				E37230CAD386F393FE18754CFB5B04C7 /* FBAEMKit.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/FBAEMKit";
			sourceTree = "<group>";
		};
		72AB1F70FF105E302A13B6744F2807E4 /* MJRefresh */ = {
			isa = PBXGroup;
			children = (
				0EE55DD0D556C460D50C97617AEF9F18 /* MJRefresh.h */,
				5E7D3F2658C5FF9BC63C19C146238EF0 /* MJRefreshAutoFooter.h */,
				97BB8B85E2684936285EDA6DE35E2037 /* MJRefreshAutoFooter.m */,
				D252DF203C2311D237C3C3E611BFCBEF /* MJRefreshAutoGifFooter.h */,
				2D1C98D35AAE74C3EF13B6F460EEDFA3 /* MJRefreshAutoGifFooter.m */,
				5BAF6786F6992480D509FB4DD1205969 /* MJRefreshAutoNormalFooter.h */,
				DC0F93774456DC84A27D91E611DBD126 /* MJRefreshAutoNormalFooter.m */,
				9BF0B319ECC588E630BB061703763EFE /* MJRefreshAutoStateFooter.h */,
				57A95C170B0A7F681E33FAE6FA961820 /* MJRefreshAutoStateFooter.m */,
				92E1B71DDBEC7BB36DCE14247966D279 /* MJRefreshBackFooter.h */,
				5CDE70A924EAFD031E709AA8BF825A1F /* MJRefreshBackFooter.m */,
				88123D6FAA396D9AF75F25750F8D3EC1 /* MJRefreshBackGifFooter.h */,
				A4F0B132AAD230F5797450132A241F48 /* MJRefreshBackGifFooter.m */,
				7232FDE2F429C285FBF950DD8CABC55F /* MJRefreshBackNormalFooter.h */,
				559D20A4B808010925C4AEEF42CBC9F8 /* MJRefreshBackNormalFooter.m */,
				EF3F7035FE6372BC0DAD6729D5E56FDF /* MJRefreshBackStateFooter.h */,
				8B6C1075FC4103587CE36B00E10346A6 /* MJRefreshBackStateFooter.m */,
				FCE3EBF3E49A379F42253639B3871893 /* MJRefreshComponent.h */,
				D71FE77CEB4D8192D475D25D5199DEDF /* MJRefreshComponent.m */,
				5AB02EF7397807AB656C9DD19DA7AD29 /* MJRefreshConfig.h */,
				C940CCDF172766EA9C6444C47C443E02 /* MJRefreshConfig.m */,
				BF77135EF3E2C3897AD8A453DDCD2BAB /* MJRefreshConst.h */,
				55306CC7388014041F4BB9FB1F889CC2 /* MJRefreshConst.m */,
				9790197BECAE968B2658D60B705AFC08 /* MJRefreshFooter.h */,
				ABF6BF2E51E9B8A6C13440ABFBF05186 /* MJRefreshFooter.m */,
				E2EDFEF94C31FF176835B73A1AEA3F1D /* MJRefreshGifHeader.h */,
				9523759946AEA807A7FC3992EEE6A188 /* MJRefreshGifHeader.m */,
				F3200DCC36729B205E9E18CAF440B2A9 /* MJRefreshHeader.h */,
				B43D44D3BBECD4BA8EB63397DCDAF2B0 /* MJRefreshHeader.m */,
				3EB099BAF1D47C58C076552D6AB59EAE /* MJRefreshNormalHeader.h */,
				F4C3BD86F9B8643726AB3BE8CF5D1CA7 /* MJRefreshNormalHeader.m */,
				03D0F1A648550047C82BAC391D8F3CCB /* MJRefreshNormalTrailer.h */,
				C0E585FAC02532C13FF9B3C1663456DC /* MJRefreshNormalTrailer.m */,
				EF6DD8858AE767C8C8BB967DC9C7303B /* MJRefreshStateHeader.h */,
				8687FBF3DABDC6266C65F5DBF9DA9F19 /* MJRefreshStateHeader.m */,
				AC7B0BA8555EF48ED3BE0E8F436BE98D /* MJRefreshStateTrailer.h */,
				A633B1FF01FBEA9F83E6016A9B19C0FB /* MJRefreshStateTrailer.m */,
				6155E668105C1B6E682E91C5E4BA598B /* MJRefreshTrailer.h */,
				89BC8B491110B74DD22065F31A45E4FA /* MJRefreshTrailer.m */,
				5AB8C89747B63EC60AC17506677B66DC /* NSBundle+MJRefresh.h */,
				0B7C6A23D76E609B238E5B495F86DA9F /* NSBundle+MJRefresh.m */,
				FCAC3C7409CC871BC8709618A577BE33 /* UICollectionViewLayout+MJRefresh.h */,
				C5D8C13D979E5F303CCEDE97C2D9A8C1 /* UICollectionViewLayout+MJRefresh.m */,
				25839EACDB320C59013B336E48C9C385 /* UIScrollView+MJExtension.h */,
				7096A2E55F0A442D8366AD0ABDE705E3 /* UIScrollView+MJExtension.m */,
				A71EAA491D66A6EEFC169902BD0F4070 /* UIScrollView+MJRefresh.h */,
				B0B6059943320F774EC2A322398B3F12 /* UIScrollView+MJRefresh.m */,
				554EA878CA6FFC285AB0718A975AFD1B /* UIView+MJExtension.h */,
				3F7DDBBBA1F1DE3A57E40BB04EA2E4FD /* UIView+MJExtension.m */,
				2E787D6A7AAD63EFD4E1421BCEA7C2CE /* Resources */,
				46132104B74743DA66F9F1632FDA5507 /* Support Files */,
			);
			name = MJRefresh;
			path = MJRefresh;
			sourceTree = "<group>";
		};
		78EA81DF954747C2318E04A79F42A5D5 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				CA1CAAE1C179643E221552C3E0C1295B /* FBAEMKit.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		7BE35CB5C95C02C99C6D1841901C2411 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				D072783E4919417ACCE7F10E11C833A8 /* FBSDKCoreKit-xcframeworks.sh */,
				FC09B20DF07A51D85D8FC96DC354D860 /* FBSDKCoreKit.debug.xcconfig */,
				17E35AFC29F73D366F8DC32D199561B6 /* FBSDKCoreKit.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/FBSDKCoreKit";
			sourceTree = "<group>";
		};
		90E39DD8BB6B1A039452A18109C88D0B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				4B5F5635CE0A988D76396CF3ECD0F1E0 /* FBSDKCoreKit_Basics.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		9DCF2718AA67035B1213555CF85E9CA1 /* NSURLSession */ = {
			isa = PBXGroup;
			children = (
				DC6809D81689953D9097375854A01017 /* AFCompatibilityMacros.h */,
				586C7D767724357618422B52C865DA28 /* AFHTTPSessionManager.h */,
				74D75DCA93CEAA4E2B3B3B9C44385947 /* AFHTTPSessionManager.m */,
				D41021804D6338C082E57D172E3A8035 /* AFURLSessionManager.h */,
				10BE79645E38972233986B0051941E7D /* AFURLSessionManager.m */,
			);
			name = NSURLSession;
			sourceTree = "<group>";
		};
		A6396B691804E3FCE7F1D0DADA83AE31 /* Resources */ = {
			isa = PBXGroup;
			children = (
				714D2BDD00D34C29FFD5B0A3D547DBD6 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		ADD972DF66319CA1C506590C44010D1F /* Products */ = {
			isa = PBXGroup;
			children = (
				A4FA15D44DF6BAC7550EDEED10862AA3 /* AFNetworking */,
				1FFED36A657123030ABB700256D73F15 /* Masonry */,
				8B8FAB0D627B17EDE1366984278705D9 /* MBProgressHUD */,
				E49D6D248DD1CEE584E6776B9164A1B2 /* MJRefresh */,
				7E3097CFEFDA621E9FB0E62009FF87FC /* MJRefresh-MJRefresh.Privacy */,
				71570934EAD5CD99703EEEE8805DBDF6 /* Pods-Nano_Loan */,
				B0B214D775196BA7CA8E17E53048A493 /* SDWebImage */,
				CF1281E58AA1045D4B7F33FC56691C42 /* SDWebImage-SDWebImage */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B94D7768568A9992200DB461E8CF687F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				C0B9C11402F190DE3661C661E327EFFF /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		C0B9C11402F190DE3661C661E327EFFF /* iOS */ = {
			isa = PBXGroup;
			children = (
				F2CA872A5138F1B8EF538FC1C4109152 /* CoreGraphics.framework */,
				********18AC31E3A631F72434F9EAC1 /* Foundation.framework */,
				DDE8BFE78F9CD7EE55A53388621C5B35 /* ImageIO.framework */,
				84D74C5AA71CCA8E912BDC9060B36F2D /* QuartzCore.framework */,
				B5D42D5E2CF5F6BD84D4A821D89CC11D /* UIKit.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		C102EAE1313A5BFD44AFF4970BF04C43 /* SDWebImage */ = {
			isa = PBXGroup;
			children = (
				0914259B5AE9890B4C9D31575E6DBD7C /* Core */,
				0D908119C7388E327B558D34B20EE2F5 /* Support Files */,
			);
			name = SDWebImage;
			path = SDWebImage;
			sourceTree = "<group>";
		};
		C24E1F740D04A24AEBF315CA8C76F193 /* FBSDKCoreKit_Basics */ = {
			isa = PBXGroup;
			children = (
				90E39DD8BB6B1A039452A18109C88D0B /* Frameworks */,
				493455650400203D8C7203AB68754273 /* Support Files */,
			);
			name = FBSDKCoreKit_Basics;
			path = FBSDKCoreKit_Basics;
			sourceTree = "<group>";
		};
		C7AC1754179F2E9A816E9A818324B58F /* Serialization */ = {
			isa = PBXGroup;
			children = (
				524B06850BC1DB525CF4376D77312BE6 /* AFURLRequestSerialization.h */,
				3B165C9C87A05B5D2B56C064216B3F53 /* AFURLRequestSerialization.m */,
				F9EC47F507A90874FBE790D58D846835 /* AFURLResponseSerialization.h */,
				55F43F7B1F5F3CEA54FB3C532B7C1EFB /* AFURLResponseSerialization.m */,
			);
			name = Serialization;
			sourceTree = "<group>";
		};
		CA2A7E61F3531B3FEAE59BBE5FCAD802 /* FBSDKCoreKit */ = {
			isa = PBXGroup;
			children = (
				025B553D18DEFB6CA42CB1E612378177 /* Frameworks */,
				7BE35CB5C95C02C99C6D1841901C2411 /* Support Files */,
			);
			name = FBSDKCoreKit;
			path = FBSDKCoreKit;
			sourceTree = "<group>";
		};
		CB8DB140EBDB63B3344658CAC5E042FA /* Pods-Nano_Loan */ = {
			isa = PBXGroup;
			children = (
				9CEEC64A52F086D3BD98E7E5176352A1 /* Pods-Nano_Loan.modulemap */,
				C19E5924F9CA03210415C0A516139AEA /* Pods-Nano_Loan-acknowledgements.markdown */,
				B0685ACA61B3EA89BF7D3B2E043062DD /* Pods-Nano_Loan-acknowledgements.plist */,
				736D36DEEA2F92CE85F489EBEAB55B3A /* Pods-Nano_Loan-dummy.m */,
				257FECACB47F5E238F43124258E43313 /* Pods-Nano_Loan-frameworks.sh */,
				C757B72EF0D6C73CA87D858AFC5A3323 /* Pods-Nano_Loan-Info.plist */,
				17CF85A57080E64F02970A5FA4D3A2C2 /* Pods-Nano_Loan-umbrella.h */,
				65D65A306338246A5219CC28799C7C62 /* Pods-Nano_Loan.debug.xcconfig */,
				5A94511790704271ECE7B1239B313843 /* Pods-Nano_Loan.release.xcconfig */,
			);
			name = "Pods-Nano_Loan";
			path = "Target Support Files/Pods-Nano_Loan";
			sourceTree = "<group>";
		};
		CF10BE13D5D0BB9A7EC7E352B8B19A56 /* MBProgressHUD */ = {
			isa = PBXGroup;
			children = (
				0109270CA3F4007C62D52BEB2C2AA82D /* MBProgressHUD.h */,
				349A8C7FE5264237269403A5B812657A /* MBProgressHUD.m */,
				56584B13CF060E3663140E3FC31499DA /* Support Files */,
			);
			name = MBProgressHUD;
			path = MBProgressHUD;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				B94D7768568A9992200DB461E8CF687F /* Frameworks */,
				2EB53983A0A1F060AE48075ABFED5334 /* Pods */,
				ADD972DF66319CA1C506590C44010D1F /* Products */,
				09B83432ACBB7648C9E063C3C88A5001 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D87F7B5CDE70BE09BE53D3BD8103232C /* Support Files */ = {
			isa = PBXGroup;
			children = (
				533B13EF4F7BE90AC47FC7AF4F7CD7B5 /* AFNetworking.modulemap */,
				E5C50857692780902AB615556CCBEADF /* AFNetworking-dummy.m */,
				47DBE4444D8F2A5F189BE961DDFCFCB1 /* AFNetworking-Info.plist */,
				0CF2537BD561882CC27737B366DE2B42 /* AFNetworking-prefix.pch */,
				999AD72F3AB19F6B2C208A4F0F891C4D /* AFNetworking-umbrella.h */,
				5216AD9C10B699BF9FE794C8C04B7506 /* AFNetworking.debug.xcconfig */,
				B8E113D5B2CC5F4AFBB17F4B14FB0553 /* AFNetworking.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/AFNetworking";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		0EF2526B5CC15C0E915F32190565F64C /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BA904ABA8ED36CC4E5EB2B2004CA1F18 /* MASCompositeConstraint.h in Headers */,
				37B890ABDC7DD441E6AA662325D412E6 /* MASConstraint.h in Headers */,
				7C5505A2D3F2A697A5F324787061F4B7 /* MASConstraint+Private.h in Headers */,
				813BE4C96A6D39C13EC50C6CD164F0AF /* MASConstraintMaker.h in Headers */,
				B680C2604BD8BC9644AE7C67BC46B9BB /* MASLayoutConstraint.h in Headers */,
				EC9B34262AED632D7EFB49804337648E /* Masonry.h in Headers */,
				B59E60FBC9665FC1061B88B8E6FD9FAF /* Masonry-umbrella.h in Headers */,
				C2068AEACC2D9C7F1FFE41AA25B12A68 /* MASUtilities.h in Headers */,
				05E2B7C1DB7528A0BBEA1521BE0DBAF1 /* MASViewAttribute.h in Headers */,
				5F45735DF355530CC955066D3C007E19 /* MASViewConstraint.h in Headers */,
				BF22D137EF6324675FA50080C5D93C00 /* NSArray+MASAdditions.h in Headers */,
				61507E402F1F7C58BF119995A0479A22 /* NSArray+MASShorthandAdditions.h in Headers */,
				DBA9500CBBA5FF6FCBBA115AE4D12152 /* NSLayoutConstraint+MASDebugAdditions.h in Headers */,
				AE7B02645B8F769CA5F215EE8F7CC5B0 /* View+MASAdditions.h in Headers */,
				772CF8E9CD02ECA4275B6173E2110E80 /* View+MASShorthandAdditions.h in Headers */,
				8C6C7E25C5A24C936F81823978190E96 /* ViewController+MASAdditions.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		22C4F6C2D1258108CF5B6E74F03D0EB2 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7989A6E79BFA78440C39F568D972305C /* MJRefresh.h in Headers */,
				325CA20B9271F3E008234E1518B79061 /* MJRefresh-umbrella.h in Headers */,
				561420A20DC0A84258A902E9EB69A15A /* MJRefreshAutoFooter.h in Headers */,
				EC8E84A8FFADDCA562A8608D141D9027 /* MJRefreshAutoGifFooter.h in Headers */,
				7902D28FC9EF5AFEB452F508C7F266B1 /* MJRefreshAutoNormalFooter.h in Headers */,
				5BB6B99986FD7111B3AEBE931C7F507B /* MJRefreshAutoStateFooter.h in Headers */,
				45E1583D7EF53489B82C4CA2AD1AD0CF /* MJRefreshBackFooter.h in Headers */,
				475B4F3E71C293065AAFDB1888696CF6 /* MJRefreshBackGifFooter.h in Headers */,
				9A7FB1E975A5955C896E6B195C521804 /* MJRefreshBackNormalFooter.h in Headers */,
				08719ABCE689ED74FE7486B1E49DAA6C /* MJRefreshBackStateFooter.h in Headers */,
				69345CBCB31076EBF8A2C5885AF973AB /* MJRefreshComponent.h in Headers */,
				4DCA75BFE1558CE59DFC56607E49B3D2 /* MJRefreshConfig.h in Headers */,
				28BA9702905AA2B4C1E9E4878032D4E4 /* MJRefreshConst.h in Headers */,
				D90DED0F5638B1C44F4B6C62D600D240 /* MJRefreshFooter.h in Headers */,
				61461B0D9D7B81C3F8D24066D9A19DCE /* MJRefreshGifHeader.h in Headers */,
				442F468E261A1106C291BF52BDBF9DB7 /* MJRefreshHeader.h in Headers */,
				EE6E8FE636D2C02E3D2FC1E8555B4612 /* MJRefreshNormalHeader.h in Headers */,
				BC2F9B1D6986FEB23B4FB1288B512538 /* MJRefreshNormalTrailer.h in Headers */,
				5DFCBADAC7D0FAC82C84A6C8E7BF1DA6 /* MJRefreshStateHeader.h in Headers */,
				F60F90EAF35CFF40DF1C33557965787D /* MJRefreshStateTrailer.h in Headers */,
				523235228A1C021C67F2E3776A922DC5 /* MJRefreshTrailer.h in Headers */,
				81A5635CEA2AD9623E30CAE9AFC3BF65 /* NSBundle+MJRefresh.h in Headers */,
				5163FC6D715F6881B1FA1AB13DCEF870 /* UICollectionViewLayout+MJRefresh.h in Headers */,
				22516EA77E7120000632C30BD9A03927 /* UIScrollView+MJExtension.h in Headers */,
				69E353C99C6EEA3C93CCF2E526460B9D /* UIScrollView+MJRefresh.h in Headers */,
				3A2FCB914F6EADED828FF05F7E9132AE /* UIView+MJExtension.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		33783D69751B087D045FCF1FCA02E724 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9B9343E8599EE5196BA75E842DCB48B7 /* NSBezierPath+SDRoundedCorners.h in Headers */,
				A839428F403C52D8AA3466B65E20C27A /* NSButton+WebCache.h in Headers */,
				8AF38EDB1E9BF0D334AEB23C488870B8 /* NSData+ImageContentType.h in Headers */,
				34B28D4F0168194B6EFAC0520EB7A7F4 /* NSImage+Compatibility.h in Headers */,
				9B3420DEB8A0CCB9E1241A669AEFCA8E /* SDAnimatedImage.h in Headers */,
				BDBE494BAC544843982C3CA96A6C41DD /* SDAnimatedImagePlayer.h in Headers */,
				DEA09692CF813A23899CD4949A9B6801 /* SDAnimatedImageRep.h in Headers */,
				C1DD8C6A64F948E4C53560C76B995DA4 /* SDAnimatedImageView.h in Headers */,
				D7B3E8948DB04BD8FB6748419DA03EA9 /* SDAnimatedImageView+WebCache.h in Headers */,
				5C8279C226EB028B044C5A0F4AC5A91A /* SDAssociatedObject.h in Headers */,
				20D618EF3EA5E3BE96DA24D36E3CA9EF /* SDAsyncBlockOperation.h in Headers */,
				ED8F64FF98CFAE0B12CF60A1B0E6BAF8 /* SDCallbackQueue.h in Headers */,
				042D40751BD2F51FBE9FECD4707CBBE9 /* SDDeviceHelper.h in Headers */,
				928371B066E1211CE87089668D5BCB4C /* SDDiskCache.h in Headers */,
				7074EA7FCC90B4967A437F5C43496828 /* SDDisplayLink.h in Headers */,
				0453019EC6578A67B82CF569EC765546 /* SDFileAttributeHelper.h in Headers */,
				5E10328A83E05D0015D7459FAAEF121D /* SDGraphicsImageRenderer.h in Headers */,
				165F1C9CBD621828C788A3018D0426C5 /* SDImageAPNGCoder.h in Headers */,
				4B2C2AE16AE3DDA7417AFCF7952588F1 /* SDImageAssetManager.h in Headers */,
				B4F231C5CBAB3D4A184699D0066E0E83 /* SDImageAWebPCoder.h in Headers */,
				D662C83ECE8BEDA5FFB52F3575CA3E1A /* SDImageCache.h in Headers */,
				14CA284AC4FF1EED75E785641EE98034 /* SDImageCacheConfig.h in Headers */,
				ABCB80C4813C849FC93D57676820C907 /* SDImageCacheDefine.h in Headers */,
				F53BE4449AE5896F76325E4DCB6D0B13 /* SDImageCachesManager.h in Headers */,
				1C8B70C74291A3076746C3B18781568E /* SDImageCachesManagerOperation.h in Headers */,
				09A2ACBC8CE1761652EAA20886AEFE10 /* SDImageCoder.h in Headers */,
				F68889CD481716EE5D6B75EBD8FD53A6 /* SDImageCoderHelper.h in Headers */,
				B741DBE2A466E6211F879EF997D9322D /* SDImageCodersManager.h in Headers */,
				6B5C3592B5E911E833D067D0BC785B1A /* SDImageFrame.h in Headers */,
				44CD842019B1CEA681F820F37A30B7C4 /* SDImageFramePool.h in Headers */,
				1B6CE67196EE181E6B56788EFC7E00D3 /* SDImageGIFCoder.h in Headers */,
				B331CE2D3DEB461E738B886086A365F9 /* SDImageGraphics.h in Headers */,
				E76969F9B01139118427505B18F9CD21 /* SDImageHEICCoder.h in Headers */,
				089F3C4BAA46A37EC5763DD312771021 /* SDImageIOAnimatedCoder.h in Headers */,
				676775CB29378BB6CA3CA5992E9C6A99 /* SDImageIOAnimatedCoderInternal.h in Headers */,
				D2CD8848F856EC9942A76610AAE66F0A /* SDImageIOCoder.h in Headers */,
				C6A100159974349FEAAC99B82BE0F872 /* SDImageLoader.h in Headers */,
				10017B43AC38C3A89D7AC1376C6E7066 /* SDImageLoadersManager.h in Headers */,
				EF6A6C725598F572A70C5FCEE328C184 /* SDImageTransformer.h in Headers */,
				2DDD48230ED9E8068C7E439D79B99A8E /* SDInternalMacros.h in Headers */,
				88473AE7C22F952DACB39FA0758D1624 /* SDMemoryCache.h in Headers */,
				3A1AD84C0DC3C256418CC46739024E96 /* SDmetamacros.h in Headers */,
				58F7CE37BB4CB3BE806B68A502E6E1A7 /* SDWeakProxy.h in Headers */,
				711D32EF4A9901567A488291603BF906 /* SDWebImage.h in Headers */,
				D62A672EEB252581BD972DDA862BE1DD /* SDWebImage-umbrella.h in Headers */,
				53433003112C4FE271EC985803862B61 /* SDWebImageCacheKeyFilter.h in Headers */,
				3C8F2F868D0C361CAF43E53CDB8EB631 /* SDWebImageCacheSerializer.h in Headers */,
				4688743B7B845309486559EB7BD5D147 /* SDWebImageCompat.h in Headers */,
				EA82B6D97C9C5D0558047AF552D63203 /* SDWebImageDefine.h in Headers */,
				29F7F0E98FD26A96364DBACD7D5F237A /* SDWebImageDownloader.h in Headers */,
				854807558DCB972EDDFC1D00032BA6E4 /* SDWebImageDownloaderConfig.h in Headers */,
				91E8B94F8E02ABF5197DF5AE7D0B3934 /* SDWebImageDownloaderDecryptor.h in Headers */,
				B66356D4E7E43B3D15324569AA7EBB05 /* SDWebImageDownloaderOperation.h in Headers */,
				BCEFDE57BB0E0B36731C8D39FFA1BE2C /* SDWebImageDownloaderRequestModifier.h in Headers */,
				18AD90784D549657DF51BC8377DA3085 /* SDWebImageDownloaderResponseModifier.h in Headers */,
				7C45DBA62EE045C4922404182F6393B8 /* SDWebImageError.h in Headers */,
				F49CB22863CCFEC7817D259F27F91C57 /* SDWebImageIndicator.h in Headers */,
				A9A49E4A3BE8882F60DF32BAF39DE191 /* SDWebImageManager.h in Headers */,
				9DF446F8CA5BC4D4098766EC9063012C /* SDWebImageOperation.h in Headers */,
				******************************** /* SDWebImageOptionsProcessor.h in Headers */,
				AC14E56ECA7A4980A8E1CA68E800B12C /* SDWebImagePrefetcher.h in Headers */,
				6E66305665DBCFBCF5B2480BF705D500 /* SDWebImageTransition.h in Headers */,
				91AAF555B286FBF53E4F98D092B406BD /* SDWebImageTransitionInternal.h in Headers */,
				BADA31750A2136D073EDA4461DBE1EEA /* UIButton+WebCache.h in Headers */,
				71BEB1D9532900291A5A24B1C038516F /* UIColor+SDHexString.h in Headers */,
				1830558A4D2D63C8E76BC3136D8213F9 /* UIImage+ExtendedCacheData.h in Headers */,
				75771A97B77FA30A0175A81B480F80EF /* UIImage+ForceDecode.h in Headers */,
				A1560247914C760D9EE5F7A2392CC06C /* UIImage+GIF.h in Headers */,
				4ED05DB3E43FF6AE1FA22130B2B50F05 /* UIImage+MemoryCacheCost.h in Headers */,
				5DCBA14510E091D6A1CE499B08B794B5 /* UIImage+Metadata.h in Headers */,
				3C7EAECB8C573E714C818BA04EB33773 /* UIImage+MultiFormat.h in Headers */,
				8D8AD606ECD8E1F247965CD43956D412 /* UIImage+Transform.h in Headers */,
				6A19379E3B0370EDA447743C9B1A1379 /* UIImageView+HighlightedWebCache.h in Headers */,
				32ACEDCEBE0507A82D6323114A1C74F1 /* UIImageView+WebCache.h in Headers */,
				36F4B09E7C71DCC5CEC6057814033C37 /* UIView+WebCache.h in Headers */,
				B5AF87C11A465F666473F6191D173905 /* UIView+WebCacheOperation.h in Headers */,
				69AB6A513D5F36D7360FEF4FDA1D60D0 /* UIView+WebCacheState.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5D73FBBE3085418AA04A3B4B8D080B8 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F2C9F9038FBEFD951516694AE873A2B9 /* MBProgressHUD.h in Headers */,
				C993A4D0CFA51E567F458CA65C1298AA /* MBProgressHUD-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C6390AB04A018D57637AAB0718C31A83 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B030B558BE97E0225652EFB8C8FA431F /* AFAutoPurgingImageCache.h in Headers */,
				9D422527A25BAE6A207DEFE11958ABBC /* AFCompatibilityMacros.h in Headers */,
				506FC58999564A737C745F2590E9B4D5 /* AFHTTPSessionManager.h in Headers */,
				FDACBA49610EA6F39CABB7FE44B137D1 /* AFImageDownloader.h in Headers */,
				7FF8A56511E71D6FEC966BF9FEE135B5 /* AFNetworkActivityIndicatorManager.h in Headers */,
				E1BF615DD0422B06C97542F03C879D41 /* AFNetworking.h in Headers */,
				BC5458210A973BC7A29D1F45D458A14B /* AFNetworking-umbrella.h in Headers */,
				F1D845E22D5B8FC6AFC3C2E41DA1B6DF /* AFNetworkReachabilityManager.h in Headers */,
				DBD9152526A180771BF7D7CD209B957E /* AFSecurityPolicy.h in Headers */,
				724991CA89C46BAFBC08264D94D86484 /* AFURLRequestSerialization.h in Headers */,
				F2AD91050B1FE3C8BC78567F1FDE3ED5 /* AFURLResponseSerialization.h in Headers */,
				3B8EDFF69A68ABD3735E0C6931CA5C95 /* AFURLSessionManager.h in Headers */,
				860CB3A5D2E13B946CD2EFB7F749C4CF /* UIActivityIndicatorView+AFNetworking.h in Headers */,
				E3FC6BEE41652C0500F57E0CB83B347F /* UIButton+AFNetworking.h in Headers */,
				EB3DF628891F7D6AB114718AF760CB2A /* UIImageView+AFNetworking.h in Headers */,
				DDA16FB9C21AD941442357DAE6939530 /* UIKit+AFNetworking.h in Headers */,
				5AF22814CD055B553AD9D78BE54B94E1 /* UIProgressView+AFNetworking.h in Headers */,
				7F886FC2763F0BF1625A24EE4F94C04D /* UIRefreshControl+AFNetworking.h in Headers */,
				C0D7926E41A294ACA98D7B033B283919 /* WKWebView+AFNetworking.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F45C02808FE0B2DAC595F9071ACC137E /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				740AB16421665B56B23671CEEC2C504D /* Pods-Nano_Loan-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		0130B3724283586C0E9D2A112D4F2AA1 /* AFNetworking */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7CEB2E47380AD51987AA02ECD4DFBCD9 /* Build configuration list for PBXNativeTarget "AFNetworking" */;
			buildPhases = (
				C6390AB04A018D57637AAB0718C31A83 /* Headers */,
				8C85583CECA68B164DC51110AF13E6FB /* Sources */,
				37145BAEB1B97BA7ADD7D6C3E86E99BD /* Frameworks */,
				9BB224D4E89ABC2539ABBEBDC9696C8F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AFNetworking;
			productName = AFNetworking;
			productReference = A4FA15D44DF6BAC7550EDEED10862AA3 /* AFNetworking */;
			productType = "com.apple.product-type.framework";
		};
		3847153A6E5EEFB86565BA840768F429 /* SDWebImage */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2F9E94B5F79E365095CB33D3D3FCA6A2 /* Build configuration list for PBXNativeTarget "SDWebImage" */;
			buildPhases = (
				33783D69751B087D045FCF1FCA02E724 /* Headers */,
				4E2C65E105E47F0D32B2C6ADA5FB58B1 /* Sources */,
				3A5330E1BD187252F408EBB46F1BDC42 /* Frameworks */,
				44B3C0D7DDF289331B7732E9D87126DB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7F5925D5E83788BD35012AFA3ECDD5A3 /* PBXTargetDependency */,
			);
			name = SDWebImage;
			productName = SDWebImage;
			productReference = B0B214D775196BA7CA8E17E53048A493 /* SDWebImage */;
			productType = "com.apple.product-type.framework";
		};
		55AF53E6C77A10ED4985E04D74A8878E /* Masonry */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AAA1F8799DB68036C3BE983C05FAA2C7 /* Build configuration list for PBXNativeTarget "Masonry" */;
			buildPhases = (
				0EF2526B5CC15C0E915F32190565F64C /* Headers */,
				8AB80AB52448851AE6F023FEAB0D3F6F /* Sources */,
				12A799DC8ABB2C283ADDDED4421A5EAB /* Frameworks */,
				ECD6B9A8E754DF142B323DF2D7E0D112 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Masonry;
			productName = Masonry;
			productReference = 1FFED36A657123030ABB700256D73F15 /* Masonry */;
			productType = "com.apple.product-type.framework";
		};
		6868056D761E163D10FDAF8CF1C4D9B8 /* MJRefresh */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 29BB59B7B51BC6194771995E3356CF70 /* Build configuration list for PBXNativeTarget "MJRefresh" */;
			buildPhases = (
				22C4F6C2D1258108CF5B6E74F03D0EB2 /* Headers */,
				CEFCB92DFC6C522681EC56915AD01B4E /* Sources */,
				11690A588400BBB164423D5F86311C35 /* Frameworks */,
				4A4F8947EF95B9D0D1FCFC1296740510 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				75A4016AE104038A382C1C5906746BB9 /* PBXTargetDependency */,
			);
			name = MJRefresh;
			productName = MJRefresh;
			productReference = E49D6D248DD1CEE584E6776B9164A1B2 /* MJRefresh */;
			productType = "com.apple.product-type.framework";
		};
		7BD8D07034708B2D327AE57BE458F3C3 /* Pods-Nano_Loan */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8F464E8149CF17E45CDE093595D7DBAA /* Build configuration list for PBXNativeTarget "Pods-Nano_Loan" */;
			buildPhases = (
				F45C02808FE0B2DAC595F9071ACC137E /* Headers */,
				C2BF64411FCD7584B30AEC11533888AD /* Sources */,
				771C25D2FDF8CD53A4785F702BAF1F1C /* Frameworks */,
				0C57E0092EF2CF6E69D51C0D2E9806BB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				CF49881715FE80D8F9455937AD4F64B6 /* PBXTargetDependency */,
				C8CABA02A786DA48E9DB3B541316E7D8 /* PBXTargetDependency */,
				7471E2AB5C70DB7B20295662E411F8AB /* PBXTargetDependency */,
				6335ECB71FBAC20B99F368AB90EACAB6 /* PBXTargetDependency */,
				607161BDBD3B91DCC22F7BCAF26467E5 /* PBXTargetDependency */,
				ECD0BB27E541521EDF25B18E9A76EDA7 /* PBXTargetDependency */,
				BBE609435CC0CBE9C87842DB4A3723F8 /* PBXTargetDependency */,
				F4C226DB69A3A39AD3A8C8C2AAC8559F /* PBXTargetDependency */,
			);
			name = "Pods-Nano_Loan";
			productName = Pods_Nano_Loan;
			productReference = 71570934EAD5CD99703EEEE8805DBDF6 /* Pods-Nano_Loan */;
			productType = "com.apple.product-type.framework";
		};
		82B0A41D3031FF27D78E17B0A9A46FB0 /* MBProgressHUD */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 945C0F2B41CBADE68A142120AE9A4AF3 /* Build configuration list for PBXNativeTarget "MBProgressHUD" */;
			buildPhases = (
				B5D73FBBE3085418AA04A3B4B8D080B8 /* Headers */,
				07FE0EDF7C4BD3D19B3E78A7915BB0E9 /* Sources */,
				CA6527CF424B1AA12AF1D0BB36EBAF73 /* Frameworks */,
				E664BDFE0B44D742638A43686ACB3008 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MBProgressHUD;
			productName = MBProgressHUD;
			productReference = 8B8FAB0D627B17EDE1366984278705D9 /* MBProgressHUD */;
			productType = "com.apple.product-type.framework";
		};
		94CFBA7D633ECA58DF85C327B035E6A3 /* SDWebImage-SDWebImage */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1170BFB031A7E8E958DB80918C2390A6 /* Build configuration list for PBXNativeTarget "SDWebImage-SDWebImage" */;
			buildPhases = (
				D53D4905FF9339FB22F10633AFB9571B /* Sources */,
				0BAA7D30DDCC9CA5671B4C061A66BD02 /* Frameworks */,
				616A94F9D0A409E60F46279AA29641CB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SDWebImage-SDWebImage";
			productName = SDWebImage;
			productReference = CF1281E58AA1045D4B7F33FC56691C42 /* SDWebImage-SDWebImage */;
			productType = "com.apple.product-type.bundle";
		};
		B26054DF1DEA11585A231AF6D1D80D5E /* MJRefresh-MJRefresh.Privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4FB6CF61FBE6AEA967C684029CB26D28 /* Build configuration list for PBXNativeTarget "MJRefresh-MJRefresh.Privacy" */;
			buildPhases = (
				38960E906873911F404AF798397FAE98 /* Sources */,
				12E99800F48CD09032DD519E22C85EE4 /* Frameworks */,
				B3427A842132EE06F20D77952F9BD84D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "MJRefresh-MJRefresh.Privacy";
			productName = MJRefresh.Privacy;
			productReference = 7E3097CFEFDA621E9FB0E62009FF87FC /* MJRefresh-MJRefresh.Privacy */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 16.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = ADD972DF66319CA1C506590C44010D1F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0130B3724283586C0E9D2A112D4F2AA1 /* AFNetworking */,
				479BDBD165CD08E2159181E58CD2078F /* FBAEMKit */,
				28907903AF6DD0AD321D92CD660E7E23 /* FBSDKCoreKit */,
				8EA62FEAB23070050EFD4D4508D659E5 /* FBSDKCoreKit_Basics */,
				55AF53E6C77A10ED4985E04D74A8878E /* Masonry */,
				82B0A41D3031FF27D78E17B0A9A46FB0 /* MBProgressHUD */,
				6868056D761E163D10FDAF8CF1C4D9B8 /* MJRefresh */,
				B26054DF1DEA11585A231AF6D1D80D5E /* MJRefresh-MJRefresh.Privacy */,
				7BD8D07034708B2D327AE57BE458F3C3 /* Pods-Nano_Loan */,
				3847153A6E5EEFB86565BA840768F429 /* SDWebImage */,
				94CFBA7D633ECA58DF85C327B035E6A3 /* SDWebImage-SDWebImage */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0C57E0092EF2CF6E69D51C0D2E9806BB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		44B3C0D7DDF289331B7732E9D87126DB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				16D7DCB7CC985C33EEC41B371C029C84 /* SDWebImage-SDWebImage in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4A4F8947EF95B9D0D1FCFC1296740510 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D90DF1376DF5E2EA644313BCD2E03058 /* MJRefresh.bundle in Resources */,
				327BA3DDA513422E632D3DA4A8FC60EC /* MJRefresh-MJRefresh.Privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		616A94F9D0A409E60F46279AA29641CB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				617B7E27B82DAACC48F39DDB1A1527CE /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9BB224D4E89ABC2539ABBEBDC9696C8F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B3427A842132EE06F20D77952F9BD84D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				72225C63C4CA3F910FC7784DCE35940E /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E664BDFE0B44D742638A43686ACB3008 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ECD6B9A8E754DF142B323DF2D7E0D112 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		A261CEA870C48433C3575F1CCC2BB166 /* [CP] Copy XCFrameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/FBAEMKit/FBAEMKit-xcframeworks-input-files.xcfilelist",
			);
			name = "[CP] Copy XCFrameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/FBAEMKit/FBAEMKit-xcframeworks-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/FBAEMKit/FBAEMKit-xcframeworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		A760E42366676CF529D7FA67FA8558B9 /* [CP] Copy XCFrameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics-xcframeworks-input-files.xcfilelist",
			);
			name = "[CP] Copy XCFrameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics-xcframeworks-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics-xcframeworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C05801FB1115AB56CF9E73BB6786A73A /* [CP] Copy XCFrameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/FBSDKCoreKit/FBSDKCoreKit-xcframeworks-input-files.xcfilelist",
			);
			name = "[CP] Copy XCFrameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/FBSDKCoreKit/FBSDKCoreKit-xcframeworks-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/FBSDKCoreKit/FBSDKCoreKit-xcframeworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		07FE0EDF7C4BD3D19B3E78A7915BB0E9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				50423028356F5C71A09C26626F681777 /* MBProgressHUD.m in Sources */,
				F95520AA120144213BF44DA77158DF38 /* MBProgressHUD-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		38960E906873911F404AF798397FAE98 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4E2C65E105E47F0D32B2C6ADA5FB58B1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C07995A22123B943C61586D2F20837CC /* NSBezierPath+SDRoundedCorners.m in Sources */,
				D96E15F93436B1DB960DDE9AB510B6BD /* NSButton+WebCache.m in Sources */,
				F93ED4D1045DA563CC1D3087C7238E4F /* NSData+ImageContentType.m in Sources */,
				EA94446AB5D7EC5D6A61B8F381B31099 /* NSImage+Compatibility.m in Sources */,
				67C0A2CDC97B42A303AE34BF316C2C8C /* SDAnimatedImage.m in Sources */,
				52749628BF96B939F9125BB1FDDE3CE3 /* SDAnimatedImagePlayer.m in Sources */,
				C9D162799D509B23026D3F86CC987578 /* SDAnimatedImageRep.m in Sources */,
				3297535CDED0B0870EE0D89965656805 /* SDAnimatedImageView.m in Sources */,
				1CBA380B90208FD5350F66ADEEC1A0F1 /* SDAnimatedImageView+WebCache.m in Sources */,
				D3D32F17B82FC5CB56B5C4A472ACD9DF /* SDAssociatedObject.m in Sources */,
				F62C7836BEF965F234AAC70294EA29B3 /* SDAsyncBlockOperation.m in Sources */,
				B4B7E71635CF3BD9872E9E58393C970C /* SDCallbackQueue.m in Sources */,
				320CFA02302E98A8C408A0786C049821 /* SDDeviceHelper.m in Sources */,
				EC3BD13BEEE1C2C70EC415A5385E3F96 /* SDDiskCache.m in Sources */,
				3E0356FE057BF02F1430BD873F35B6B1 /* SDDisplayLink.m in Sources */,
				152254941AEACDB6527E28D67508ABF5 /* SDFileAttributeHelper.m in Sources */,
				4DC7640E40A36308776A77D54DA5307E /* SDGraphicsImageRenderer.m in Sources */,
				D0D19B844154C5747894F960B6C0BF5A /* SDImageAPNGCoder.m in Sources */,
				6C9286C64D83FA635B84DDF89EEE597B /* SDImageAssetManager.m in Sources */,
				ACD87CF4B1D7D0A31EE549089E86787F /* SDImageAWebPCoder.m in Sources */,
				E8141F27937D3C60DF2415E091D71FDC /* SDImageCache.m in Sources */,
				9E4310021331C49FF3D3CB28A88ABC50 /* SDImageCacheConfig.m in Sources */,
				8D7E57CF1EF86090C37EDCA040FB8EBA /* SDImageCacheDefine.m in Sources */,
				24B75682B8B059FD919A8407F7D38E5A /* SDImageCachesManager.m in Sources */,
				64A640196239E834DE6731EE7CB7A673 /* SDImageCachesManagerOperation.m in Sources */,
				8EC9EF51F559247D472BC27AD2582EB4 /* SDImageCoder.m in Sources */,
				F74B0737895AD6DC8F3C2D9778A4E6D8 /* SDImageCoderHelper.m in Sources */,
				4E5CEF5269F568F729B61E77FA228958 /* SDImageCodersManager.m in Sources */,
				5CFB9A7B5E7A245241F4AD04879437FF /* SDImageFrame.m in Sources */,
				49590B0067E8A602534A1AE38C2C27FB /* SDImageFramePool.m in Sources */,
				0A681F5B1D3611879DAC60D8F5FA2DFD /* SDImageGIFCoder.m in Sources */,
				C2127875C73D2410098AB80F5E4B5A47 /* SDImageGraphics.m in Sources */,
				4D02BAA1BD88757C9D6AC6951AFB668C /* SDImageHEICCoder.m in Sources */,
				D8DA999E71BF3CA7665BC59ED50AAD5D /* SDImageIOAnimatedCoder.m in Sources */,
				F284AB7A34BEE825EB8C313437F0C400 /* SDImageIOCoder.m in Sources */,
				0921DE69D3845C45AC5A01C8734D2751 /* SDImageLoader.m in Sources */,
				E457126A7666DED46B9686263B99D2FC /* SDImageLoadersManager.m in Sources */,
				4838DBCD6667728EAE284532D8B21E10 /* SDImageTransformer.m in Sources */,
				FC93205E60C3C950572EC83697CF2662 /* SDInternalMacros.m in Sources */,
				42D19F96A647042273F246D0364C3B77 /* SDMemoryCache.m in Sources */,
				C75D6F34DD6408428E6E843DDA7CB82F /* SDWeakProxy.m in Sources */,
				84E2CF954781CBD9C07190929149983A /* SDWebImage-dummy.m in Sources */,
				328DC444E3E85145A1D3DF091E2C225B /* SDWebImageCacheKeyFilter.m in Sources */,
				8A16FBAC5F4A39084245E51D177A5FA9 /* SDWebImageCacheSerializer.m in Sources */,
				31A02B30F6A0125668E95F05DCB21A81 /* SDWebImageCompat.m in Sources */,
				2F5A2FD069FBABDE30591A84DF9FB88D /* SDWebImageDefine.m in Sources */,
				CD9A33B79474CBDBE3A7571612FB052F /* SDWebImageDownloader.m in Sources */,
				7ADAC982EF440C408C37CB327EF7A9D5 /* SDWebImageDownloaderConfig.m in Sources */,
				7277143439291D1B631DFD7C5CA4481B /* SDWebImageDownloaderDecryptor.m in Sources */,
				E2F1C542596DA1C119025B09006515DF /* SDWebImageDownloaderOperation.m in Sources */,
				0F2F96E0860F1AB7064896E0D2B0901C /* SDWebImageDownloaderRequestModifier.m in Sources */,
				BC4DBB0F7860378B09B9C0FF861B2B50 /* SDWebImageDownloaderResponseModifier.m in Sources */,
				1B8A9603A3F8D9A7BCD3B6B026448F84 /* SDWebImageError.m in Sources */,
				2BC106322877BDD113AD9391AA501613 /* SDWebImageIndicator.m in Sources */,
				CF7AD9106444B9F4C82D46AC684D26A3 /* SDWebImageManager.m in Sources */,
				AF25A814FF0A0D65FF79EC152EC71BE2 /* SDWebImageOperation.m in Sources */,
				013EF29548D769A6B936884AE5C2535A /* SDWebImageOptionsProcessor.m in Sources */,
				2B84004AB1221A608BD462E8D4BA341C /* SDWebImagePrefetcher.m in Sources */,
				CFB4828D54C4831F7B05B02FDEE77313 /* SDWebImageTransition.m in Sources */,
				13F68E946EC25038A0F858F9A6BA5DF8 /* UIButton+WebCache.m in Sources */,
				******************************** /* UIColor+SDHexString.m in Sources */,
				A0B5717DFED2312163B6222F293C2B52 /* UIImage+ExtendedCacheData.m in Sources */,
				D506F4595C868442A852AECC5FF083B7 /* UIImage+ForceDecode.m in Sources */,
				F1B08CBF7344D57B22F2EFBBCC770118 /* UIImage+GIF.m in Sources */,
				0A5C13680D1476AD861EE31A969378A9 /* UIImage+MemoryCacheCost.m in Sources */,
				37F1B9B70F767DA30596A3BA6644E41F /* UIImage+Metadata.m in Sources */,
				95147B9C545E9B66F6B9BC200EB35DF9 /* UIImage+MultiFormat.m in Sources */,
				40B4B682DBC23D33222753DA25D49641 /* UIImage+Transform.m in Sources */,
				0AFE4714903E897473AB28AD0D9E7B07 /* UIImageView+HighlightedWebCache.m in Sources */,
				8B14EFC1D98F43F78BC38ECCD66A14DA /* UIImageView+WebCache.m in Sources */,
				DCAE35CC587C05DB52C8AFD750D1283E /* UIView+WebCache.m in Sources */,
				AAA12F449ABE87694D43AA22271FF125 /* UIView+WebCacheOperation.m in Sources */,
				2FC2D532909A53348881E18D363F57B0 /* UIView+WebCacheState.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8AB80AB52448851AE6F023FEAB0D3F6F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				73A82F328FA7386A4D227872619BB283 /* MASCompositeConstraint.m in Sources */,
				4E01EC96AEC8B92FF3443CCAA2584DA6 /* MASConstraint.m in Sources */,
				08D4F37046B05382A7823C78F89B0ACC /* MASConstraintMaker.m in Sources */,
				E22E41C2FFFDC3CF4A97D20721F914AA /* MASLayoutConstraint.m in Sources */,
				125C88474A190605BBD3FAE7E42E627A /* Masonry-dummy.m in Sources */,
				76E7308986FD31C25E217252CF49056E /* MASViewAttribute.m in Sources */,
				F926674D7D29E89BDFB085520CC4CDFC /* MASViewConstraint.m in Sources */,
				EEAF8007F9311797284A7E74B481777A /* NSArray+MASAdditions.m in Sources */,
				E1BA4F623ABACB4BF2C5308666DC16C7 /* NSLayoutConstraint+MASDebugAdditions.m in Sources */,
				C6C25434FA32BED77FB193984A8F0210 /* View+MASAdditions.m in Sources */,
				A861F677DC3D972226F683C0C553FCF5 /* ViewController+MASAdditions.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8C85583CECA68B164DC51110AF13E6FB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BB4AFA8BD0A33167A5A2003A6627BFCE /* AFAutoPurgingImageCache.m in Sources */,
				9E74186C38FAB93E6209E9BF8D4870F5 /* AFHTTPSessionManager.m in Sources */,
				0312D6B222E4A663DFE3ED35CAE14421 /* AFImageDownloader.m in Sources */,
				05AE79C2626475CFDA15C800FFEAEC4B /* AFNetworkActivityIndicatorManager.m in Sources */,
				D3EEDC1A8AAA55DF23CDB729D3E2E0EE /* AFNetworking-dummy.m in Sources */,
				AC951F4137A3411BA2F7E01D0508858C /* AFNetworkReachabilityManager.m in Sources */,
				E62B22EE7D6F55848B2AB98656A2C57B /* AFSecurityPolicy.m in Sources */,
				DE1CCE35189DBF6186C484FD0821BE35 /* AFURLRequestSerialization.m in Sources */,
				93F4FED694DA572489D514D9DDFEF0C1 /* AFURLResponseSerialization.m in Sources */,
				23C0A5C619043400F23B3F042BDA80D0 /* AFURLSessionManager.m in Sources */,
				15E4D04155F21B3C06E2914DCBEB1487 /* UIActivityIndicatorView+AFNetworking.m in Sources */,
				597B38597411E71CC422895F08A745AD /* UIButton+AFNetworking.m in Sources */,
				D969F44324C5A550A4F9E52D94A97CE8 /* UIImageView+AFNetworking.m in Sources */,
				A9B6C4C3ED3EEA4DD0FFE76EEBD44E43 /* UIProgressView+AFNetworking.m in Sources */,
				4BC2CFC679C78670D7CCAB0CB5FEF32C /* UIRefreshControl+AFNetworking.m in Sources */,
				3008B1104322076167E9533A611D1604 /* WKWebView+AFNetworking.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C2BF64411FCD7584B30AEC11533888AD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				23CE18E13FAB699AD5A19CA3DDC7FB2E /* Pods-Nano_Loan-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CEFCB92DFC6C522681EC56915AD01B4E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				786A94A677B3D65FF397A4CCE3034387 /* MJRefresh-dummy.m in Sources */,
				92213E59C653C52367B079C66AF5F3F1 /* MJRefreshAutoFooter.m in Sources */,
				BD144751C3F13099EC9FC35B0E2B0CE0 /* MJRefreshAutoGifFooter.m in Sources */,
				564D02061AE23EF6021261CB5C141E70 /* MJRefreshAutoNormalFooter.m in Sources */,
				FE05C5A4B556B0BDD376B2CC0C8A2054 /* MJRefreshAutoStateFooter.m in Sources */,
				5CDD4C746A3580335B6004AF7631A7CF /* MJRefreshBackFooter.m in Sources */,
				CDDDA24076474BFEE9EC40F1E1A0B17F /* MJRefreshBackGifFooter.m in Sources */,
				2AF772FC9A2744BC34B5A8892FF2B945 /* MJRefreshBackNormalFooter.m in Sources */,
				F90D1B7A3985536ABBC7F5C4CFF91C6B /* MJRefreshBackStateFooter.m in Sources */,
				34BA423491607EBBD666F70032FCE826 /* MJRefreshComponent.m in Sources */,
				3EB9FFA38676CB17C4149A603286FEFD /* MJRefreshConfig.m in Sources */,
				B7AE3054B7362DB2A0D78FDB4581EA5D /* MJRefreshConst.m in Sources */,
				933B1C5909851CEBFE69E48514849EDC /* MJRefreshFooter.m in Sources */,
				B9A69C90CCB9BCF725EBE0B7F9CAFE83 /* MJRefreshGifHeader.m in Sources */,
				EC3E287CFC2FAF33318835692104B5A3 /* MJRefreshHeader.m in Sources */,
				EF9F754349CB7CE18D611FE17C8E8304 /* MJRefreshNormalHeader.m in Sources */,
				FE112C1E57DAF79DD883E3E9B8006E7B /* MJRefreshNormalTrailer.m in Sources */,
				4D459564B62CBFD801DB06B13C9EC93A /* MJRefreshStateHeader.m in Sources */,
				87697EDFD230BDF5B6DAA611AC08626D /* MJRefreshStateTrailer.m in Sources */,
				E63F1BC0601BA84CB6A57C6C5AD6FF0A /* MJRefreshTrailer.m in Sources */,
				6F4848A4A2336FAD34BEDCBA91BEEF49 /* NSBundle+MJRefresh.m in Sources */,
				573B039F8868FFFCEF8CDCC4FB9819C8 /* UICollectionViewLayout+MJRefresh.m in Sources */,
				54A7D3D251FC888FA93ADDCE7865AE2F /* UIScrollView+MJExtension.m in Sources */,
				5E01DE8DD929768591CFA26F8659FFF0 /* UIScrollView+MJRefresh.m in Sources */,
				A160E2FF8CC42AF58486C0A4AE34AA35 /* UIView+MJExtension.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D53D4905FF9339FB22F10633AFB9571B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		166551A15307C14C93C75A280E83F037 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FBSDKCoreKit_Basics;
			target = 8EA62FEAB23070050EFD4D4508D659E5 /* FBSDKCoreKit_Basics */;
			targetProxy = D2291A5AD3B3F70AA59B0508EFAA7AA0 /* PBXContainerItemProxy */;
		};
		3AE4598BB741766C8A9F7423056CE411 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FBSDKCoreKit_Basics;
			target = 8EA62FEAB23070050EFD4D4508D659E5 /* FBSDKCoreKit_Basics */;
			targetProxy = 9145D864B4E2F1363956581CD8712005 /* PBXContainerItemProxy */;
		};
		607161BDBD3B91DCC22F7BCAF26467E5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MBProgressHUD;
			target = 82B0A41D3031FF27D78E17B0A9A46FB0 /* MBProgressHUD */;
			targetProxy = F44F0F009949752F9B43812F21818368 /* PBXContainerItemProxy */;
		};
		6335ECB71FBAC20B99F368AB90EACAB6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FBSDKCoreKit_Basics;
			target = 8EA62FEAB23070050EFD4D4508D659E5 /* FBSDKCoreKit_Basics */;
			targetProxy = 0D5556361B032ECE56C2000CF77B4838 /* PBXContainerItemProxy */;
		};
		63F9A7FD383742B5297CAB1A76AEBBAE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FBAEMKit;
			target = 479BDBD165CD08E2159181E58CD2078F /* FBAEMKit */;
			targetProxy = C9464F204F473A476B98E179BF693C89 /* PBXContainerItemProxy */;
		};
		7471E2AB5C70DB7B20295662E411F8AB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FBSDKCoreKit;
			target = 28907903AF6DD0AD321D92CD660E7E23 /* FBSDKCoreKit */;
			targetProxy = 2B3AA200267FB4CD1A593D18DA38F626 /* PBXContainerItemProxy */;
		};
		75A4016AE104038A382C1C5906746BB9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "MJRefresh-MJRefresh.Privacy";
			target = B26054DF1DEA11585A231AF6D1D80D5E /* MJRefresh-MJRefresh.Privacy */;
			targetProxy = 41CFEEA77C98E40881A18930DE32E907 /* PBXContainerItemProxy */;
		};
		7F5925D5E83788BD35012AFA3ECDD5A3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "SDWebImage-SDWebImage";
			target = 94CFBA7D633ECA58DF85C327B035E6A3 /* SDWebImage-SDWebImage */;
			targetProxy = B7291C21D0E8234F35EA7B3CA26D01AF /* PBXContainerItemProxy */;
		};
		BBE609435CC0CBE9C87842DB4A3723F8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Masonry;
			target = 55AF53E6C77A10ED4985E04D74A8878E /* Masonry */;
			targetProxy = 78E76CA90215151AA2C33C51BCF862C4 /* PBXContainerItemProxy */;
		};
		C8CABA02A786DA48E9DB3B541316E7D8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FBAEMKit;
			target = 479BDBD165CD08E2159181E58CD2078F /* FBAEMKit */;
			targetProxy = FCD223F85A2C9E016876F441ED25FA87 /* PBXContainerItemProxy */;
		};
		CF49881715FE80D8F9455937AD4F64B6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AFNetworking;
			target = 0130B3724283586C0E9D2A112D4F2AA1 /* AFNetworking */;
			targetProxy = 89710304AB044E533C928C864D0E804D /* PBXContainerItemProxy */;
		};
		ECD0BB27E541521EDF25B18E9A76EDA7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MJRefresh;
			target = 6868056D761E163D10FDAF8CF1C4D9B8 /* MJRefresh */;
			targetProxy = 66596F3590FAE8DF8059ECD650A5EDFF /* PBXContainerItemProxy */;
		};
		F4C226DB69A3A39AD3A8C8C2AAC8559F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SDWebImage;
			target = 3847153A6E5EEFB86565BA840768F429 /* SDWebImage */;
			targetProxy = 1091B66A7DD14CAC562909F218C5307D /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		269E1B8CDF70A235EA8FC7DEDD6CAEF6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CA413C8E2996E5305CFF922E51E27757 /* SDWebImage.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SDWebImage";
				IBSC_MODULE = SDWebImage;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/ResourceBundle-SDWebImage-SDWebImage-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		2D1085CA7BD144CABF012FC10C6C9120 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7EE0B708208DA54C7792D0858F97CE53 /* Masonry.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/Masonry/Masonry-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/Masonry/Masonry-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/Masonry/Masonry.modulemap";
				PRODUCT_MODULE_NAME = Masonry;
				PRODUCT_NAME = Masonry;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		4BC7450F9457737EE3E637BA155B56F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		4D4ABAF622C6036588675F101E4470AE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5A94511790704271ECE7B1239B313843 /* Pods-Nano_Loan.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-Nano_Loan/Pods-Nano_Loan-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Nano_Loan/Pods-Nano_Loan.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		51753BD6FE635BB9421BCA4C05F63C6A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 007ADD6FF507592CA39ADDB8D6027C4B /* MJRefresh.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/MJRefresh/MJRefresh-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/MJRefresh/MJRefresh-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/MJRefresh/MJRefresh.modulemap";
				PRODUCT_MODULE_NAME = MJRefresh;
				PRODUCT_NAME = MJRefresh;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		614F7847ADAD2F1EEC9E48FAEC955108 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 807409C5D7B136690C2D2F02306755F2 /* MJRefresh.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/MJRefresh/MJRefresh-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/MJRefresh/MJRefresh-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/MJRefresh/MJRefresh.modulemap";
				PRODUCT_MODULE_NAME = MJRefresh;
				PRODUCT_NAME = MJRefresh;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		68DA133648CDDACB26B9F427A2691389 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 70D4DA719BD7B77C17376AF0A5648950 /* SDWebImage.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SDWebImage";
				IBSC_MODULE = SDWebImage;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/ResourceBundle-SDWebImage-SDWebImage-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		7F61D7A866D5005E987D463223322769 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E37230CAD386F393FE18754CFB5B04C7 /* FBAEMKit.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		8B5A46FF8D3C1289CDEE3BAFACABCD2A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		9F519E5162C0E51D10B7E999E2FD0125 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CA413C8E2996E5305CFF922E51E27757 /* SDWebImage.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SDWebImage/SDWebImage-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/SDWebImage-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SDWebImage/SDWebImage.modulemap";
				PRODUCT_MODULE_NAME = SDWebImage;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		A2F8AB6E2CCE19ADD6D909B79AA3D71B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6C5DFC15211FCFAE28B79354EE4CEE3F /* FBSDKCoreKit_Basics.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		B04295D726C1883ADA40A304483D7E33 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 70D4DA719BD7B77C17376AF0A5648950 /* SDWebImage.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SDWebImage/SDWebImage-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/SDWebImage-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SDWebImage/SDWebImage.modulemap";
				PRODUCT_MODULE_NAME = SDWebImage;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B92194816CD555817018B3AE6BD0219D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DFBCAAB37C2CEBD6FDCABDF38D0C83E7 /* FBAEMKit.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		BCD70A0BB22D0C72BAF7207A7D4F44BD /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 105ADDBD5121C299E136A86C6823E6BB /* MBProgressHUD.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/MBProgressHUD/MBProgressHUD-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/MBProgressHUD/MBProgressHUD-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/MBProgressHUD/MBProgressHUD.modulemap";
				PRODUCT_MODULE_NAME = MBProgressHUD;
				PRODUCT_NAME = MBProgressHUD;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		BE0CDEB7BE0A402663AFCC2111742430 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DAA02086A5396C2AA0002DFA34B0634A /* MBProgressHUD.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/MBProgressHUD/MBProgressHUD-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/MBProgressHUD/MBProgressHUD-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/MBProgressHUD/MBProgressHUD.modulemap";
				PRODUCT_MODULE_NAME = MBProgressHUD;
				PRODUCT_NAME = MBProgressHUD;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		CEE7FEC0A1B23DE7053203A448EEB294 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5216AD9C10B699BF9FE794C8C04B7506 /* AFNetworking.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AFNetworking/AFNetworking-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AFNetworking/AFNetworking-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AFNetworking/AFNetworking.modulemap";
				PRODUCT_MODULE_NAME = AFNetworking;
				PRODUCT_NAME = AFNetworking;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		D0AB0AEF4014B926FCD853D3AE0A370A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1228C9454A54DBE46799F2B283099983 /* Masonry.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/Masonry/Masonry-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/Masonry/Masonry-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/Masonry/Masonry.modulemap";
				PRODUCT_MODULE_NAME = Masonry;
				PRODUCT_NAME = Masonry;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		D7EB5808742F795357A48F8FB201650A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 17E35AFC29F73D366F8DC32D199561B6 /* FBSDKCoreKit.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		DA533AA9B577872DAFB44EF2CF26C49A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B8E113D5B2CC5F4AFBB17F4B14FB0553 /* AFNetworking.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AFNetworking/AFNetworking-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AFNetworking/AFNetworking-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AFNetworking/AFNetworking.modulemap";
				PRODUCT_MODULE_NAME = AFNetworking;
				PRODUCT_NAME = AFNetworking;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		E604BF76FD1462AA5E1652B4D0B65D29 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 807409C5D7B136690C2D2F02306755F2 /* MJRefresh.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MJRefresh";
				IBSC_MODULE = MJRefresh;
				INFOPLIST_FILE = "Target Support Files/MJRefresh/ResourceBundle-MJRefresh.Privacy-MJRefresh-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = MJRefresh.Privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		E8BF49BA85DACFB3EC640ACDC4C9089C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 007ADD6FF507592CA39ADDB8D6027C4B /* MJRefresh.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MJRefresh";
				IBSC_MODULE = MJRefresh;
				INFOPLIST_FILE = "Target Support Files/MJRefresh/ResourceBundle-MJRefresh.Privacy-MJRefresh-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = MJRefresh.Privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		F2D1BBC238A574C89E47FE72971E6DDB /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 65D65A306338246A5219CC28799C7C62 /* Pods-Nano_Loan.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-Nano_Loan/Pods-Nano_Loan-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Nano_Loan/Pods-Nano_Loan.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		F8538C7B8F881F606D4B24F041EFF275 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EADA52AD6202CAF4A1C888B13C187276 /* FBSDKCoreKit_Basics.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		FC4D48C7EE884A87C34B90F9D93E95E4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FC09B20DF07A51D85D8FC96DC354D860 /* FBSDKCoreKit.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1170BFB031A7E8E958DB80918C2390A6 /* Build configuration list for PBXNativeTarget "SDWebImage-SDWebImage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				68DA133648CDDACB26B9F427A2691389 /* Debug */,
				269E1B8CDF70A235EA8FC7DEDD6CAEF6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		29BB59B7B51BC6194771995E3356CF70 /* Build configuration list for PBXNativeTarget "MJRefresh" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				614F7847ADAD2F1EEC9E48FAEC955108 /* Debug */,
				51753BD6FE635BB9421BCA4C05F63C6A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2F9E94B5F79E365095CB33D3D3FCA6A2 /* Build configuration list for PBXNativeTarget "SDWebImage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B04295D726C1883ADA40A304483D7E33 /* Debug */,
				9F519E5162C0E51D10B7E999E2FD0125 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		442545805DAF10650DBA7C00DEAAD47B /* Build configuration list for PBXAggregateTarget "FBAEMKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B92194816CD555817018B3AE6BD0219D /* Debug */,
				7F61D7A866D5005E987D463223322769 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4BC7450F9457737EE3E637BA155B56F7 /* Debug */,
				8B5A46FF8D3C1289CDEE3BAFACABCD2A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4FB6CF61FBE6AEA967C684029CB26D28 /* Build configuration list for PBXNativeTarget "MJRefresh-MJRefresh.Privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E604BF76FD1462AA5E1652B4D0B65D29 /* Debug */,
				E8BF49BA85DACFB3EC640ACDC4C9089C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7CEB2E47380AD51987AA02ECD4DFBCD9 /* Build configuration list for PBXNativeTarget "AFNetworking" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CEE7FEC0A1B23DE7053203A448EEB294 /* Debug */,
				DA533AA9B577872DAFB44EF2CF26C49A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8F464E8149CF17E45CDE093595D7DBAA /* Build configuration list for PBXNativeTarget "Pods-Nano_Loan" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F2D1BBC238A574C89E47FE72971E6DDB /* Debug */,
				4D4ABAF622C6036588675F101E4470AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		945C0F2B41CBADE68A142120AE9A4AF3 /* Build configuration list for PBXNativeTarget "MBProgressHUD" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BCD70A0BB22D0C72BAF7207A7D4F44BD /* Debug */,
				BE0CDEB7BE0A402663AFCC2111742430 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AAA1F8799DB68036C3BE983C05FAA2C7 /* Build configuration list for PBXNativeTarget "Masonry" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D1085CA7BD144CABF012FC10C6C9120 /* Debug */,
				D0AB0AEF4014B926FCD853D3AE0A370A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AB73C55C047FFED8C4B6CB08922D32ED /* Build configuration list for PBXAggregateTarget "FBSDKCoreKit_Basics" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F8538C7B8F881F606D4B24F041EFF275 /* Debug */,
				A2F8AB6E2CCE19ADD6D909B79AA3D71B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C320C79E1F381838923FC2BF4FAB74A1 /* Build configuration list for PBXAggregateTarget "FBSDKCoreKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FC4D48C7EE884A87C34B90F9D93E95E4 /* Debug */,
				D7EB5808742F795357A48F8FB201650A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
