/* The format string for device login instructions */
"DeviceLogin.LogInPrompt" = "Visit %@ and enter the code shown above.";

/* The 'or' string for smart login instructions */
"DeviceLogin.SmartLogInOrLabel" = "-- OR --";

/* The string for smart login instructions */
"DeviceLogin.SmartLogInPrompt" = "To connect your account, open the Facebook app on your mobile device and check for notifications.";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Cancel";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Please log in to this app again to reconnect your Facebook account.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "The server is temporarily busy, please try again.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Cancel";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Log out";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Logged in using Facebook";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Logged in as %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Log in";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Continue with Facebook";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Log in with Facebook";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Log out";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Access has not been granted to the Facebook account. Verify device settings.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Unable to connect to Facebook. Please check your network connection and try again.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Your Facebook password has changed. To confirm your password, open Settings &gt; Facebook and tap your name.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "The Facebook account has not been configured on the device.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Your account is not confirmed. Please log in to www.facebook.com and follow the instructions given.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "You cannot log in to apps at this time. Please log in to www.facebook.com and follow the instructions given.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "You're in control – choose what information you want to share with apps.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Log In";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Send";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Share";

/* The title for the alert when smart login requires confirmation */
"SmartLogin.ConfirmationTitle" = "Confirm Login";

/* The format string to continue as <name> for the alert when smart login requires confirmation */
"SmartLogin.Continue" = "Continue as %@";

/* The cancel label for the alert when smart login requires confirmation */
"SmartLogin.NotYou" = "Not you?";
