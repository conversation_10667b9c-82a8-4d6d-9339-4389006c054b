/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Kunjungi %@ dan masukkan kode yang ditampilkan di atas.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Untuk menghubungkan akun Anda, buka aplikasi Facebook di perangkat seluler Anda dan periksa pemberitahuan.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- ATAU -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Batal";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Harap masuk ke aplikasi ini lagi untuk menghubungkan kembali akun Facebook Anda.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "Server untuk sementara sedang sibuk, harap coba lagi.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Batal";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Keluar";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Masuk menggunakan Facebook";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Masuk sebagai %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Masuk";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Lanjutkan dengan Facebook";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Masuk menggunakan Facebook";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Keluar";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Akses belum diberikan ke akun Facebook. Verifikasi pengaturan perangkat.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Tidak dapat terhubung ke Facebook. Periksa koneksi jaringan dan coba lagi.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Kata sandi Facebook Anda telah diubah. Untuk mengonfirmasi kata sandi Anda, buka Pengaturan &gt; Facebook, lalu ketuk nama Anda.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "Akun Facebook belum dikonfigurasikan di perangkat.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Akun Anda tidak dikonfirmasi. Masuk ke www.facebook.com dan ikuti petunjuknya.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Saat ini Anda tidak dapat masuk ke aplikasi. Masuk ke www.facebook.com dan ikuti petunjuknya.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Anda memegang kendali - pilih info yang ingin Anda bagikan dengan aplikasi.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Masuk";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Kirim";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Berbagi";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Bukan Anda?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Konfirmasikan Masuk";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Lanjutkan sebagai %@";
