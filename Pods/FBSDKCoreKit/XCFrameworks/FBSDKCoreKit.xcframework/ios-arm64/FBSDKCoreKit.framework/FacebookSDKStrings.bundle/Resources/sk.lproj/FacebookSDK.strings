/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Navštívte stránku %@ a zadajte kód zobrazený vyššie.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Na pripojenie svojho účtu otvorte vo svojom mobilnom zariadení aplikáciu Facebook a skontrolujte upozornenia.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- ALEBO -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Zru<PERSON><PERSON><PERSON>";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Ak sa chcete znova pripojiť k svojmu účtu na Facebooku, prihláste sa znova do tejto aplikácie.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "Server je dočasne zaneprázdnený – skúste znova.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Zrušiť";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Odhlásiť";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Prihlásený cez Facebook";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Prihlásený ako %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Prihlásiť sa";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Pokračovať s Facebookom";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Prihlásiť sa cez Facebook";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Odhlásiť sa";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Danému účtu na Facebooku nebol poskytnutý prístup. Skontrolujte nastavenia zariadenia.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Nedá sa pripojiť k Facebooku. Skontrolujte svoje sieťové pripojenie a skúste znova.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Vaše heslo pre Facebook sa zmenilo. Na potvrdenie svojho hesla otvorte Nastavenia &gt; Facebook a ťuknite na svoje meno.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "Daný účet na Facebooku nebol nastavený v zariadení.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Váš účet nie je potvrdený. Prihláste sa na stránke www.facebook.com a postupujte podľa pokynov.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Momentálne sa nemôžete prihlásiť do aplikácií. Prihláste sa na stránke www.facebook.com a postupujte podľa pokynov.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Máte to pod kontrolou: vyberte, ktoré informácie chcete zdieľať s aplikáciami.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Prihlásenie sa";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Odoslať";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Zdieľať";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Nie ste to vy?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Potvrdiť prihlásenie";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Pokračovať ako %@";
