/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Besoek %@ en voer die kode in wat hierbo vertoon word.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Om jou rekening te verbind, maak die Facebook-toepassing op jou mobiele toestel oop en kontroleer vir kennisgewings.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- OF -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "Goed";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Kanselleer";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Meld asseblief weer by hierdie toepassing aan om jou Facebook-rekening te herkoppel. ";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "Goed";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "Die bediener is tydelik besig, probeer asseblief weer.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Kanselleer";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Meld af";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Met Facebook aangemeld";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "As %@ aangemeld";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Meld aan";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Gaan voort met Facebook";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Meld met Facebook aan";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Meld af";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Toegang is nie na die Facebook-rekening verleen nie. Verifieer toestelinstellings.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Kan nie aan Facebook koppel nie. Gaan jou netwerkverbinding na en probeer weer.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Jou Facebook-wagwoord het verander. Om jou wagwoord te bevestig, maak Instellings &gt; Facebook oop en tik jou naam.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "Die Facebook-rekening is nie op die toestel opgestel nie.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Jou rekening is nie bevestig nie. Meld asseblief by www.facebook.com aan en volg die gegewe instruksies.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Jy kan nie op die oomblik by toepassings aanmeld nie. Meld asseblief by www.facebook.com aan en volg die gegewe instruksies.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Jy is in beheer – kies watter inligting jy met toepassings wil deel.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Meld aan";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Stuur";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Deel";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Nie jy nie?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Bevestig aantekening";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Gaan voort as %@";
