/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "%@ ની મુલાકાત લો અને ઉપર બતાવ્યા પ્રમાણે કોડ દાખલ કરો.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "તમારા એકાઉન્ટ સાથે જોડાવા માટે, તમારા મોબાઇલ ઉપકરણ પર Facebook એપ્લિકેશન ખોલો અને સૂચનો માટે તપાસો.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- અથવા -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "ઠીક";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "રદ કરો";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "તમારા ફેસબુક ખાતાને ફરીથી કનેક્ટ કરવા માટે કૃપા કરીને આ એપ્લિકેશનમાં ફરી લૉગ ઇન કરો.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "ઠીક";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "સર્વર અસ્થાયીરૂપે વ્યસ્ત છે, કૃપા કરીને ફરી પ્રયાસ કરો.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "રદ કરો";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "લૉગ આઉટ કરો";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "ફેસબુકનો ઉપયોગ કરીને લૉગ ઇન કરો";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "%@ તરીકે લૉગ ઇન થયાં";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "લૉગ ઇન કરો";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Facebook સાથે ચાલુ રાખો";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "ફેસબુક સાથે લૉગ ઇન કરો";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "લૉગ આઉટ કરો";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "ફેસબુક ખાતા પર ઍક્સેસ આપવામાં આવી નથી. ઉપકરણ સેટિંગ્સ ચકાસો";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "ફેસબુકથી કનેક્ટ કરવામાં અસમર્થ. તમારું નેટવર્ક કનેક્શન તપાસો અને ફરી પ્રયાસ કરો.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "તમારો ફેસબુક પાસવર્ડ બદલાઈ ગયો છે. તમારા પાસવર્ડની પુષ્ટિ કરવા માટે, સેટિંગ્સ &gt; ફેસબુક ખોલો અને તમારા નામ પર ટૅપ કરો.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "ઉપકરણ પર ફેસબુક ખાતું કન્ફિગર કરવામાં આવ્યું નથી.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "તમારા ખાતાની પુષ્ટિ કરવામાં આવી નથી. કૃપા કરીને www.facebook.com પર લૉગ ઇન કરો અને આપેલા સૂચનોને અનુસરો.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "તમે આ સમયે એપ્લિકેશન્સમાં લૉગ ઇન કરી શકતાં નથી. કૃપા કરીને www.facebook.com પર લૉગ ઇન કરો અને આપેલા સૂચનોને અનુસરો.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "તમે નિયંત્રણ કરો છો - તમે એપ્લિકેશન્સ સાથે કઈ માહિતી શેર કરવા માંગો છો તે પસંદ કરો.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "લૉગ ઇન કરો";

/* The label for FBSDKSendButton */
"SendButton.Send" = "મોકલો";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "શેર કરો";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "તમે નહિ?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "લૉગિનની પુષ્ટિ કરો";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "%@ તરીકે ચાલુ રાખો";
