/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Visita %@ e inserisci il codice mostrato sopra.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Per collegare il tuo account, apri l'app Facebook sul tuo dispositivo mobile e controlla se hai notifiche.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- OPPURE -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Annulla";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Effettua di nuovo l'accesso a questa applicazione per riconnettere il tuo account Facebook.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "Il server è temporaneamente occupato, riprova.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Annulla";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Esci";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Accesso effettuato tramite Facebook";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Accesso effettuato come %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Accedi";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Continua con Facebook";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Accedi con Facebook";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Esci";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "All'account Facebook non è stato concesso l'accesso. Verifica le impostazioni del dispositivo.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Impossibile connettersi a Facebook. Controlla la tua connessione e riprova.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "La tua password di Facebook è stata modificata. Per confermare la password, apri Impostazioni &gt; Facebook e tocca il tuo nome.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "L'account Facebook non è stato configurato nel dispositivo.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Il tuo account non è stato verificato. Accedi a www.facebook.com e segui le istruzioni fornite.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Impossibile accedere alle applicazioni al momento. Accedi a www.facebook.com e segui le istruzioni fornite.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Il controllo è nelle tue mani: scegli quali informazioni condividere con le app.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Accedi";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Invia";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Condividi";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Non sei tu?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Conferma l'accesso";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Continua come %@";
