/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Lawati %@ dan masukkan kod yang ditunjukkan di atas.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Untuk menghubungkan akaun anda, buka apl Facebook di peranti mudah alih anda dan semak pemberitahuan.";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Bukan anda?";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Batal";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Sila log masuk ke aplikasi ini sekali lagi untuk menyambung semula akaun Facebook anda.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "Pelayan ini sibuk buat sementara waktu, sila cuba lagi.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Batal";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Log Keluar";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Dilog masuk menggunakan Facebook";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Dilog masuk sebagai %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Log masuk";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Teruskan dengan Facebook";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Log masuk dengan Facebook";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Log keluar";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Akses ke akaun Facebook tidak diberikan. Sahkan tetapan peranti.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Tidak dapat menyambung ke Facebook. Semak sambungan rangkaian anda dan cuba lagi.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Kata laluan Facebook anda telah ditukar. Untuk mengesahkan kata laluan anda, buka Tetapan &gt; Facebook dan ketik nama anda.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "Akaun Facebook masih belum dikonfigurasikan pada peranti.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Akaun anda tidak disahkan. Sila log masuk ke www.facebook.com dan ikuti arahan yang diberi.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Anda tidak boleh log masuk ke aplikasi pada masa ini. Sila log masuk ke www.facebook.com dan ikuti arahan yang diberi.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Anda yang mengawal - pilih maklumat yang anda ingin kongsi dengan apl.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Log Masuk";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Hantar";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Kongsi";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Sahkan Log Masuk";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Teruskan sebagai %@";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- ATAU -";
