/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "تفضل بزيارة %@ وإدخال الرمز الموضح أدناه.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "لربط حسابك، افتح تطبيق فيسبوك على جهازك المحمول ثم تفقد الإشعارات.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- أو -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "موافق";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "إلغاء";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "يرجى تسجيل الدخول إلى هذا التطبيق مرة أخرى لإعادة الاتصال بحساب فيسبوك.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "موافق";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "الخادم مشغول مؤقتًا، يرجى إعادة المحاولة.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "إلغاء";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "تسجيل الخروج";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "تم تسجيل الدخول بحساب فيسبوك";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "تم تسجيل الدخول باسم %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "تسجيل الدخول";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "متابعة بحساب فيسبوك";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "تسجيل الدخول بحساب فيسبوك";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "تسجيل الخروج";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "لم يتم منح إذن الوصول لحساب فيسبوك. تحقق من إعدادات الجهاز.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "تعذر الاتصال بفيسبوك. يُرجى التحقق من الاتصال بالإنترنت وإعادة المحاولة.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "تم تغيير كلمة سر فيسبوك الخاصة بك. لتأكيد كلمة السر، افتح الإعدادات &gt; فيسبوك ثم اضغط على اسمك.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "لم تتم تهيئة حساب فيسبوك على الجهاز.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "لم يتم تأكيد حسابك. يُرجى تسجيل الدخول إلى www.facebook.com واتباع التعليمات الموضحة.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "لا يمكنك تسجيل الدخول إلى التطبيقات حاليًا. يُرجى تسجيل الدخول إلى www.facebook.com واتباع التعليمات الموضحة.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "أنت المتحكم - اختر المعلومات التي تريد مشاركتها مع التطبيقات.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "تسجيل الدخول";

/* The label for FBSDKSendButton */
"SendButton.Send" = "إرسال";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "مشاركة";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "لست أنت؟";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "تأكيد تسجيل الدخول";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "متابعة باسم %@";
