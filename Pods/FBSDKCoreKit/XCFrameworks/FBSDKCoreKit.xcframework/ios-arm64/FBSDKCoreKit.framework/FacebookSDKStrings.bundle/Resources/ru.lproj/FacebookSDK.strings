/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Откройте страницу %@ и введите представленный выше код.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Чтобы подключить аккаунт, откройте приложение Facebook на мобильном устройстве и проверьте уведомления.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- ИЛИ -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Отменить";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Войдите в это приложение еще раз, чтобы повторно подключить ваш аккаунт Facebook.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "Сервер временно загружен, повторите попытку.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Отменить";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Выход";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Выполнен вход с помощью Facebook";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Вы вошли как %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Вход";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Продолжить с Facebook";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Вход через Facebook";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Выход";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Аккаунту Facebook доступ не предоставлен. Проверьте настройки устройства.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Не удалось подключиться к Facebook. Проверьте сетевое подключение и повторите попытку.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Ваш пароль Facebook изменен. Чтобы подтвердить пароль, откройте «Настройки» &gt; Facebook и коснитесь вашего имени.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "На устройстве не настроен аккаунт Facebook.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Ваш аккаунт не подтвержден. Войдите на сайт www.facebook.com и следуйте инструкциям.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Сейчас нельзя войти в приложения. Войдите на сайт www.facebook.com и следуйте инструкциям.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Вы сами выбираете, какой информацией поделиться с приложениями.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Вход в систему";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Отправить";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Поделиться";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Это не вы?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Подтвердить вход";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Продолжить как %@";
