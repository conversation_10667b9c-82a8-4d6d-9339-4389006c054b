/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "%@ पर जाएँ और ऊपर दिया गया कोड दर्ज करें.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "अपने खाते को कनेक्ट करने के लिए, अपने मोबाइल एप्लिकेशन में Facebook एप्लिकेशन खोलें और सूचनाओं के लिए जाँच करें.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- या -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "ठीक";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "रद्द करें";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "अपने Facebook खाते से फिर से कनेक्ट होने के लिए कृपया इस एप्लिकेशन में फिर से लॉग इन करें.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "ठीक";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "सर्वर अस्थायी रूप से व्यस्त है, कृपया फिर से कोशिश करें.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "रद्द करें";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "लॉग आउट करें";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Facebook का उपयोग करके लॉग इन किया हुआ है";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "%@ के रूप में लॉग इन किया हुआ है";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "लॉग इन करें";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Facebook के साथ जारी रखें";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Facebook से लॉग इन करें";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "लॉग आउट";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Facebook खाते को एक्सेस नहीं दी गई है. डिवाइस सेटिंग सत्यापित करें.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Facebook से कनेक्ट होने में असमर्थ. कृपया अपने नेटवर्क कनेक्शन की जाँच करें फिर से प्रयास करें.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "आपका Facebook पासवर्ड बदल गया है. अपना पासवर्ड कन्फ़र्म करने के लिए, सेटिंग &gt; Facebook खोलें और अपना नाम टैप करें.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "डिवाइस पर Facebook खाता कॉन्फ़िगर नहीं किया गया है.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "आपका खाता कन्फ़र्म नहीं किया गया है. कृपया www.facebook.com में लॉग इन करें और दिए गए निर्देशों का पालन करें.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "आप इस समय एप्लिकेशन में लॉग इन नहीं कर सकते. कृपया www.facebook.com में लॉग इन करें और दिए गए निर्देशों का पालन करें.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "आप नियंत्रण में हैं - चुनें कि आप एप्लिकेशन से कौन-सी जानकारी साझा करना चाहते हैं.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "लॉग इन करें";

/* The label for FBSDKSendButton */
"SendButton.Send" = "भेजें";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "साझा करें";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "आप नहीं हैं?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "लॉग इन कन्फ़र्म करें";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "%@ के रूप में जारी रखें";
