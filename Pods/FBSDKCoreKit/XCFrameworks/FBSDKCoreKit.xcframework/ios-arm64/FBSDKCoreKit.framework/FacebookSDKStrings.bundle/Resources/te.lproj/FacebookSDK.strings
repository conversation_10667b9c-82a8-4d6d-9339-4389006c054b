/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "%@ లింక్‌ను సందర్శించి, ఎగువ చూపిన కోడ్‌ను నమోదు చేయండి.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "మీ ఖాతాను అనుసంధానించడానికి, మీ మొబైల్ పరికరంలో Facebook అనువర్తనాన్ని తెరిచి, నోటిఫికేషన్‌లను తనిఖీ చేయండి.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- లేదా -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "ఒప్పుకుంటున్నాను";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "రద్దు చేయి";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "దయచేసి మీ Facebook ఖాతాను మళ్లీ కనెక్ట్ చేయడానికి ఈ అప్లికేషన్‌కు మళ్లీ లాగిన్ చేయండి.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "ఒప్పుకుంటున్నాను";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "సర్వర్ తాత్కాలికంగా బిజీగా ఉంది, దయచేసి మళ్లీ ప్రయత్నించండి.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "రద్దు చేయి";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "లాగ్ అవుట్ చేయి";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Facebook ఉపయోగించి లాగిన్ చేసారు";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "%@గా లాగిన్ చేసారు";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "లాగిన్ చేయి";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Facebookతో కొనసాగించండి";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Facebookతో లాగిన్ చేయి";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "లాగ్ అవుట్ చేయి";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Facebook ఖాతాకు ప్రాప్యత మంజూరు చేయలేదు. పరికర సెట్టింగ్‌లను సరిచూడండి.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Facebookకి కనెక్ట్ చేయడం సాధ్యపడలేదు. మీ నెట్‌వర్క్ కనెక్షన్‌ను తనిఖీ చేసి, మళ్లీ ప్రయత్నించండి.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "మీ Facebook పాస్‌వర్డ్ మారింది. మీ పాస్‌వర్డ్‌ను నిర్ధారించడానికి, సెట్టింగ్‌లు &gt; Facebook తెరిచి, మీ పేరు నొక్కండి.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "పరికరంలో Facebook ఖాతా కాన్ఫిగర్ చేయబడలేదు.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "మీ ఖాతా నిర్ధారించబడలేదు. దయచేసి www.facebook.comకి లాగిన్ చేసి, అందించిన సూచనలను అనుసరించండి.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "ప్రస్తుతం మీరు అప్లికేషన్‌లకు లాగిన్ చేయలేరు. దయచేసి www.facebook.comకి లాగిన్ చేసి, అందించిన సూచనలను అనుసరించండి.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "మీకు నియంత్రణ ఉంది - మీరు అనువర్తనాలతో భాగస్వామ్యం చేయాలనుకునే సమాచారాన్ని ఎంచుకోండి.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "లాగిన్ చేయండి";

/* The label for FBSDKSendButton */
"SendButton.Send" = "పంపు";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "భాగస్వామ్యం చేయండి";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "మీరు కాదా?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "లాగిన్‌ను నిర్ధారించండి";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "%@ వలె కొనసాగండి";
