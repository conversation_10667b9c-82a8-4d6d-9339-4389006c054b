<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>FacebookSDKStrings.bundle/Resources/af.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Ze3vRmqRphRbVord7pGibO3fPjM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ar.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RU5U5p2+HVYWsFfSq227cr6oPa4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/bn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			062Bxy406hTTH31PhS52P1+Clqs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/cs.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mD61p8uMAsJbHWM5u43pGNZFp74=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/da.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jOzBKA4li06kg94bqglzoOLJD9M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/de.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nEbzzEMN0NGvB2cD2IpkSbWV6NM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/el.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/j4hlLRB9a6Bz377kSUlsx1c+Uk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/en.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			X2K5j4DkrzLxC/mhfhHoIXx5aRk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/en_GB.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			T3svla3GmadkuwnmQcwbAvlYi98=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/es.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8Bwe0QXPL21MImzsItkC041zC+s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/es_ES.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			c5FkRPxpv9zvua45N7qY/2Lnjc4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/fi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cpEfxM3HMb6H6HEuQEa8afs87bA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/fil.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QDnup8k5E1ovyik1vJG+36NnxRY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/fr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+kWYsXEYhJPWju3cpYvxA0fNPIU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/gu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XWLBntIiv6IgAicsZYD1u2/ZCNE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/he.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9CSDzNjRF5Zi4T8lQzEb4BFHrV8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/hi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3UoJKTt98zMp4MoNL+3/r86aYhQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/hr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lwcfpmcTECcYUnEE5hPsizpm3wg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/hu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IzpGhcaXvGa6oKaqvoknvGo7TtU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/id.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EC8VfATHFmjNpky43WLkcretWaY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/it.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xPK7qaQJKfzP3MZ0b3VJyO+gNMw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ja.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CKj+8eG+ZnwNE9X55i68+oS18Ok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/kn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EsrhFpmgw71P+TyRPtinsIr4hDQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ko.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NaTTNshzDqPGr0O5SATt5Ar/Ke4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ml.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mifHGo/qSi071/IqjLRUwRFs640=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/mr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			51s+e6B/gw6shnNo/gbRAQIunSc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ms.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			evzrkbxiAdaJ+exnQUlBrhVkZjE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/nb.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WMN/9oOghCcQVhMJD5HywSIIetM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/nl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cxRuVwKkGzmB3JnxcdYfSlMU+3Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/pa.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VuOm5+GxMy4d/JXxpVyHXLsdpBc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/pl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Hv1fBzZGisbifod43+Yl7abQPcA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/pt.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			70uwcrwkXYlkAfk5UaIrUQkWQI0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/pt_PT.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p9PvnBPstHznGo+paZZrb+pNMAc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ru.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bJltHFKTun/WYe6nQvEAlKM2dRM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/sk.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dDydplGYNYff6uM8Gft5tZqTXhI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/sv.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			N7VTH3m8nIqk04E4+7aclr63OsA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ta.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8ujSZal8Bl4w2uQzbRuH+iyid3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/te.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Dzl0I4eYOYud+sVAICju4r/2xwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/th.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i5VzACdOfJFncxh8cooghE3tIdc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/tr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RbNisbiWvkqWwj2C0LDgvQiLYUA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/vi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7KWOQQAFL0LBHlGYTuxqCzW1agQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/zh.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			socx43t1VI649J+QyCOEeZ/YOJQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/zh_Hant_HK.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VWqZ/8HYO+hnD4ncuigmcqdwTfo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/zh_Hant_TW.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rr2kvGmgmXEdbTLOH1CwY/7fGcc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Headers/FBSDKAEMManager.h</key>
		<data>
		Yj5aPKxlx+gRF70J6qlF3vXE4mk=
		</data>
		<key>Headers/FBSDKATEPublisherCreating.h</key>
		<data>
		HCKpYddhfbeOKPVi+PyxpQEl/Sw=
		</data>
		<key>Headers/FBSDKATEPublisherFactory.h</key>
		<data>
		o4BqZ5pce+E7bPPdIkkE30cwTuI=
		</data>
		<key>Headers/FBSDKAccessToken.h</key>
		<data>
		52NScPmZOyhPECDbdwVz3SgFfnU=
		</data>
		<key>Headers/FBSDKAccessTokenProviding.h</key>
		<data>
		8ssoFJOvhs7F3QcUOp076HcFlTA=
		</data>
		<key>Headers/FBSDKAdvertiserIDProviding.h</key>
		<data>
		spPSWUz3WKLY8u+9ZEUgThNQTMY=
		</data>
		<key>Headers/FBSDKAdvertisingTrackingStatus.h</key>
		<data>
		s2/tT+xSXPH4xXaQ+yW41JtgT58=
		</data>
		<key>Headers/FBSDKAppAvailabilityChecker.h</key>
		<data>
		Wyf9l4OPVlNw4rmgihSwsLLXekY=
		</data>
		<key>Headers/FBSDKAppEventDropDetermining.h</key>
		<data>
		kuHhyp+2GLTjun17XfcnCE5AhEU=
		</data>
		<key>Headers/FBSDKAppEventName.h</key>
		<data>
		BOUOaSiuKli6Qe/v5ySB3h+M5X4=
		</data>
		<key>Headers/FBSDKAppEventParameterName.h</key>
		<data>
		PUlvfFayxBDoZ9T09/HpoVZ7Hgc=
		</data>
		<key>Headers/FBSDKAppEventParameterProduct.h</key>
		<data>
		r+KeRcYNIPkXBrMbyUHT2wI7s9Y=
		</data>
		<key>Headers/FBSDKAppEventParameterValue.h</key>
		<data>
		040yhBlHKamIDlwNu0ImpgLTCqc=
		</data>
		<key>Headers/FBSDKAppEventParametersExtracting.h</key>
		<data>
		9pOtxV2/CHu04fcr39VsN+sfZ+s=
		</data>
		<key>Headers/FBSDKAppEventUserDataType.h</key>
		<data>
		A3lEI7gxtNx4AHoXWeE0s7u1zK8=
		</data>
		<key>Headers/FBSDKAppEvents.h</key>
		<data>
		lg573O7sH41xBg8lJrqEit3K/XQ=
		</data>
		<key>Headers/FBSDKAppEventsConfiguration.h</key>
		<data>
		HzthBYIOOFB3e2msa04M0rtBsRo=
		</data>
		<key>Headers/FBSDKAppEventsConfigurationManager.h</key>
		<data>
		ANbr0kXkjHZjFTN/QfFH+kdJmYI=
		</data>
		<key>Headers/FBSDKAppEventsConfigurationProtocol.h</key>
		<data>
		v6/aYc8HvZlvKvREulESWKamY/g=
		</data>
		<key>Headers/FBSDKAppEventsConfigurationProviding.h</key>
		<data>
		Q6UHSFjjvfBNsl6lIgbm+E/xdvg=
		</data>
		<key>Headers/FBSDKAppEventsConfiguring.h</key>
		<data>
		mHGiLeCpVuFdv3XqdFxv9vt7374=
		</data>
		<key>Headers/FBSDKAppEventsDeviceInfo.h</key>
		<data>
		SxQnYEiX9/yUxdAbzLI6d4eaegc=
		</data>
		<key>Headers/FBSDKAppEventsFlushBehavior.h</key>
		<data>
		tGoQfvz6uAy5DjnhPnzX/Ai2vl4=
		</data>
		<key>Headers/FBSDKAppEventsFlushReason.h</key>
		<data>
		CVXh0F8QRsx+pTFSV0t8r1QBBAQ=
		</data>
		<key>Headers/FBSDKAppEventsNotificationName.h</key>
		<data>
		y7c3PKWx/w77oSbeugClHIvTMS8=
		</data>
		<key>Headers/FBSDKAppEventsParameterProcessing.h</key>
		<data>
		lSEA6VeT3RQ/9OIKlCWKuogT/fc=
		</data>
		<key>Headers/FBSDKAppEventsReporter.h</key>
		<data>
		j2aR5673Kbc66xveJIq2h8rsKLk=
		</data>
		<key>Headers/FBSDKAppEventsState.h</key>
		<data>
		WuHkw89fZW/JsqsG7+MqdROIuqU=
		</data>
		<key>Headers/FBSDKAppEventsStateManager.h</key>
		<data>
		1npaFG2cN1/3fD1rD7gXkZmnUbI=
		</data>
		<key>Headers/FBSDKAppEventsStatePersisting.h</key>
		<data>
		oylwsOMHWTY2ag3rphOgwSAfz7c=
		</data>
		<key>Headers/FBSDKAppEventsStateProviding.h</key>
		<data>
		KzGafbBYik5zyGw1bm76HKS7Tc0=
		</data>
		<key>Headers/FBSDKAppEventsUtility.h</key>
		<data>
		5vrPi5Z7t//eIoz9u9sQrS88V8E=
		</data>
		<key>Headers/FBSDKAppEventsUtilityProtocol.h</key>
		<data>
		w++q3FMjWosIk0QoN6chRjfvng8=
		</data>
		<key>Headers/FBSDKAppLinkCreating.h</key>
		<data>
		nqhYr/0Yijl6YG6zjg69DkIZMdo=
		</data>
		<key>Headers/FBSDKAppLinkEventPosting.h</key>
		<data>
		Unlyk7QfK8RMiwI6wrySVV1IWP4=
		</data>
		<key>Headers/FBSDKAppLinkNavigationBlock.h</key>
		<data>
		1PU/0JKCKzJJV3POVzX50kAFA0o=
		</data>
		<key>Headers/FBSDKAppLinkNavigationType.h</key>
		<data>
		3fSnRUTotphysdDrdAvLMxLPW+k=
		</data>
		<key>Headers/FBSDKAppLinkProtocol.h</key>
		<data>
		FaTKQ3PKUnwjWoYivjhBcoWCvf8=
		</data>
		<key>Headers/FBSDKAppLinkResolverRequestBuilding.h</key>
		<data>
		SXBVCFpg1usB96KY2Km7m8BU4PQ=
		</data>
		<key>Headers/FBSDKAppLinkResolving.h</key>
		<data>
		vW0iP2TWioh9F2xVFhjb96AWH/M=
		</data>
		<key>Headers/FBSDKAppLinkTargetCreating.h</key>
		<data>
		CSjhIDvr2Fy0xL6BAuVxNcPFzdg=
		</data>
		<key>Headers/FBSDKAppLinkTargetProtocol.h</key>
		<data>
		KjpJOKaE7Hu4fZ/kcuexDfd7h/E=
		</data>
		<key>Headers/FBSDKAppLinkURL.h</key>
		<data>
		QygAk/iNI8C9zeo9e0YUgaIWY7g=
		</data>
		<key>Headers/FBSDKAppLinkURLCreating.h</key>
		<data>
		oN/+vlRMv1IvKkWrsh+VtDwu92U=
		</data>
		<key>Headers/FBSDKAppLinkURLKeys.h</key>
		<data>
		9Ke3NaIqqmJ0mx/UMz1Vw+/X9hA=
		</data>
		<key>Headers/FBSDKAppLinkUtility.h</key>
		<data>
		CSxGCvSd3TFNsqT26linPXGDWM4=
		</data>
		<key>Headers/FBSDKAppLinkVersion.h</key>
		<data>
		4lyPD97ZcXbRvhIigNtr3LGnzKo=
		</data>
		<key>Headers/FBSDKAppLinksBlock.h</key>
		<data>
		TPb6CeLZPTcgS7hT6GviE2JRz4k=
		</data>
		<key>Headers/FBSDKAppOperationalDataType.h</key>
		<data>
		Yxnb0SsotnkBXyJBdPIv4DE+uuE=
		</data>
		<key>Headers/FBSDKAppStoreReceiptProviding.h</key>
		<data>
		YAWUu9gn0hfRwdt2dscR4Pjncb0=
		</data>
		<key>Headers/FBSDKAppURLSchemeProviding.h</key>
		<data>
		G7H5ArEaw56tAdukKkeFnHJW3yM=
		</data>
		<key>Headers/FBSDKApplicationActivating.h</key>
		<data>
		oHcGo1IxNL4whFjkCAqY0Bezu1A=
		</data>
		<key>Headers/FBSDKApplicationLifecycleNotifications.h</key>
		<data>
		HZOemKjr7HIyL3ND764s1sIhRdA=
		</data>
		<key>Headers/FBSDKApplicationLifecycleObserving.h</key>
		<data>
		tM9oT+qXMwjCWeGffrZ/7pU9dhk=
		</data>
		<key>Headers/FBSDKApplicationObserving.h</key>
		<data>
		xJEwA88eDINbA4Kq2mRmWsoZVaE=
		</data>
		<key>Headers/FBSDKApplicationStateSetting.h</key>
		<data>
		fy5bOvCaeEMdMssTZs5062vJuLs=
		</data>
		<key>Headers/FBSDKAuthenticationStatusUtility.h</key>
		<data>
		NzmrmWDBawgvUzFKUTG4Zq+D8PQ=
		</data>
		<key>Headers/FBSDKAuthenticationToken.h</key>
		<data>
		Hbt4TrpEQ1E7m0cPJVPJrvfOMYE=
		</data>
		<key>Headers/FBSDKAuthenticationTokenProviding.h</key>
		<data>
		zDTRRbEFRongbmR9iUSO35PCbhs=
		</data>
		<key>Headers/FBSDKAutoSetup.h</key>
		<data>
		ZrZsPjcuQHmtIb0VA/2JxYjo0Bk=
		</data>
		<key>Headers/FBSDKBridgeAPIProtocol.h</key>
		<data>
		tLjX3wSZTxU3/edMWI3RtKPsv+A=
		</data>
		<key>Headers/FBSDKBridgeAPIProtocolType.h</key>
		<data>
		DblZg5KWlfoY7uhozPWbR8A+C+Q=
		</data>
		<key>Headers/FBSDKBridgeAPIRequest.h</key>
		<data>
		9p88MKTDxjMSnhRmD3tBUBpyKP0=
		</data>
		<key>Headers/FBSDKBridgeAPIRequestCreating.h</key>
		<data>
		fSrgRjXiEf48iO5SW4tUzCK4Dfw=
		</data>
		<key>Headers/FBSDKBridgeAPIRequestOpening.h</key>
		<data>
		0yprixA39qr6F++QUQmDaS+kI5c=
		</data>
		<key>Headers/FBSDKBridgeAPIRequestProtocol.h</key>
		<data>
		QvIJ69YhObUzXyvQTDogepJbLIY=
		</data>
		<key>Headers/FBSDKBridgeAPIResponse.h</key>
		<data>
		0xHREU5UN9V2806Plbv9YnZCtvo=
		</data>
		<key>Headers/FBSDKButton.h</key>
		<data>
		nRauGR6uRcDNSlw5mNra+SReMhI=
		</data>
		<key>Headers/FBSDKButtonImpressionLogging.h</key>
		<data>
		LsVpQhR6smaHCPnF/ITEWiKGkKA=
		</data>
		<key>Headers/FBSDKClientTokenProviding.h</key>
		<data>
		W+JNLAn4YexFixxEQGR3Q9RH0KI=
		</data>
		<key>Headers/FBSDKCodelessIndexer.h</key>
		<data>
		ua4gDNRjw0KAFXM4vqI1AxIhmgQ=
		</data>
		<key>Headers/FBSDKCodelessIndexing.h</key>
		<data>
		vvo1NJ1mTuIU5ZOEX1L9qWcyo+s=
		</data>
		<key>Headers/FBSDKConstants.h</key>
		<data>
		D8KfnDhhBY1wExKSXz3swq16lTE=
		</data>
		<key>Headers/FBSDKContainerViewController.h</key>
		<data>
		U4pg9nowPmFOUC9fvIpLijlN6fg=
		</data>
		<key>Headers/FBSDKConversionValueUpdating.h</key>
		<data>
		fTxcQJg2+ZGuh6rqfJoDG5p6L94=
		</data>
		<key>Headers/FBSDKCoreKit-Swift.h</key>
		<data>
		7600QDNflQgHY+mG/cwStJOsec8=
		</data>
		<key>Headers/FBSDKCoreKit.h</key>
		<data>
		9NSBO9571MvtFn98up/7ePg4lwo=
		</data>
		<key>Headers/FBSDKCoreKitVersions.h</key>
		<data>
		/eqaMAzP93B16wXvakv2GDTKX0k=
		</data>
		<key>Headers/FBSDKCrashObserver.h</key>
		<data>
		i5LsrVb8OhFQsCQv02NggyWl4qk=
		</data>
		<key>Headers/FBSDKCrashShield.h</key>
		<data>
		488qfAna0UU+EgR6ICrzMNPuE+Y=
		</data>
		<key>Headers/FBSDKDataProcessingOptionKey.h</key>
		<data>
		2KTIgp4tO9oDInYHDKE4PkSZtuo=
		</data>
		<key>Headers/FBSDKDeviceInformationProviding.h</key>
		<data>
		AhMeLL4pfNP6NOlehkTmCGd908I=
		</data>
		<key>Headers/FBSDKDialogConfiguration.h</key>
		<data>
		BkHb91/H2f5w4nnsjzWvACP+LO8=
		</data>
		<key>Headers/FBSDKDialogConfigurationMapBuilding.h</key>
		<data>
		3JigPqy5HGPxZ1EfvLl6K2r09G4=
		</data>
		<key>Headers/FBSDKDomainConfiguration.h</key>
		<data>
		z42cVELCq+08IS97csqrsv0G3UM=
		</data>
		<key>Headers/FBSDKDomainConfigurationManager.h</key>
		<data>
		vhxTjwLkFlXSayMOST5Du3FF1Ns=
		</data>
		<key>Headers/FBSDKDomainConfigurationProviding.h</key>
		<data>
		bAZKBepNb9Jwher3IYkkHIK+jM4=
		</data>
		<key>Headers/FBSDKDomainHandler.h</key>
		<data>
		4pB423j54rXYv0k1XCxquIvgGII=
		</data>
		<key>Headers/FBSDKDynamicFrameworkLoaderProxy.h</key>
		<data>
		gQze+1wXFmg8HHDE0Ba4/AdlSbk=
		</data>
		<key>Headers/FBSDKErrorConfiguration.h</key>
		<data>
		t9pebtrwIlsvol930VzK/Afs/8E=
		</data>
		<key>Headers/FBSDKErrorConfigurationProtocol.h</key>
		<data>
		rWzO75a4AGVSBUj9usd195pV1pw=
		</data>
		<key>Headers/FBSDKErrorConfigurationProvider.h</key>
		<data>
		/NYL7EwesbS1Jj4zI5j/M36RrP4=
		</data>
		<key>Headers/FBSDKErrorConfigurationProviding.h</key>
		<data>
		8oEeLY1N7YRaIXucUhviYIeCeUA=
		</data>
		<key>Headers/FBSDKErrorCreating.h</key>
		<data>
		hSTHauBFdEYzYLgpazD8Nu2mbvA=
		</data>
		<key>Headers/FBSDKErrorRecoveryAttempting.h</key>
		<data>
		woJpc0K7/L9yeXE4C+9IxIUoyNo=
		</data>
		<key>Headers/FBSDKErrorRecoveryConfiguration.h</key>
		<data>
		ycgx9i+ACoXgcR7EKbYFYCxzlOg=
		</data>
		<key>Headers/FBSDKErrorReporter.h</key>
		<data>
		mr/DzUsbnLVLYMNNABg7aaa45vw=
		</data>
		<key>Headers/FBSDKErrorReporting.h</key>
		<data>
		QPo2mKvh+gBY7bUYxLIRori3P84=
		</data>
		<key>Headers/FBSDKEventLogging.h</key>
		<data>
		DUSe3ilZQO8BqMsmrVYsaoF7LGA=
		</data>
		<key>Headers/FBSDKEventProcessing.h</key>
		<data>
		gp3AdngU5TgiopBE4rJMoR6ANSg=
		</data>
		<key>Headers/FBSDKEventsProcessing.h</key>
		<data>
		O75xhVmbjtVsiUJUjeGE1r788nM=
		</data>
		<key>Headers/FBSDKFeature.h</key>
		<data>
		Oh4B8Z0ZOzOtzlb70Z4TPxT1NQg=
		</data>
		<key>Headers/FBSDKFeatureChecking.h</key>
		<data>
		rYTkx84W03mL0rrno4sthw6poiM=
		</data>
		<key>Headers/FBSDKFeatureDisabling.h</key>
		<data>
		5RTMQMD5BH2IHczkFRDwcDa6YEc=
		</data>
		<key>Headers/FBSDKFeatureExtracting.h</key>
		<data>
		rLjAEFGlg1SFdt3LKE/Vyvpy4C4=
		</data>
		<key>Headers/FBSDKFeatureExtractor.h</key>
		<data>
		UpeejK9e6KddzNj4pJ/187IOAok=
		</data>
		<key>Headers/FBSDKGateKeeperManager.h</key>
		<data>
		UApODVoVBgYDmemuf8pq5KhSZ2A=
		</data>
		<key>Headers/FBSDKGateKeeperManaging.h</key>
		<data>
		MZUyM6YX2TAfccBQg6xiTBypZ18=
		</data>
		<key>Headers/FBSDKGraphErrorRecoveryProcessor.h</key>
		<data>
		aNj/uzA6QMePztxIxRll77xHLIg=
		</data>
		<key>Headers/FBSDKGraphRequest.h</key>
		<data>
		W0+x27i8eT+6EMZ3lVcQXPKsAP0=
		</data>
		<key>Headers/FBSDKGraphRequestConnecting.h</key>
		<data>
		sAlo7za+Sf4zj8Xg+Wy83BUtRCE=
		</data>
		<key>Headers/FBSDKGraphRequestConnection.h</key>
		<data>
		NUnPpwpwUkVhUBU67o81VFaERfo=
		</data>
		<key>Headers/FBSDKGraphRequestConnectionDelegate.h</key>
		<data>
		FSHiVcHDpJTlfdfBczQNHtjDJ8s=
		</data>
		<key>Headers/FBSDKGraphRequestConnectionFactory.h</key>
		<data>
		gAfT3DO/vuTTlEaJPEP8hL5P3Eo=
		</data>
		<key>Headers/FBSDKGraphRequestConnectionFactoryProtocol.h</key>
		<data>
		A26a5H79Zb1dRO6YHMFB4DbS+D8=
		</data>
		<key>Headers/FBSDKGraphRequestDataAttachment.h</key>
		<data>
		7vvCqPiZp4o6JKVaJJ4FP9XkXKE=
		</data>
		<key>Headers/FBSDKGraphRequestFactory.h</key>
		<data>
		lAwX1CKv5VHiJ07/xZZylICOdg4=
		</data>
		<key>Headers/FBSDKGraphRequestFactoryProtocol.h</key>
		<data>
		ydipmJZDsiXmT3/5DNEiX0gzsNs=
		</data>
		<key>Headers/FBSDKGraphRequestFlags.h</key>
		<data>
		Zas2ccUoNaCrjUffAdLC6TmKLWs=
		</data>
		<key>Headers/FBSDKGraphRequestHTTPMethod.h</key>
		<data>
		sF4WT7ko2ZXuQ91thBewwSb29Cc=
		</data>
		<key>Headers/FBSDKGraphRequestMetadata.h</key>
		<data>
		4ZYoDzErnijlAw/YdD1e2qQ3eDs=
		</data>
		<key>Headers/FBSDKGraphRequestPiggybackManaging.h</key>
		<data>
		RzvPVhBs2lDl2xH7PANCNXkswIE=
		</data>
		<key>Headers/FBSDKGraphRequestProtocol.h</key>
		<data>
		2WftYLHKj8D0QUQgx2F/+tCFX3k=
		</data>
		<key>Headers/FBSDKGraphRequestQueue.h</key>
		<data>
		YazdAJ0rcrHmvRHvxaduooEWglg=
		</data>
		<key>Headers/FBSDKIAPDedupeProcessing.h</key>
		<data>
		jFfQ2uToisxlUwu5HAehBwtT5yc=
		</data>
		<key>Headers/FBSDKIAPFailedTransactionLogging.h</key>
		<data>
		3naly4poGndtl04IKJLRhIn0y58=
		</data>
		<key>Headers/FBSDKIAPFailedTransactionLoggingCreating.h</key>
		<data>
		8f1KPldp/xKn7SdJ+extn1OcPWY=
		</data>
		<key>Headers/FBSDKIAPTransactionCaching.h</key>
		<data>
		EUOfrTb9ijRfv++ZOI1VjUF46+o=
		</data>
		<key>Headers/FBSDKImpressionLoggerFactory.h</key>
		<data>
		nfa97DKHYMz7v0+pUDy3nlxrUYk=
		</data>
		<key>Headers/FBSDKImpressionLoggerFactoryProtocol.h</key>
		<data>
		3LEI96v+5GIEFysj3D1C/rG8ihw=
		</data>
		<key>Headers/FBSDKImpressionLogging.h</key>
		<data>
		wrrEayG2/hN5wUceRYRAU8HrXBk=
		</data>
		<key>Headers/FBSDKImpressionLoggingButton.h</key>
		<data>
		5jJQoQEgcqekG5Zl8U42hb9oI80=
		</data>
		<key>Headers/FBSDKInstrumentManager.h</key>
		<data>
		b4LuHg6PamZ7IBBxVoOYpxuYERM=
		</data>
		<key>Headers/FBSDKIntegrityParametersProcessorProvider.h</key>
		<data>
		YAejrtcnEaVtGKUEB4ZJpyeATvQ=
		</data>
		<key>Headers/FBSDKIntegrityProcessing.h</key>
		<data>
		87ZiOpw4U2tli0OR6o2FOZV0Szc=
		</data>
		<key>Headers/FBSDKInternalURLOpener.h</key>
		<data>
		FLV0q7bfHv0++Xz2yX93rch6WQk=
		</data>
		<key>Headers/FBSDKInternalUtility.h</key>
		<data>
		xVnxfL7eTB7Crbqhijh4imgTplo=
		</data>
		<key>Headers/FBSDKInternalUtilityProtocol.h</key>
		<data>
		/ZcaAKHHVRGsTO5fkpWQKXtwRlg=
		</data>
		<key>Headers/FBSDKKeychainStore.h</key>
		<data>
		VSfBFlzguwgAJhVey77PM3TiKzI=
		</data>
		<key>Headers/FBSDKKeychainStoreProtocol.h</key>
		<data>
		Vl4nIrUwT7cqcjwlXymbPkKUVDo=
		</data>
		<key>Headers/FBSDKKeychainStoreProviding.h</key>
		<data>
		u2GnomfI+oHCeoe6Ei9pG5quwVE=
		</data>
		<key>Headers/FBSDKLocation.h</key>
		<data>
		lVldFN//gmPckkWOntm6/lMe0QE=
		</data>
		<key>Headers/FBSDKLogger.h</key>
		<data>
		19xKJkbTRpwMoQwzaC6Nu+w5tME=
		</data>
		<key>Headers/FBSDKLoggerFactory.h</key>
		<data>
		oN6VhKse4/3rPEKNFGojETNkg58=
		</data>
		<key>Headers/FBSDKLogging.h</key>
		<data>
		/DbryGZcqEQACAktvCjPjV6SDG4=
		</data>
		<key>Headers/FBSDKLoggingBehavior.h</key>
		<data>
		lNVc//dYNZlhHrZHLByspMT+TeY=
		</data>
		<key>Headers/FBSDKLoggingNotifying.h</key>
		<data>
		ClxijYzHweoVytnlGNbT1n62Q2I=
		</data>
		<key>Headers/FBSDKLoginTooltip.h</key>
		<data>
		t9qlwGoUeyWhxDfXE8Ky6RnF/gg=
		</data>
		<key>Headers/FBSDKMacCatalystDetermining.h</key>
		<data>
		Fy2901RMaq8v1KZ1nkSvVuhYSN8=
		</data>
		<key>Headers/FBSDKMath.h</key>
		<data>
		rYMaHGq5qMdO0PMhIT/NeaWQMFE=
		</data>
		<key>Headers/FBSDKMeasurementEventListener.h</key>
		<data>
		OVZSC/JIKFj+KgzAwt5biOqF7rI=
		</data>
		<key>Headers/FBSDKMeasurementEventNames.h</key>
		<data>
		9XUuzXpHXt6PyqsDNvqKaCYOsaQ=
		</data>
		<key>Headers/FBSDKMetadataIndexer.h</key>
		<data>
		yvEMxozxtAwiiPOoouj17MOx38w=
		</data>
		<key>Headers/FBSDKMetadataIndexing.h</key>
		<data>
		0XbHu0BpTUjfkrPvYn1B9+H0Bww=
		</data>
		<key>Headers/FBSDKModelManager.h</key>
		<data>
		UTvH2kBUyG0vy9bmR5tucIdzDMU=
		</data>
		<key>Headers/FBSDKMutableCopying.h</key>
		<data>
		CdAKmAi79FHfugMUCBcou38XjyY=
		</data>
		<key>Headers/FBSDKNetworkErrorChecker.h</key>
		<data>
		lc4ltIsnGN0wefVKZeW3BTQqt8o=
		</data>
		<key>Headers/FBSDKNetworkErrorChecking.h</key>
		<data>
		DQOOpk+tae6sTARv6zgYkUNQv+4=
		</data>
		<key>Headers/FBSDKObjectDecoding.h</key>
		<data>
		rsoqTN959edBc1dWsOJAk4SFajc=
		</data>
		<key>Headers/FBSDKOperatingSystemVersionComparing.h</key>
		<data>
		jd/kWOWeuMfcK4hF/tgU6AeS2yU=
		</data>
		<key>Headers/FBSDKPasteboard.h</key>
		<data>
		4+oJKTiDHC9gezS8CCuhdWHO2gs=
		</data>
		<key>Headers/FBSDKPaymentObserving.h</key>
		<data>
		2F/w1aia/WcWnKwAGffiRgYimWQ=
		</data>
		<key>Headers/FBSDKPaymentProductRequestor.h</key>
		<data>
		ZQ6Btyy0z7ZiD/M9eX+7+KDRiLE=
		</data>
		<key>Headers/FBSDKPaymentProductRequestorCreating.h</key>
		<data>
		kofChLuS6UMw2biE8Lcu2yhsmt0=
		</data>
		<key>Headers/FBSDKProductAvailability.h</key>
		<data>
		4z6lAOLiyG+H6sMmuDzBXlrBO4Q=
		</data>
		<key>Headers/FBSDKProductCondition.h</key>
		<data>
		p2M86R+0XjuIIHBALGh4qHhF0sg=
		</data>
		<key>Headers/FBSDKProductRequestFactory.h</key>
		<data>
		7BIaz0Ab6i62/Dq0UaZpdoNPhCs=
		</data>
		<key>Headers/FBSDKProductsRequestProtocols.h</key>
		<data>
		quFgypTeZ+2XNvJr6deEaYceClo=
		</data>
		<key>Headers/FBSDKProfileBlock.h</key>
		<data>
		m3b2HY43bVO+k7tLfgTUHlel3XM=
		</data>
		<key>Headers/FBSDKProfileNotifications.h</key>
		<data>
		p/v7YsyAewf4Zx5PgVOw0g9689k=
		</data>
		<key>Headers/FBSDKRandom.h</key>
		<data>
		rqrUnE5WdNwaBw/Fm/Sauc2KceA=
		</data>
		<key>Headers/FBSDKRestrictiveDataFilterManager.h</key>
		<data>
		FhBIBCgefnNx6Hx9hBvjEQxlgTk=
		</data>
		<key>Headers/FBSDKRulesFromKeyProvider.h</key>
		<data>
		9/nqsZZ8Epe4NBnTPqEOptCNdzs=
		</data>
		<key>Headers/FBSDKSKAdNetworkReporter.h</key>
		<data>
		N8+fSt031r3BemNKfykFVGaMU2g=
		</data>
		<key>Headers/FBSDKSKAdNetworkReporterV2.h</key>
		<data>
		t1JQiDuyttg76dcS901/oxfu6tQ=
		</data>
		<key>Headers/FBSDKServerConfiguration.h</key>
		<data>
		ePai1Ct6rcU1fbdNFDdAFZgZLYo=
		</data>
		<key>Headers/FBSDKServerConfigurationManager.h</key>
		<data>
		MabAusZdKvvZ5Ny2wrhRTzvzRhY=
		</data>
		<key>Headers/FBSDKServerConfigurationProviding.h</key>
		<data>
		OVcy7K8/6LpbY7Ls9wBkMeiLOXc=
		</data>
		<key>Headers/FBSDKSettingsLogging.h</key>
		<data>
		j4NKiO1um7BzI27sPShA+WNNV6E=
		</data>
		<key>Headers/FBSDKSourceApplicationTracking.h</key>
		<data>
		eyc/NF7kaz05PnojBKao9RoOkXo=
		</data>
		<key>Headers/FBSDKSuggestedEventsIndexer.h</key>
		<data>
		eLTHBeEjKRaaEKDEDTrfz5o1+pE=
		</data>
		<key>Headers/FBSDKSuggestedEventsIndexerProtocol.h</key>
		<data>
		Nlpu1MobSLVgHiTs61VhpCb8F84=
		</data>
		<key>Headers/FBSDKSwizzler.h</key>
		<data>
		PgutzEuZyjT9se/U24OTeoEMo1g=
		</data>
		<key>Headers/FBSDKSwizzling.h</key>
		<data>
		jMRYakKevWrqsOerYuQ9Fb1eCT4=
		</data>
		<key>Headers/FBSDKTimeSpentData.h</key>
		<data>
		Zgill5e+tNHLsGRpZ8nXHCk2iEc=
		</data>
		<key>Headers/FBSDKTimeSpentRecording.h</key>
		<data>
		NuI7oI0R+b4/s+KiUbS+MEn4x8k=
		</data>
		<key>Headers/FBSDKTokenCaching.h</key>
		<data>
		cla0KYAtpSCy2GcXC++XdSQrxck=
		</data>
		<key>Headers/FBSDKTokenStringProviding.h</key>
		<data>
		43wQbjzLSB+cSu9hTQZ4tJq24Mc=
		</data>
		<key>Headers/FBSDKTransactionObserving.h</key>
		<data>
		lZIW9x0Pm8LlHaUourw8qilnS7I=
		</data>
		<key>Headers/FBSDKTransformer.h</key>
		<data>
		Ui2GFPACS7T6kK9LcCLcdJyCYyo=
		</data>
		<key>Headers/FBSDKURL.h</key>
		<data>
		lg+hfhk8xZ7goFdbGZ8cVtCyNbw=
		</data>
		<key>Headers/FBSDKURLHosting.h</key>
		<data>
		O+RAgM4Uu49yrzKRLeqtv9FfVKE=
		</data>
		<key>Headers/FBSDKURLOpener.h</key>
		<data>
		/9/EmnpZWM6KdVyGGEI1NUvMbYk=
		</data>
		<key>Headers/FBSDKURLOpening.h</key>
		<data>
		0r8abqnzwBXvpV5PlCa/kQ+OdW8=
		</data>
		<key>Headers/FBSDKURLScheme.h</key>
		<data>
		36HfFNYLwWfRajDYFDJeNZe/evc=
		</data>
		<key>Headers/FBSDKURLSessionProxyFactory.h</key>
		<data>
		fkszrJOzG1OgfuDotJLKT74xw1w=
		</data>
		<key>Headers/FBSDKURLSessionProxyProviding.h</key>
		<data>
		5Hely+G3LbaFH81TEA3aS8tH8Xk=
		</data>
		<key>Headers/FBSDKURLSessionProxying.h</key>
		<data>
		LIrnTeexPfrk+a2pllxOcLBqw+Q=
		</data>
		<key>Headers/FBSDKUnarchiverProvider.h</key>
		<data>
		mBxizULPLClFmFkzGvq7DZbedIU=
		</data>
		<key>Headers/FBSDKUnarchiverProviding.h</key>
		<data>
		PU6uhik4cqPkhqWAmJpctYAS+vI=
		</data>
		<key>Headers/FBSDKUserAgeRange.h</key>
		<data>
		paq4PxztwLvJ2EQKujqXLJfgjiA=
		</data>
		<key>Headers/FBSDKUserDataPersisting.h</key>
		<data>
		eiIDv79NpGtVgJHZ+jovQsS4ZsU=
		</data>
		<key>Headers/FBSDKUserDataStore.h</key>
		<data>
		iSw84njE3Q4fckPhhhUCpEMT4Uw=
		</data>
		<key>Headers/FBSDKUserIDProviding.h</key>
		<data>
		1G3kOunQeQMavUujt30qZv1Oj98=
		</data>
		<key>Headers/FBSDKUserIdentifier.h</key>
		<data>
		EqrAGtLbcJRfQwl12songIdMMEE=
		</data>
		<key>Headers/FBSDKUtility.h</key>
		<data>
		ACK+e48w6WLwZDhZT9VIaXDWTlk=
		</data>
		<key>Headers/FBSDKWebDialogDelegate.h</key>
		<data>
		oPLwzJ7KUTr8T5hq/c983EM1rfE=
		</data>
		<key>Headers/FBSDKWebDialogView.h</key>
		<data>
		EHOQKin9zYzG+gmc/LfBegKrvEE=
		</data>
		<key>Headers/FBSDKWebView.h</key>
		<data>
		jUhFO4/5Ly1VNml/cAryOayE4Ag=
		</data>
		<key>Headers/FBSDKWebViewAppLinkResolver.h</key>
		<data>
		jEC9UH9Inm7DYqoFZv2qaN3Pe14=
		</data>
		<key>Headers/FBSDKWebViewProviding.h</key>
		<data>
		6dBqgFJpP3qYvCEw7KHe/d5ieC8=
		</data>
		<key>Headers/NSNotificationCenter+NotificationPosting.h</key>
		<data>
		4IqhFgtQjABnwxNo0vP4+5Q/hSU=
		</data>
		<key>Headers/NSProcessInfo+MacCatalystDetermining.h</key>
		<data>
		P2PZ0k71jzSsWpQg6vH4TOHr6SU=
		</data>
		<key>Headers/NSProcessInfo+OperatingSystemVersionComparing.h</key>
		<data>
		7p95y3GgCy4C7g+8xfGVXW4sr18=
		</data>
		<key>Headers/UIApplication+URLOpener.h</key>
		<data>
		N6kxbbjqI4XB8O7cVqNSb4VtaOs=
		</data>
		<key>Headers/UIPasteboard+Pasteboard.h</key>
		<data>
		MKDA9GrF2o6+h3tbW9e9adSk23k=
		</data>
		<key>Headers/WKWebView+WebViewProtocol.h</key>
		<data>
		9AxT3+g6DPZx5q3rQWhRBMFJu64=
		</data>
		<key>Headers/_FBSDKNotificationPosting.h</key>
		<data>
		w1Gpzlc2q889xvFT3+ffPk2kwQU=
		</data>
		<key>Headers/_FBSDKWindowFinding.h</key>
		<data>
		Gac9mAAYHny41SRhpW53CbfSo2s=
		</data>
		<key>Headers/__FBSDKLoggerCreating.h</key>
		<data>
		TTkFW5xuSEAOVq8O1cbEJ6/RgQg=
		</data>
		<key>Info.plist</key>
		<data>
		ioQQll/iBFQ7MfZ5k8S1w05OKS0=
		</data>
		<key>Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		OkoPZtcS5lu+Hk/2Cmm3NkUC1LA=
		</data>
		<key>Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		o8MMQ2RxBsTwTAF4vAwFP1JbYWY=
		</data>
		<key>Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		CSmfnAungUw15mQMizDDGTV7ElY=
		</data>
		<key>Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		o8MMQ2RxBsTwTAF4vAwFP1JbYWY=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		130ouJhMpZj1Z376F5gsUfoyxyc=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		+4xzw+xvpntuK5AhSisGTSzsudg=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>FacebookSDKStrings.bundle/Resources/af.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			aCtbnATpHK8Vi9pb9hBnXBBY6+zqsfeZF+F4H8aujwc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ar.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			s1aJgl1DJYHZsgUSZhMYINgeVIIi1NDjhZ3KkkA6SHY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/bn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RYmfdmkMCNbGahvM9NYcmYiySFg8EvXZ2GUMXlSBWN8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/cs.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			JIHHymPdIttC0qC3iZx89N8HLHiCT2sFHJ7U09zZQzg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/da.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			rQLYTaF5jkBlY9WlSinHBEtLGJwU8VJyBAXbMOS1FBw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/de.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jaRdBaX5SEMhMJHA4X7DSIX/mjEKUdH09tKg+e1jkuU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/el.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KH/h3KcObMmRfS3vpIMRtbDX/tuUrUrwt3agnfZKKf4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/en.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			eNYbN/zs5JhZzRo97ltQFilMcvfV7MUlGzmUK8JCOK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/en_GB.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Ehn5jlEk9nHkql1/teEwOXPdlmM0yqLQMkVq95W1lU0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/es.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			X1CMNL7b4eteT/mu2U4kGGxt77m8h6i9IZlZ+3aXBMc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/es_ES.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			AWSgHyoVXbBxs9n2Ty2XM6gGClpTvrWQheR7USy3aHo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/fi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			n0r9aE/uabG+yQ+CTicKtVH2R8NXpmfBfW3pEneb2Gc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/fil.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			SrYgrW5xGmEgfJvEg2BMqfBsRxYa1koP9r1J1lyMd7A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/fr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			5puGnwqyUU34y4BDDWnmrqL9cbHAosqA27c/VZvrQlE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/gu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jDOx6ZOVtvUbyWYwon1C1cRzUnAY3r3JmiFTNgJd30M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/he.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Fg41Nq6D6Yg8CCJhCwqZd3eMLjs/NepPFMy2rB2A+vg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/hi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			7MIeteCKjPKu7GszF+BrRJqpVxduoAXlg6drRpQPceQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/hr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			iD8FRdiUx28qJEFvtvyQRbOWzsTPikwXwrOEMF5pAWw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/hu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GtWEiES81lZil10Skylfrn88mjj2e3NcvieRmzh43Qw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/id.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Eq9a4nxlLu+lwpJLYXEf46sjwQW6bi3N3y7N8UycWNQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/it.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			kDpMVTknyBjiPj2EtWrRNdRGBZtmXRVPM5ldzU+2X9g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ja.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			9xk36xdQPl2SRgXbWGMBxwjjzkVXUxtiYtkidSYwVNQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/kn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8NH+f4tH0tzd5k5LyfZJ14DN64SkSURQ5CfyFJf/Gmc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ko.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			C2sb15n2T4Ad64dDZGVBPhfJ+aew0h2WCmSN6SX8IjQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ml.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			gHIBTSCHftU0BQWZOGcD5td1wykq7tQA+mKClgUkYHM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/mr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fn0JKkKO/dH0bVur9VfzWntl8ls0yoY8YjgvYr/XsLw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ms.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nNTZclYH1fxv1+Mch6eJbzgnzbwYRtH9f9Vtu+Le3dk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/nb.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			QSYAJw601nnnqakR434bP6yeMjFPWZsx8ypSv68A13A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/nl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			yNnGqC//Orb49RDL1a0nOau+xDgkzinH4LUqhsqqkEQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/pa.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			bRVgGo+3BK4SwH5mPgjOjwn/mV3H6X+N2aAccxRdrdY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/pl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hEObnVN8x0e7V7SEKVMLd2+36sqDjInp/lw2GBlVDv8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/pt.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fr0opMxcbe0rfDIeWfWEevhQ3S5ydVBvEE5yPFYhBw8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/pt_PT.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			meQ3C7YBEw7vLepXd6Ck7W8869A1xgLgptLl84sgKhk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ru.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			tLbG0XkAh2lBT62Y0q08V7HUq1yB/QWlDKQ66pvXAtU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/sk.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			pNDFOu2O93RtAOzuyC4q2d8DP6wZuffCiHfxEIolnpU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/sv.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4HoEVPvwpF9IR063NtuNj8j9VEshT8JjvyTRGfaZbKw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/ta.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			l7aWKjZkBRjMSzn2T6GE21BMAi7DPKsf48+MQ/itkbo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/te.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			71qAVzkjqD6kmUs/nmYSfTlJv1FVVG9R4MKcNw3We4k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/th.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Gt+X0AF2SJdujyvtLTzRvcWYWA6VH8PZRE0EcpMy0Ws=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/tr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			MbpGbjBd1dHuUbE6jB8W1a+0xEj5ODzVz6yaQtCidqk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/vi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3KTxqBqodalyJ3jdjQwlZ+RPQbYNJJHRWjXgy1lJSyg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/zh.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			eXBsqwueqa9PpbpzHtzpzN3ztNOYTwY4vCTqsUFhQC0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/zh_Hant_HK.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			pnbR7cjq5w2nZG17PXaHRT+19mzFJ44tIEnJCoUqrvo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FacebookSDKStrings.bundle/Resources/zh_Hant_TW.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			2XHa9tLW3cf0L3byDYr/+6qjFMSrS3i+xwESUxC9pZc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Headers/FBSDKAEMManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RRWu3ofQe/GuFslThfnBEdL+ks4YVUnKAckD+t6Kw78=
			</data>
		</dict>
		<key>Headers/FBSDKATEPublisherCreating.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4DAoEwvpFC6T6PC5EJZwpa4Ey+A8LZo6rz/k/078BUM=
			</data>
		</dict>
		<key>Headers/FBSDKATEPublisherFactory.h</key>
		<dict>
			<key>hash2</key>
			<data>
			cW1M/vPKiBNWmJ+XzS8iFtLl9s1+5ahOBCQWQ33lFfA=
			</data>
		</dict>
		<key>Headers/FBSDKAccessToken.h</key>
		<dict>
			<key>hash2</key>
			<data>
			oFHJEVxEJrVA9Y7bNOjGQZ28+CrbY+rbVTIA+GC3LWs=
			</data>
		</dict>
		<key>Headers/FBSDKAccessTokenProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ttnRwTC/lx3T/wwxDPI/n0tOC8tMavvDSKnOHYtwmJ0=
			</data>
		</dict>
		<key>Headers/FBSDKAdvertiserIDProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			3TS/lxGjmXQaqCF4fgbFoM/w3nXircZe7UfQRibtXh0=
			</data>
		</dict>
		<key>Headers/FBSDKAdvertisingTrackingStatus.h</key>
		<dict>
			<key>hash2</key>
			<data>
			pj5HBFKU2AJRVkryxLDxsNyV+Hq0vhsL7ESLeXA7gco=
			</data>
		</dict>
		<key>Headers/FBSDKAppAvailabilityChecker.h</key>
		<dict>
			<key>hash2</key>
			<data>
			WCKAfRQSLZ76amGNcy7D85Zr0FqbK3yqgD2x9Q2KMVc=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventDropDetermining.h</key>
		<dict>
			<key>hash2</key>
			<data>
			L9oIxJipjM/MQk2hCZ2frITY+t5sCEXfXwbMQZJm5P4=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventName.h</key>
		<dict>
			<key>hash2</key>
			<data>
			gkInDcjiBNASc1T6fLvTisSC622V3ONdX5cFYRNhq4Q=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventParameterName.h</key>
		<dict>
			<key>hash2</key>
			<data>
			dourp/NLYssFkQ5Dg6AxyxLObwl5JhALDaFz6lFf9Lc=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventParameterProduct.h</key>
		<dict>
			<key>hash2</key>
			<data>
			FIPXmw+JMv7bBSbF0zhfVC2Ib03Sx9JYrwjlNp1XInI=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventParameterValue.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Q2olYJJI/DN976h566Nwy3D+obhtAQAKHOJ5lKeTfm8=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventParametersExtracting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			BdY6x122D6MPzxunayL68o6siMnk1pbG9+SIttD3Er0=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventUserDataType.h</key>
		<dict>
			<key>hash2</key>
			<data>
			7pjsRgcXBeV8tJeLjrQvQ/3ZBmzY9k086Z46TsArMag=
			</data>
		</dict>
		<key>Headers/FBSDKAppEvents.h</key>
		<dict>
			<key>hash2</key>
			<data>
			UXJezVQ0cuGeeCKQaxUnK81y4yXJT55f4K9dAPNv5/A=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsConfiguration.h</key>
		<dict>
			<key>hash2</key>
			<data>
			CPd+ImdtELN8bwE7ctjAasJyW7VT89Q5RQaUxTFXz6M=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsConfigurationManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			9Hd2vCTkBhdhGl2O0GrDnmf5Dk+9LGUCubPXDNOSj9M=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsConfigurationProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HNsLJH7DmlMk6E5VETGgJDTdrBxFh1tmIqN2HupuauQ=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsConfigurationProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Sxr+A8kVMuZThRbBh4I4DH8boozZFBrhAwev32Vh5Hw=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsConfiguring.h</key>
		<dict>
			<key>hash2</key>
			<data>
			CYre/9YuQpKmC/OI/41C1Pu8lysIfIKLoct7rQEcEdc=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsDeviceInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			c8TYlTd79b+8iKRVt8ssLF77TZo5fhxvf5ChNBNfMhc=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsFlushBehavior.h</key>
		<dict>
			<key>hash2</key>
			<data>
			iUxqEKL4pmF7f47Qul2Oe8QI0MjDPnOn3VhWjVQWe90=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsFlushReason.h</key>
		<dict>
			<key>hash2</key>
			<data>
			z4aL5e8KYk3I9292BbvFVfL7VvGSXi8r1ULbNY9qC+o=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsNotificationName.h</key>
		<dict>
			<key>hash2</key>
			<data>
			7JmzpHhHPCXS4WcGYrhN2g1u5YXUgR/ltWdRyfv8l0I=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsParameterProcessing.h</key>
		<dict>
			<key>hash2</key>
			<data>
			pCHyiWkvDhXpHUHpcTQYsXWk6DNMxor1yUXbLb1zJoM=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsReporter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			wg1FOXDGXyIdKy7bYj+hrqcKxtTG/paGmTO6QAjIyZ0=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsState.h</key>
		<dict>
			<key>hash2</key>
			<data>
			R6QD42bulvhwFoDu6RdyZBgBVdXMRl4ysZHuVUj1g9I=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsStateManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			QFynSzFRj8ZNs48YxDNrvOzNjk/5/TKdEvOpAV6gfAo=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsStatePersisting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			89C3WF44BG25E9QGywyeFSIhzSN3vIZ8j0Cf1zBc7Nw=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsStateProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			/cQous3za5Y8sBNc9gJnSblGnlcQJNisDkxD0q+XHUg=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsUtility.h</key>
		<dict>
			<key>hash2</key>
			<data>
			pGuB3yod13iFDnnyRtuUuYaX7VHyqUUmVwj8DUHOU1Q=
			</data>
		</dict>
		<key>Headers/FBSDKAppEventsUtilityProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			5XYISqTXIOZPBrYNgUjayHCm4HMFKWuvcjLD+DYj3GA=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkCreating.h</key>
		<dict>
			<key>hash2</key>
			<data>
			TIES03/iWapcNCC9q93neFaUJvaUs7+RvhH7EO++7NE=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkEventPosting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ynWClusbalHB6D8+QrD6JmUW1Gr2JdcsDTxToJtiJ/I=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkNavigationBlock.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JmQsfAZrZwAJH5qlBBRvvfzK10IKeqMWfuyED+KYNmU=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkNavigationType.h</key>
		<dict>
			<key>hash2</key>
			<data>
			hwFJL88C4bRrHRe3Y4ljnl5aCOTIvPg7Oqmz7nhwygo=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4DXqK//wHh7BOXM1el/fpSnI2itx5Xvq5U9scd3DtTE=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkResolverRequestBuilding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			e+6RKqsAlhAYxmTXpFwikKmionJN86SP5HKrZXbAkNs=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkResolving.h</key>
		<dict>
			<key>hash2</key>
			<data>
			qb3BIqDSak/QEJVHaa0UyJE2SIhRWJTvILOUETHKNbU=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkTargetCreating.h</key>
		<dict>
			<key>hash2</key>
			<data>
			YPM7wFIveXtTUS5N5/9q/A437m7KU0YQ6H6B206u6R4=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkTargetProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			xRIohRLTU/DyWAlvJeGXzuTsyj3d0Pb9Erqu0nLl2Qo=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkURL.h</key>
		<dict>
			<key>hash2</key>
			<data>
			GAKSBD0rwQ0puEpcq+BbmYBTAYi4nS6GrhNxJ1vQoKQ=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkURLCreating.h</key>
		<dict>
			<key>hash2</key>
			<data>
			sKw0E6SLYUeeNW0wOHnRp9kQIhstniWV4X8zLvnmzVU=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkURLKeys.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Up9j6FGSd/tWYjJkFp+P1BcOj9PPjDtXZlpdfizgL4s=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkUtility.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kp2CnE92bsV1FLjTg72fYgYnBgr5Jsp2QEI1Efop6JQ=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinkVersion.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ApoRkmalk5WwUDAjytM+t8cyZVllZTYJC92Fbu0c22s=
			</data>
		</dict>
		<key>Headers/FBSDKAppLinksBlock.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ummPfWNsfoi+QpV6yx2bMpP5IvBHvQkfcON2myGbUy4=
			</data>
		</dict>
		<key>Headers/FBSDKAppOperationalDataType.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Kyg3KDbG6mD8rK/gtHc1u8If+GNKh/8Uxmbfib1bpXg=
			</data>
		</dict>
		<key>Headers/FBSDKAppStoreReceiptProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			x7PPCf3qyw2K0DAl7af8aPMLzR2TVekwBP1nj0J4Ro4=
			</data>
		</dict>
		<key>Headers/FBSDKAppURLSchemeProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o9vW113QSBrXeTu8w1RgrMfMpi3Li+ZHpavPt/xYGa4=
			</data>
		</dict>
		<key>Headers/FBSDKApplicationActivating.h</key>
		<dict>
			<key>hash2</key>
			<data>
			N3sSrIIH8n9PUXaO6Ko042/wGj7FjgmjMz09LpHRsKk=
			</data>
		</dict>
		<key>Headers/FBSDKApplicationLifecycleNotifications.h</key>
		<dict>
			<key>hash2</key>
			<data>
			gVCQIdprFkqHFWN03C0LuQuv1i9nrhtP3G3t03o3SUM=
			</data>
		</dict>
		<key>Headers/FBSDKApplicationLifecycleObserving.h</key>
		<dict>
			<key>hash2</key>
			<data>
			wPjg8wCzbmbbm8y0dJZMh/wce2hP0UMVUsa2oZnCv6E=
			</data>
		</dict>
		<key>Headers/FBSDKApplicationObserving.h</key>
		<dict>
			<key>hash2</key>
			<data>
			jUSJX2r8mLJr8u+wAn2jLp571gJ3SriNQhCpnkSNEfw=
			</data>
		</dict>
		<key>Headers/FBSDKApplicationStateSetting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			th4kWyfL9axOR4QeSZnS2+ff21hvfrJBKkVqOS9qpY4=
			</data>
		</dict>
		<key>Headers/FBSDKAuthenticationStatusUtility.h</key>
		<dict>
			<key>hash2</key>
			<data>
			eSjfSzpHEMWVAvxBo7bJWUgrhMNFvTx8WmBhyasQcEY=
			</data>
		</dict>
		<key>Headers/FBSDKAuthenticationToken.h</key>
		<dict>
			<key>hash2</key>
			<data>
			hc6XPooa0mMb1VzLG43orpr9kKXVeMa+FrWuKr51KE0=
			</data>
		</dict>
		<key>Headers/FBSDKAuthenticationTokenProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			mfib/LOFoh5fxGgumdwep5nNFARnqYaIQPovjFKmlwo=
			</data>
		</dict>
		<key>Headers/FBSDKAutoSetup.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ex/CIuIAAIQMIaocCGblxTLzDaMcZuJvi3MU6lxdgM0=
			</data>
		</dict>
		<key>Headers/FBSDKBridgeAPIProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o6EBabARMQ/jGNCu0KhpTrBsI6PnWr6akob4aCraUv4=
			</data>
		</dict>
		<key>Headers/FBSDKBridgeAPIProtocolType.h</key>
		<dict>
			<key>hash2</key>
			<data>
			A5XYnc2oBVddlLz3wGiibtMvO6i/A+82gXL5XY+AsgA=
			</data>
		</dict>
		<key>Headers/FBSDKBridgeAPIRequest.h</key>
		<dict>
			<key>hash2</key>
			<data>
			/2BV782KXKlCMETLnCxG2DLQWT+tVDZ/Ijo516KxeVY=
			</data>
		</dict>
		<key>Headers/FBSDKBridgeAPIRequestCreating.h</key>
		<dict>
			<key>hash2</key>
			<data>
			NX+gToQ0lPJ7TSH2qMy4mIDEYMYzkZhwyZbP0XvC1iI=
			</data>
		</dict>
		<key>Headers/FBSDKBridgeAPIRequestOpening.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RkLNO6zfPQbD2c3NWSSdmM3tTFBY3GPfYgBqxAleaf8=
			</data>
		</dict>
		<key>Headers/FBSDKBridgeAPIRequestProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			zXyn4IrEQSs/K0vIuWaHZHWeYSHJfjrS2xjq3hrHThs=
			</data>
		</dict>
		<key>Headers/FBSDKBridgeAPIResponse.h</key>
		<dict>
			<key>hash2</key>
			<data>
			VpEoCY99wbTWk0mTFsof9zSkSb9JK0bc0ty0LHM0GGE=
			</data>
		</dict>
		<key>Headers/FBSDKButton.h</key>
		<dict>
			<key>hash2</key>
			<data>
			3PYcJgi9ksT3Oh7qkXSmtIZ4zC05ftjnedLkaQyiEEc=
			</data>
		</dict>
		<key>Headers/FBSDKButtonImpressionLogging.h</key>
		<dict>
			<key>hash2</key>
			<data>
			pShKA3myYUve8S5W/TI08BTWmhPh0RgoZQ6lY9c5S9g=
			</data>
		</dict>
		<key>Headers/FBSDKClientTokenProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			GvqZ0xNKA6tPwu3fX7MjAlOAhByiiNjI7PR4KRn+IbU=
			</data>
		</dict>
		<key>Headers/FBSDKCodelessIndexer.h</key>
		<dict>
			<key>hash2</key>
			<data>
			FcsOP37OoWFHJHceDyLXTSnifB7NTvxbg8l+Ea2N7KE=
			</data>
		</dict>
		<key>Headers/FBSDKCodelessIndexing.h</key>
		<dict>
			<key>hash2</key>
			<data>
			pLXLVKuG6Qa9BS6uHD2G6ofjKsDr28Rz0u2tNx1QKBM=
			</data>
		</dict>
		<key>Headers/FBSDKConstants.h</key>
		<dict>
			<key>hash2</key>
			<data>
			FhwOaPhmdYaDveEEpkZpS+FYAManp/GC+vHLW+hAU6A=
			</data>
		</dict>
		<key>Headers/FBSDKContainerViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			29xbe5Cc1TOVvV8YSvMCywrkYejv/ToTkWJXi96mSh0=
			</data>
		</dict>
		<key>Headers/FBSDKConversionValueUpdating.h</key>
		<dict>
			<key>hash2</key>
			<data>
			6KHP0pS9iTtIWkg6hqMPweWwdf3DKMJVhKQxPa5rhCY=
			</data>
		</dict>
		<key>Headers/FBSDKCoreKit-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			J+PneAPNIzsM6TXXiXj/wEQDSINuc31/znTjvXu7le4=
			</data>
		</dict>
		<key>Headers/FBSDKCoreKit.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o2TE4Ds12wtlfLBvKjQ8VsMS5tnFEdVj2vJfsqI/21I=
			</data>
		</dict>
		<key>Headers/FBSDKCoreKitVersions.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Vfex5H/RRx7zB2DpAjP6NiIB1F+xRX28EDuU7fmVphY=
			</data>
		</dict>
		<key>Headers/FBSDKCrashObserver.h</key>
		<dict>
			<key>hash2</key>
			<data>
			VLAJlXn/MRpH+6S2wXiJ2MMHTkHfS7Iiq8J4ETcBHo0=
			</data>
		</dict>
		<key>Headers/FBSDKCrashShield.h</key>
		<dict>
			<key>hash2</key>
			<data>
			wrUKA5GCwCODrnCFSkEOYMuhWsXWs+v3tqHYUpWF3JE=
			</data>
		</dict>
		<key>Headers/FBSDKDataProcessingOptionKey.h</key>
		<dict>
			<key>hash2</key>
			<data>
			N45K4T1fNjF4+O/UVhpkoNMCpF0tstXjwLVZpz1m/E0=
			</data>
		</dict>
		<key>Headers/FBSDKDeviceInformationProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o//wG1+zWPP4qO37XPTeCIBSdGTXg1o88LARSZXZBpc=
			</data>
		</dict>
		<key>Headers/FBSDKDialogConfiguration.h</key>
		<dict>
			<key>hash2</key>
			<data>
			sxJ8YoB2cRIbhfFUnxXIe0KRF4COPsi4Zlpo/qsKI/Q=
			</data>
		</dict>
		<key>Headers/FBSDKDialogConfigurationMapBuilding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			DoHcwC+ItwFyYXOM+3jsJKQDRSVYaPDdnK5QjYY6Bdg=
			</data>
		</dict>
		<key>Headers/FBSDKDomainConfiguration.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ODDfLUp/EjYZAOC3/MOAnDiI7lc8nLJSqY8sennYpO4=
			</data>
		</dict>
		<key>Headers/FBSDKDomainConfigurationManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			aJ/dMNcl3sV3TF84MdS1OF9uE+sX9Qh6m5P+Wu5QWhQ=
			</data>
		</dict>
		<key>Headers/FBSDKDomainConfigurationProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			A0RxB1Xye7jGvnnjZ0gR4CstN5CfaM7agMWrUsOCmHg=
			</data>
		</dict>
		<key>Headers/FBSDKDomainHandler.h</key>
		<dict>
			<key>hash2</key>
			<data>
			/swwaLXUz3BoEglRz+kpA5A5WTy4IyeJyk8eC4k3/4g=
			</data>
		</dict>
		<key>Headers/FBSDKDynamicFrameworkLoaderProxy.h</key>
		<dict>
			<key>hash2</key>
			<data>
			DKzc5A2VHGjb5zjiW11FfYE+be1Je5rxetWvpGpLWP8=
			</data>
		</dict>
		<key>Headers/FBSDKErrorConfiguration.h</key>
		<dict>
			<key>hash2</key>
			<data>
			DhLqWnWnnr8IYmWabnEPwwtlgNNnXJV/K4weIvUDLz4=
			</data>
		</dict>
		<key>Headers/FBSDKErrorConfigurationProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+cf3Zndluf4/nH6CRo8QwhZjyyogyQr9rRGqFSJJ/Qw=
			</data>
		</dict>
		<key>Headers/FBSDKErrorConfigurationProvider.h</key>
		<dict>
			<key>hash2</key>
			<data>
			hujKKoeVm3HeeiIxu8JoiRbP5INzeD2/vMsdcL57FLM=
			</data>
		</dict>
		<key>Headers/FBSDKErrorConfigurationProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			disBMqvoKxnyr7jUVHZSCd0dYEdLy+w18e3cMgW5INU=
			</data>
		</dict>
		<key>Headers/FBSDKErrorCreating.h</key>
		<dict>
			<key>hash2</key>
			<data>
			J1rXYSPoy877pIwlZKDP6n/b3DufexQeIl0pOymmYVY=
			</data>
		</dict>
		<key>Headers/FBSDKErrorRecoveryAttempting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			W0d5bC9aorrzc9ohCQNpLCX0lFAN0NEWcKF8J0wW/rs=
			</data>
		</dict>
		<key>Headers/FBSDKErrorRecoveryConfiguration.h</key>
		<dict>
			<key>hash2</key>
			<data>
			sxH7BnnlsngUMLwevQeegntSFKDRSmTEKtWhPQj/1Gs=
			</data>
		</dict>
		<key>Headers/FBSDKErrorReporter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			rcdsRdF/ar59iiIPHyagPto5zGT/Sxfh95CnVdi22+w=
			</data>
		</dict>
		<key>Headers/FBSDKErrorReporting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			akNPfvBbZsCgwyqes5+H7hRxxdGAzyQYxlJqnkGK4hA=
			</data>
		</dict>
		<key>Headers/FBSDKEventLogging.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0VHOLZCsy4MIa2IyIiw2m0r7HlIaK1PEG6/P55bLZ8o=
			</data>
		</dict>
		<key>Headers/FBSDKEventProcessing.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XdMXEDfV+PUzUnWtYK5vvmDq15EbhKM7Msfu34hUKZ8=
			</data>
		</dict>
		<key>Headers/FBSDKEventsProcessing.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yIDs9c5MQ+yOaWXnwlnpInkbHV8GMjI7ZTBwOd+/AT0=
			</data>
		</dict>
		<key>Headers/FBSDKFeature.h</key>
		<dict>
			<key>hash2</key>
			<data>
			V1hpI7TAZqwqEOEABosdtY1n2cBJTV/NzjeKNw9UDUc=
			</data>
		</dict>
		<key>Headers/FBSDKFeatureChecking.h</key>
		<dict>
			<key>hash2</key>
			<data>
			QtYErERzFYGRmUpt4HXd8p062xQtrNl5l+J2nUhhc1k=
			</data>
		</dict>
		<key>Headers/FBSDKFeatureDisabling.h</key>
		<dict>
			<key>hash2</key>
			<data>
			TKR1xKxgrd0N8JUx6kxkmWjLK07R7jMGOSWpnHlMoqA=
			</data>
		</dict>
		<key>Headers/FBSDKFeatureExtracting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			9+2zhi+7pfcv33U40/OTdeLKZgpJVvbygggabihtg/Q=
			</data>
		</dict>
		<key>Headers/FBSDKFeatureExtractor.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kEO+YvkRcV483BfguxWKQ5uS0+Qmx3F3mL3dsHGkRks=
			</data>
		</dict>
		<key>Headers/FBSDKGateKeeperManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			7mWH6MgxmutTpF+sXRQKlG6n5MLh4o5QHROkkRPhs1E=
			</data>
		</dict>
		<key>Headers/FBSDKGateKeeperManaging.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZRWGI6azeE93UU6MLd4PVLDsl0+khZ9ajsIyRJrwYNU=
			</data>
		</dict>
		<key>Headers/FBSDKGraphErrorRecoveryProcessor.h</key>
		<dict>
			<key>hash2</key>
			<data>
			wyEA5FnCIsiLQMekTMTbQfJgpsp5uPtXVy0RVKPm/Co=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequest.h</key>
		<dict>
			<key>hash2</key>
			<data>
			6GPfja4r0l+3PHnf0yfFzGgyV0iPVl+wm7ooUBBX1M0=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestConnecting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			/aivBeJLeVgjejn+J7Ve8A1PgbyFwWSEIOEpNrMZwLw=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestConnection.h</key>
		<dict>
			<key>hash2</key>
			<data>
			1ICPsAdQVISoM+fhXtKdpyBkCX9WIi5bqIEJz2IfkHA=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestConnectionDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			pbeIgtjILQ+9lWJviGFFqpO8IqXWnVf2DQ094YKDKMY=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestConnectionFactory.h</key>
		<dict>
			<key>hash2</key>
			<data>
			xgTvIuiH3O0GP4dAfx+lVweVvj/3VUFzk/ZwDkEd6UM=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestConnectionFactoryProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZSBkNkUs6k4hh3lOO6aa5+j5kuh9vwSm6BTbH++KEH4=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestDataAttachment.h</key>
		<dict>
			<key>hash2</key>
			<data>
			rrQm0dv7u0VNuBOOr4bOsLq22U2VRKN5+/8z8dvg8ac=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestFactory.h</key>
		<dict>
			<key>hash2</key>
			<data>
			VHji6+eQJ/noGhXoav1+rDhYGkeNSPac5f3JMIMO4OQ=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestFactoryProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			LGCTe0D34qYvAzYLEDR50t2F4wbgmylanIR+2HAcV/E=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestFlags.h</key>
		<dict>
			<key>hash2</key>
			<data>
			QaBxTTw493IFQLv0fwkhBkWu57wAPkAZ/fexBSmcWuA=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestHTTPMethod.h</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ZdV1PYtfb+e5MToTE5eWQ/g8Ea8Lfbn1y5cPXIois=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestMetadata.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Twh3qoAgt3ReSFrZuMn8tfYVVZ+eHo/Z01B//qRL/aM=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestPiggybackManaging.h</key>
		<dict>
			<key>hash2</key>
			<data>
			wxiIa9cDFOlxQYXM+TkWOQooJpOkR5UkCGuNOfRHzCo=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			aKkuXQ5CLQQh3MSzr4CWjSGHECgsjZlvDwFTFbRrrFI=
			</data>
		</dict>
		<key>Headers/FBSDKGraphRequestQueue.h</key>
		<dict>
			<key>hash2</key>
			<data>
			jg27Rw8BPOqfHG/lqSFvmFumU0quxxvP7wdja0rMIq8=
			</data>
		</dict>
		<key>Headers/FBSDKIAPDedupeProcessing.h</key>
		<dict>
			<key>hash2</key>
			<data>
			mzR9LdGDTqtc3DYV+JOsBkVoOuodrkRWOO3EnpWU9C0=
			</data>
		</dict>
		<key>Headers/FBSDKIAPFailedTransactionLogging.h</key>
		<dict>
			<key>hash2</key>
			<data>
			YxYLbyBk6Uw+vYZwpHHGp5GKRCS9QE/hRs6s5QPT0uw=
			</data>
		</dict>
		<key>Headers/FBSDKIAPFailedTransactionLoggingCreating.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZzaGG8LO/3EvuN9LDMk/455U5A74GiZMmXHsNKy4Zlk=
			</data>
		</dict>
		<key>Headers/FBSDKIAPTransactionCaching.h</key>
		<dict>
			<key>hash2</key>
			<data>
			bjiGwxnSAUlt+NVqp+emo6xGvyxyQzPz5qaW1530I/E=
			</data>
		</dict>
		<key>Headers/FBSDKImpressionLoggerFactory.h</key>
		<dict>
			<key>hash2</key>
			<data>
			QuBRqz4neAxQgnQCCfZVT7P/volyw8iY5KgrMKRJmJs=
			</data>
		</dict>
		<key>Headers/FBSDKImpressionLoggerFactoryProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Us0Ez3sk3OsuatGsASCqoKebAXNWPg9S5pMOJ5DxDc4=
			</data>
		</dict>
		<key>Headers/FBSDKImpressionLogging.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Vprv+yj1TQTAVjk0AdI+90h3w3DcuHwxVoAECMAadvM=
			</data>
		</dict>
		<key>Headers/FBSDKImpressionLoggingButton.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ndY9up8dSnK60T7Qxxo1R50wARczYvrrA0RXdqjCzKI=
			</data>
		</dict>
		<key>Headers/FBSDKInstrumentManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+5qq+ul/kPhxOgEhuLz8XmNc60qQeWJkTw9obciSaz4=
			</data>
		</dict>
		<key>Headers/FBSDKIntegrityParametersProcessorProvider.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o5WVcgrcNZ9fy9iy8pkmLgt9cY41GNSvTFg3Z4b9k5A=
			</data>
		</dict>
		<key>Headers/FBSDKIntegrityProcessing.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZIhsJR2yehcaQancBDHC1s72R+hCE/frIqi5p8edPn8=
			</data>
		</dict>
		<key>Headers/FBSDKInternalURLOpener.h</key>
		<dict>
			<key>hash2</key>
			<data>
			d7pu09v171CZOLDOnWFtkt5TfA+u9wD6UYyoWxkSA+w=
			</data>
		</dict>
		<key>Headers/FBSDKInternalUtility.h</key>
		<dict>
			<key>hash2</key>
			<data>
			fGPA4wFG0PbwsHF1HMG8uXRBNUpRfuVBclcu0v5TDCk=
			</data>
		</dict>
		<key>Headers/FBSDKInternalUtilityProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			QxK2NWyHrM+GqD1pK5e3LD2xbiZX/p/UzObquLThWI0=
			</data>
		</dict>
		<key>Headers/FBSDKKeychainStore.h</key>
		<dict>
			<key>hash2</key>
			<data>
			sQGCel/07cMPMZI0kl8wscwPV9WL/JIYf1ns0PSf8v0=
			</data>
		</dict>
		<key>Headers/FBSDKKeychainStoreProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XNfcYyJYq69j5eL0ARtTlzkh8SdVCq+G/s9eY4Pd8O8=
			</data>
		</dict>
		<key>Headers/FBSDKKeychainStoreProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			/INTsqlL5Gr82q1fsrI5Mm6ox4RJoy9zwOvstKFPXrI=
			</data>
		</dict>
		<key>Headers/FBSDKLocation.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4VM07vWgUKPPsLEMLF29hXYKIHBkc9vETSX506Z++Uw=
			</data>
		</dict>
		<key>Headers/FBSDKLogger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			O7DI9fO361qB0Y+XI/ka6eMk5/LMGvTE5T0NPkcTBBI=
			</data>
		</dict>
		<key>Headers/FBSDKLoggerFactory.h</key>
		<dict>
			<key>hash2</key>
			<data>
			e8PHQtEWsLuVh+XXwDbUAFSXUORwmsnkmOyyYz+ph8g=
			</data>
		</dict>
		<key>Headers/FBSDKLogging.h</key>
		<dict>
			<key>hash2</key>
			<data>
			IvKTyTv5bHSAJcZqLwaHR/lW5CFnjIggFOzHMRDWMk4=
			</data>
		</dict>
		<key>Headers/FBSDKLoggingBehavior.h</key>
		<dict>
			<key>hash2</key>
			<data>
			f5RLVvxNd/VtlSpoMhH6nO6jFRiC9rudIo+algkCGBs=
			</data>
		</dict>
		<key>Headers/FBSDKLoggingNotifying.h</key>
		<dict>
			<key>hash2</key>
			<data>
			/H/LDFDn+fA0n3pb11qgMdP8PeuXl02axVuT5C9K6nc=
			</data>
		</dict>
		<key>Headers/FBSDKLoginTooltip.h</key>
		<dict>
			<key>hash2</key>
			<data>
			C6wHDAq5ukwucR1FkTnnq3ucsw6y7GR9wDadgB3zHZY=
			</data>
		</dict>
		<key>Headers/FBSDKMacCatalystDetermining.h</key>
		<dict>
			<key>hash2</key>
			<data>
			dOVbOuPHBVVE2h/fz8iXrr824nuLBVRrqymrNwdF5kY=
			</data>
		</dict>
		<key>Headers/FBSDKMath.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RQb23pN1EiaRezovWmrJpZ289Eby0ndYmkHCpsWKbXo=
			</data>
		</dict>
		<key>Headers/FBSDKMeasurementEventListener.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ELmJcxc3dSPYAYyrwqHJpwUxM4is2hfU27i90WTOYQ4=
			</data>
		</dict>
		<key>Headers/FBSDKMeasurementEventNames.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Xy6hXD6E/laXHrWOeDhRU5cQKCbE05HcdVjR5tdUx6E=
			</data>
		</dict>
		<key>Headers/FBSDKMetadataIndexer.h</key>
		<dict>
			<key>hash2</key>
			<data>
			FC2xPfBPJ+KkA1lGRnOxuFDbWIJc5fChKOpjrK4QYMk=
			</data>
		</dict>
		<key>Headers/FBSDKMetadataIndexing.h</key>
		<dict>
			<key>hash2</key>
			<data>
			QjaWgUOa4ftPTfJONsDDsoLADLIhk0afRL0wWiGd9qc=
			</data>
		</dict>
		<key>Headers/FBSDKModelManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4HNzWyenJC3JFcA8hUl1k235rwmiyBYrmtRHx6i0w2g=
			</data>
		</dict>
		<key>Headers/FBSDKMutableCopying.h</key>
		<dict>
			<key>hash2</key>
			<data>
			9WETC6Qraw3B3QY90JfYu/elsAnM/L40JTEsRAOO+hQ=
			</data>
		</dict>
		<key>Headers/FBSDKNetworkErrorChecker.h</key>
		<dict>
			<key>hash2</key>
			<data>
			mQPfqbSnxLTJW64KKhoGcZPQYNTYQABLzG1AZ2hcTSs=
			</data>
		</dict>
		<key>Headers/FBSDKNetworkErrorChecking.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JaxujLpfeoL0uJ15AXk/+TxmnQ+XgmtHYz9sb5k3r1w=
			</data>
		</dict>
		<key>Headers/FBSDKObjectDecoding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			qsowPp05Itw5uODC4pGjeSY1WE9ntYKWLfZW7A0nNw4=
			</data>
		</dict>
		<key>Headers/FBSDKOperatingSystemVersionComparing.h</key>
		<dict>
			<key>hash2</key>
			<data>
			KaQv/BSjIbO1IWul0m3Ckagnf4cBrNKnGm3fhM4GJcg=
			</data>
		</dict>
		<key>Headers/FBSDKPasteboard.h</key>
		<dict>
			<key>hash2</key>
			<data>
			UJnUSOEkUYzGWK3fOCD3HGGdwQLxYhZg4znp+HpSNGQ=
			</data>
		</dict>
		<key>Headers/FBSDKPaymentObserving.h</key>
		<dict>
			<key>hash2</key>
			<data>
			g21RW5m5mkv5jnc/g4IZvHnCPQLSjxGoaSvN3vo4UZw=
			</data>
		</dict>
		<key>Headers/FBSDKPaymentProductRequestor.h</key>
		<dict>
			<key>hash2</key>
			<data>
			S2CSCz82/VNHbyry4LOyJvanQu9UC+br3C0NUFHHqe8=
			</data>
		</dict>
		<key>Headers/FBSDKPaymentProductRequestorCreating.h</key>
		<dict>
			<key>hash2</key>
			<data>
			fGzWAJQkZyRBklHMViipfo4KyA1fMAOzlOiH4HhweQk=
			</data>
		</dict>
		<key>Headers/FBSDKProductAvailability.h</key>
		<dict>
			<key>hash2</key>
			<data>
			AfSg3sbP+VegxUAApbWi9NSI+/dlu9LbDGiLvCWo3Z0=
			</data>
		</dict>
		<key>Headers/FBSDKProductCondition.h</key>
		<dict>
			<key>hash2</key>
			<data>
			dNGTpMMgyZMruD+nBPSsD0Y3Bc2L8ZoTcsW1f5tdK7Q=
			</data>
		</dict>
		<key>Headers/FBSDKProductRequestFactory.h</key>
		<dict>
			<key>hash2</key>
			<data>
			6bp/2xb4hfUOim9SEuGgPr9z+Bysu/FK27psJ1WxipQ=
			</data>
		</dict>
		<key>Headers/FBSDKProductsRequestProtocols.h</key>
		<dict>
			<key>hash2</key>
			<data>
			a7mWH2GsxBZ9X9N8Z/19nO+toxG/0r99YGYxK28kY0o=
			</data>
		</dict>
		<key>Headers/FBSDKProfileBlock.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4/f1aRcfzmU+yr0ypnHjdfSBpIQ8QxluAy3Yn0XxJPE=
			</data>
		</dict>
		<key>Headers/FBSDKProfileNotifications.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yWNI2yhxUUa25VEKtZBJViFYRTXMmJxx1JUrJbK6QAs=
			</data>
		</dict>
		<key>Headers/FBSDKRandom.h</key>
		<dict>
			<key>hash2</key>
			<data>
			/FYnxRG5dQwkxSXWGGkucUnk7tABHKmrqud2upLxMSk=
			</data>
		</dict>
		<key>Headers/FBSDKRestrictiveDataFilterManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			mSbOfI4ZD+1zdJOwvNbPEvSXVCr/iH1TcmA+bmwJW5k=
			</data>
		</dict>
		<key>Headers/FBSDKRulesFromKeyProvider.h</key>
		<dict>
			<key>hash2</key>
			<data>
			KU18OwgqjgGW8bB4DSVWVWXRm2R5Uh9WKEHg/7D71/c=
			</data>
		</dict>
		<key>Headers/FBSDKSKAdNetworkReporter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			uqe1+q806a6PrWGJ1z275ZtkYMC6hW+JEjEM1j+UxFY=
			</data>
		</dict>
		<key>Headers/FBSDKSKAdNetworkReporterV2.h</key>
		<dict>
			<key>hash2</key>
			<data>
			hyqPE+oRV6Q/uWySMPT2HXaeoU0Fhbr/rzJPAblC8zQ=
			</data>
		</dict>
		<key>Headers/FBSDKServerConfiguration.h</key>
		<dict>
			<key>hash2</key>
			<data>
			svD59mf/yaDmtJnt9mqyUETp6BqGqszyESR3t8RUtz0=
			</data>
		</dict>
		<key>Headers/FBSDKServerConfigurationManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			7bxzdmwW3VP1D775pYiEPDzkARci0KU5+G83fUIv+Lk=
			</data>
		</dict>
		<key>Headers/FBSDKServerConfigurationProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nggJ6UDcdQ8WAvG9K2eWGIr1+MNgK3/g6Tj4sImbWHM=
			</data>
		</dict>
		<key>Headers/FBSDKSettingsLogging.h</key>
		<dict>
			<key>hash2</key>
			<data>
			GgfZ+r7AkNT2pA6iA72tqgExzEgxS0MRiVNUSlpsa48=
			</data>
		</dict>
		<key>Headers/FBSDKSourceApplicationTracking.h</key>
		<dict>
			<key>hash2</key>
			<data>
			qajy5wvDILF4b7Jcx9hTizBUhaT1i2GXvpxik1TzkNE=
			</data>
		</dict>
		<key>Headers/FBSDKSuggestedEventsIndexer.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o+ccIBHzx5V9J1IZLNYIg+BkWS3Kl3rMRYXWeWRdhDQ=
			</data>
		</dict>
		<key>Headers/FBSDKSuggestedEventsIndexerProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			9kKE4jZryp1a9dSaPrjDlA+9RhscC9+Yq3GlCBL0DpU=
			</data>
		</dict>
		<key>Headers/FBSDKSwizzler.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0n+HQ6UGePndL7zICwSJXV5WRg12HV3lB4DiARhtWIU=
			</data>
		</dict>
		<key>Headers/FBSDKSwizzling.h</key>
		<dict>
			<key>hash2</key>
			<data>
			KFyWd42cMnaIyExVvONG1g3JPBSvnv/4xdag+5V9zDQ=
			</data>
		</dict>
		<key>Headers/FBSDKTimeSpentData.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4onV/tAXCdp3PH0pHlu7hEtJ7bg+cH1JUhZKPPfhxms=
			</data>
		</dict>
		<key>Headers/FBSDKTimeSpentRecording.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Xi9idQwWvd5DFqQNYAbll1aNfRM0zlSsrHt8LqUVh0E=
			</data>
		</dict>
		<key>Headers/FBSDKTokenCaching.h</key>
		<dict>
			<key>hash2</key>
			<data>
			aqvT3pRj7C448KPxobWR9AZGsr5TeBuYFw6Fe2phX4o=
			</data>
		</dict>
		<key>Headers/FBSDKTokenStringProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			TTwDb4bRpxFALwHRV7rPZ9zwNFPGZhy34If3avetGjM=
			</data>
		</dict>
		<key>Headers/FBSDKTransactionObserving.h</key>
		<dict>
			<key>hash2</key>
			<data>
			NOPfkdHDnW6uD+jIMqNB/e5UkbR4E7d1qzrjahnOHdo=
			</data>
		</dict>
		<key>Headers/FBSDKTransformer.h</key>
		<dict>
			<key>hash2</key>
			<data>
			76ADDGvmmKAAjwJQmqZOKN1QRhrUmGE9qELPwGG2F4k=
			</data>
		</dict>
		<key>Headers/FBSDKURL.h</key>
		<dict>
			<key>hash2</key>
			<data>
			MC/UJOZYmjruWzpiMns2o9iOpycM+cvnUnx2d+brcDA=
			</data>
		</dict>
		<key>Headers/FBSDKURLHosting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			tY5HluqD9iw44nsX/G1SrqCii9dfnxqiv8T/kapFGQs=
			</data>
		</dict>
		<key>Headers/FBSDKURLOpener.h</key>
		<dict>
			<key>hash2</key>
			<data>
			DUeUDJEfTX1NM5s8QxHytKu4/avgUtMZIXnRyx+sTF4=
			</data>
		</dict>
		<key>Headers/FBSDKURLOpening.h</key>
		<dict>
			<key>hash2</key>
			<data>
			djQLdPxXd/h2GC826rf/QKm1UM7Fb9poUtZsWIB9K/M=
			</data>
		</dict>
		<key>Headers/FBSDKURLScheme.h</key>
		<dict>
			<key>hash2</key>
			<data>
			BdpEnJgCwk4m/BX4jdUcFWtch+dOASiPi4b4XuShRow=
			</data>
		</dict>
		<key>Headers/FBSDKURLSessionProxyFactory.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nFIFaAzvqWS22hEvcMkix5YaPcK4GBmjn+7lCb40/RI=
			</data>
		</dict>
		<key>Headers/FBSDKURLSessionProxyProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			aTA0C16KsECkZ2E8HCXO7ce5CfBhS4krYqHjd2sMT84=
			</data>
		</dict>
		<key>Headers/FBSDKURLSessionProxying.h</key>
		<dict>
			<key>hash2</key>
			<data>
			xCWdJ07ZEEhMehbPyCICo2qsde4nyWuid/4r5sZNYM0=
			</data>
		</dict>
		<key>Headers/FBSDKUnarchiverProvider.h</key>
		<dict>
			<key>hash2</key>
			<data>
			km9PeIz2mArhmhaSjTUxnyXNDnyTw45ONMOIrFvX9Ng=
			</data>
		</dict>
		<key>Headers/FBSDKUnarchiverProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			oCPV4SoPYuNAh0Y+MkV7TYC6T/3tFQbq2/dC8tplruQ=
			</data>
		</dict>
		<key>Headers/FBSDKUserAgeRange.h</key>
		<dict>
			<key>hash2</key>
			<data>
			pLTGrXlcRTNXtDJu1omtFNVR7Tl9niHZllskPvHvlLI=
			</data>
		</dict>
		<key>Headers/FBSDKUserDataPersisting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			e5bOiJw1uxd3xCQKKVryAoxKqTxaIVcOMbL0cYWLyJo=
			</data>
		</dict>
		<key>Headers/FBSDKUserDataStore.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RY2Lg4PYOft0K2wbJtT1axcgmL2lJ5PaGtXZga3hQjI=
			</data>
		</dict>
		<key>Headers/FBSDKUserIDProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			n8EfpaG2V2BCI5xE/8Hz8+83cLBSVMqXjkEx1Gv6r6E=
			</data>
		</dict>
		<key>Headers/FBSDKUserIdentifier.h</key>
		<dict>
			<key>hash2</key>
			<data>
			TIFVVKaBj4BvRv/gFJvtxkh3Bm/40McFA0531BAu5AI=
			</data>
		</dict>
		<key>Headers/FBSDKUtility.h</key>
		<dict>
			<key>hash2</key>
			<data>
			aDJa31ufENPB6jQuSRYbBb60HcsTocEvvcqZw2iWBN8=
			</data>
		</dict>
		<key>Headers/FBSDKWebDialogDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RhnzKhKPGNsn+I0efiSBdbYdU7h+Z/m5R4mQUqXW8xA=
			</data>
		</dict>
		<key>Headers/FBSDKWebDialogView.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HC8WLkRk5PhVWGbE58hLSP/goSUOleGYhuUzBNm31v4=
			</data>
		</dict>
		<key>Headers/FBSDKWebView.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yZ2ZBoGlvdRcTOHXhCl80pgVGKFXIQXl9HClExQmfbc=
			</data>
		</dict>
		<key>Headers/FBSDKWebViewAppLinkResolver.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+Ac5AbGpEiHL5SuKIFm5ORqswif+O0w+zlIxL1qgUd4=
			</data>
		</dict>
		<key>Headers/FBSDKWebViewProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			2TVzs8L8DTwpM3xFUOklhzjkivHzm2VLIiY+IXFxMnk=
			</data>
		</dict>
		<key>Headers/NSNotificationCenter+NotificationPosting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			/hTFkvjgGwyFzsQ6VB5CKDoMAKTrun+jQ7LMU4Bcfws=
			</data>
		</dict>
		<key>Headers/NSProcessInfo+MacCatalystDetermining.h</key>
		<dict>
			<key>hash2</key>
			<data>
			N5KvICMBUqwJ3JveHhLX4bomYQIHATUqScz3F4Vyf8Q=
			</data>
		</dict>
		<key>Headers/NSProcessInfo+OperatingSystemVersionComparing.h</key>
		<dict>
			<key>hash2</key>
			<data>
			G6fi3L4ee8mocOfWvq0r6J1Iy2HRUZ7PyHTmpxrxo08=
			</data>
		</dict>
		<key>Headers/UIApplication+URLOpener.h</key>
		<dict>
			<key>hash2</key>
			<data>
			LFldGsPohxFxp2EMtRNr9DW5offC4saf8wAKpZaih60=
			</data>
		</dict>
		<key>Headers/UIPasteboard+Pasteboard.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JdRN7qh5XEBay0JfDd6bXvUO+ciilKne5qAXVmNWoyY=
			</data>
		</dict>
		<key>Headers/WKWebView+WebViewProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Uo6vnwYxxFh+V/+J5U7GTu7dFxg9xliEGHkxzgAJYTs=
			</data>
		</dict>
		<key>Headers/_FBSDKNotificationPosting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZBUZDT5SUs3wBxxP06/A5KDaxedomK4NJ7axY0qD7jY=
			</data>
		</dict>
		<key>Headers/_FBSDKWindowFinding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			QxPymhBROXgyvxD0bzeN+T5ennsD7zaelwvA1a2l3oE=
			</data>
		</dict>
		<key>Headers/__FBSDKLoggerCreating.h</key>
		<dict>
			<key>hash2</key>
			<data>
			KjjsJxMgCNjZYBZHCA+t/dUcdoGwgXLLXtNZjYzHQrA=
			</data>
		</dict>
		<key>Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3vDnj3mq4rNEgbA2If/m00wW5JaGeu+mVlK7rYEf/2s=
			</data>
		</dict>
		<key>Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			TMNNZtPUGIU/qs2kUvQPGEC2iIKt5ImZ4RhTyr/LjT0=
			</data>
		</dict>
		<key>Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			wrCl6/+Xg6AwkRtGiqR/QaRYQhFdO6LBw7yKXixdOEM=
			</data>
		</dict>
		<key>Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			TMNNZtPUGIU/qs2kUvQPGEC2iIKt5ImZ4RhTyr/LjT0=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			l8ml0KsKmiLEQKTzAYpZRg2k4nhDb5o3vF3lArU4ZzY=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			QgUFyksoQJOBn7vsX2WBw89uQ1s+OjAtb6XqI3vhQ74=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
