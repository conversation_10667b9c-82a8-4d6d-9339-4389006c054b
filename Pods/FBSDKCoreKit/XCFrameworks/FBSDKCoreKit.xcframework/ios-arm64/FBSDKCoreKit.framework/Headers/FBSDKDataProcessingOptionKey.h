/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

#import <Foundation/Foundation.h>

/**
 Internal type exposed to facilitate transition to Swift.
 API Subject to change or removal without warning. Do not use.

 @warning INTERNAL - DO NOT USE
 */
typedef NSString *FBSDKDataProcessingOptionKey NS_TYPED_EXTENSIBLE_ENUM NS_SWIFT_NAME(DataProcessingOptionKey);

/**
 Internal value exposed to facilitate transition to Swift.
 API Subject to change or removal without warning. Do not use.

 @warning INTERNAL - DO NOT USE
 */
FOUNDATION_EXPORT FBSDKDataProcessingOptionKey const FBSDKDataProcessingOptionKeyOptions;

/**
 Internal value exposed to facilitate transition to Swift.
 API Subject to change or removal without warning. Do not use.

 @warning INTERNAL - DO NOT USE
 */
FOUNDATION_EXPORT FBSDKDataProcessingOptionKey const FBSDKDataProcessingOptionKeyCountry;

/**
 Internal value exposed to facilitate transition to Swift.
 API Subject to change or removal without warning. Do not use.

 @warning INTERNAL - DO NOT USE
 */
FOUNDATION_EXPORT FBSDKDataProcessingOptionKey const FBSDKDataProcessingOptionKeyState;
