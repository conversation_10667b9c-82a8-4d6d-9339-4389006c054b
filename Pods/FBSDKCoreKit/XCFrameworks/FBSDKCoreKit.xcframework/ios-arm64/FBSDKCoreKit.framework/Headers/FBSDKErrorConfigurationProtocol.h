/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

#import <Foundation/Foundation.h>

@class FBSDKErrorRecoveryConfiguration;
@protocol FBSDKGraphRequest;

NS_ASSUME_NONNULL_BEGIN

/**
 Internal type exposed to facilitate transition to Swift.
 API Subject to change or removal without warning. Do not use.

 @warning INTERNAL - DO NOT USE
 */
NS_SWIFT_NAME(_ErrorConfigurationProtocol)
@protocol FBSDKErrorConfiguration

- (nullable FBSDKErrorRecoveryConfiguration *)recoveryConfigurationForCode:(nullable NSString *)code
                                                                   subcode:(nullable NSString *)subcode
                                                                   request:(id<FBSDKGraphRequest>)request;

@end

NS_ASSUME_NONNULL_END
