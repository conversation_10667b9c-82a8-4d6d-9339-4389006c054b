#if 0
#elif defined(__arm64__) && __arm64__
// Generated by Apple Swift version 6.0.3 effective-5.10 (swiftlang-6.0.3.1.10 clang-1600.0.30.1)
#ifndef FBSDKCOREKIT_SWIFT_H
#define FBSDKCOREKIT_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wnon-modular-include-in-framework-module"
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#pragma clang diagnostic pop
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import AuthenticationServices;
@import CoreFoundation;
@import CoreGraphics;
@import FBAEMKit;
@import FBSDKCoreKit_Basics;
@import Foundation;
@import ObjectiveC;
@import SafariServices;
@import StoreKit;
@import UIKit;
#endif

#import <FBSDKCoreKit/FBSDKCoreKit.h>

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"
#pragma clang diagnostic ignored "-Wunsafe-buffer-usage"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="FBSDKCoreKit",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)
@class NSString;
@class NSNumber;
@class NSURL;

/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_PROTOCOL_NAMED("_AEMReporterProtocol")
@protocol FBSDKAEMReporter
+ (void)enable;
+ (void)recordAndUpdateEvent:(NSString * _Nonnull)event currency:(NSString * _Nullable)currency value:(NSNumber * _Nullable)value parameters:(NSDictionary<NSString *, id> * _Nullable)parameters;
+ (void)setConversionFilteringEnabled:(BOOL)isEnabled;
+ (void)setCatalogMatchingEnabled:(BOOL)isEnabled;
+ (void)setAdvertiserRuleMatchInServerEnabled:(BOOL)isEnabled;
+ (void)handle:(NSURL * _Nullable)url;
@end


@interface FBAEMReporter (SWIFT_EXTENSION(FBSDKCoreKit)) <FBSDKAEMReporter>
@end



@protocol FBSDKAppLinkTarget;

/// Contains App Link metadata relevant for navigation on this device
/// derived from the HTML at a given URL.
SWIFT_CLASS_NAMED("AppLink")
@interface FBSDKAppLink : NSObject <FBSDKAppLink>
/// The URL from which this FBSDKAppLink was derived
@property (nonatomic, readonly, copy) NSURL * _Nullable sourceURL;
/// The ordered list of targets applicable to this platform that will be used
/// for navigation.
@property (nonatomic, readonly, copy) NSArray<id <FBSDKAppLinkTarget>> * _Nonnull targets;
/// The fallback web URL to use if no targets are installed on this device.
@property (nonatomic, readonly, copy) NSURL * _Nullable webURL;
/// Internal property exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// <blockquote>
/// Warning: INTERNAL - DO NOT USE
///
/// </blockquote>
@property (nonatomic, getter=isBackToReferrer) BOOL backToReferrer;
/// Creates an AppLink with the given list of AppLinkTargets and target URL.
/// Generally, this will only be used by implementers of the AppLinkResolving protocol,
/// as these implementers will produce App Link metadata for a given URL.
/// \param sourceURL The <em>URL</em> from which this App Link is derived.
///
/// \param targets An ordered list of AppLinkTargets for this platform derived from App Link metadata.
///
/// \param webURL The fallback web URL, if any, for the app link.
///
- (nonnull instancetype)initWithSourceURL:(NSURL * _Nullable)sourceURL targets:(NSArray<id <FBSDKAppLinkTarget>> * _Nonnull)targets webURL:(NSURL * _Nullable)webURL;
/// Creates an AppLink with the given list of AppLinkTargets and target URL.
/// Generally, this will only be used by implementers of the AppLinkResolving protocol,
/// as these implementers will produce App Link metadata for a given URL.
/// \param sourceURL The <em>URL</em> from which this App Link is derived.
///
/// \param targets An ordered list of AppLinkTargets for this platform derived from App Link metadata.
///
/// \param webURL The fallback web URL, if any, for the app link.
///
+ (id <FBSDKAppLink> _Nonnull)appLinkWithSourceURL:(NSURL * _Nullable)sourceURL targets:(NSArray<id <FBSDKAppLinkTarget>> * _Nonnull)targets webURL:(NSURL * _Nullable)webURL SWIFT_WARN_UNUSED_RESULT SWIFT_DEPRECATED_MSG("\n      Please use designated init to instantiate an AppLink. This method will be removed in future releases.\"\n      ");
/// Internal method exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// <blockquote>
/// Warning: INTERNAL - DO NOT USE
///
/// </blockquote>
- (nonnull instancetype)initWithSourceURL:(NSURL * _Nullable)sourceURL targets:(NSArray<id <FBSDKAppLinkTarget>> * _Nonnull)targets webURL:(NSURL * _Nullable)webURL isBackToReferrer:(BOOL)isBackToReferrer OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@protocol FBSDKAppLinkResolving;
@protocol FBSDKSettings;

/// Represents a pending request to navigate to an app link. Instead of simplying opening a URL, you can build custom requests with additional navigation and app data attached to them by creating an <code>AppLinkNavigation</code>.
SWIFT_CLASS_NAMED("AppLinkNavigation") SWIFT_AVAILABILITY(ios_app_extension,unavailable,message="Not available in app extension")
@interface FBSDKAppLinkNavigation : NSObject
/// The default resolver to be used for App Link resolution. If the developer has not set one explicitly,
/// a basic, built-in <code>WebViewAppLinkResolver</code> will be used.
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, strong) id <FBSDKAppLinkResolving> _Nonnull defaultResolver;)
+ (id <FBSDKAppLinkResolving> _Nonnull)defaultResolver SWIFT_WARN_UNUSED_RESULT;
+ (void)setDefaultResolver:(id <FBSDKAppLinkResolving> _Nonnull)newValue;
/// The extras for the AppLinkNavigation. This will generally contain application-specific
/// data that should be passed along with the request, such as advertiser or affiliate IDs or
/// other such metadata relevant on this device.
@property (nonatomic, readonly, copy) NSDictionary<NSString *, id> * _Nonnull extras;
/// The al_applink_data for the AppLinkNavigation. This will generally contain data common to
/// navigation attempts such as back-links, user agents, and other information that may be used
/// in routing and handling an App Link request.
@property (nonatomic, readonly, copy) NSDictionary<NSString *, id> * _Nonnull appLinkData;
/// The AppLink to navigate to
@property (nonatomic, readonly, strong) FBSDKAppLink * _Nullable appLink;
/// Returns navigation type for current instance. It does not produce any side-effects as the <code>navigate</code> method.
@property (nonatomic, readonly) FBSDKAppLinkNavigationType navigationType;
/// Creates an AppLinkNavigation with the given link, extras, and App Link data
- (nonnull instancetype)initWithAppLink:(FBSDKAppLink * _Nullable)appLink extras:(NSDictionary<NSString *, id> * _Nonnull)extras appLinkData:(NSDictionary<NSString *, id> * _Nonnull)appLinkData OBJC_DESIGNATED_INITIALIZER;
/// Creates an AppLinkNavigation with the given link, extras,  App Link data and settings
- (nonnull instancetype)initWithAppLink:(FBSDKAppLink * _Nonnull)appLink extras:(NSDictionary<NSString *, id> * _Nonnull)extras appLinkData:(NSDictionary<NSString *, id> * _Nonnull)appLinkData settings:(id <FBSDKSettings> _Nonnull)settings SWIFT_DEPRECATED_MSG("\n      Please use init(appLink:extras:appLinkData:) to instantiate an `AppLinkNavigation`.\n      This method will be removed in the next major version.\"\n      ");
/// Creates an AppLinkNavigation with the given link, extras, and App Link data. The <code>settings</code> argument will be ignored in favor of internal dependency injection.
+ (FBSDKAppLinkNavigation * _Nonnull)navigationWithAppLink:(FBSDKAppLink * _Nullable)appLink extras:(NSDictionary<NSString *, id> * _Nonnull)extras appLinkData:(NSDictionary<NSString *, id> * _Nonnull)appLinkData settings:(id <FBSDKSettings> _Nonnull)settings SWIFT_WARN_UNUSED_RESULT SWIFT_DEPRECATED_MSG("\n      Please use designated init to instantiate an AppLinkNavigation. This method will be removed in future releases.\"\n      ");
/// Creates an instance of <code>[String: [String: String]]</code> with the correct format for iOS callback URLs to be used as ‘appLinkData’ argument in the call to init(appLink:extras:appLinkData:).
+ (NSDictionary<NSString *, NSDictionary<NSString *, NSString *> *> * _Nonnull)callbackAppLinkDataForAppWithName:(NSString * _Nonnull)appName url:(NSString * _Nonnull)url SWIFT_WARN_UNUSED_RESULT;
/// Performs the navigation
- (void)navigate:(FBSDKAppLinkNavigationBlock _Nullable)handler;
/// Returns an AppLink for the given URL
+ (void)resolveAppLink:(NSURL * _Nonnull)destination handler:(FBSDKAppLinkBlock _Nonnull)handler;
/// Returns an AppLink for the given URL using the given App Link resolution strategy
+ (void)resolveAppLink:(NSURL * _Nonnull)destination resolver:(id <FBSDKAppLinkResolving> _Nonnull)resolver handler:(FBSDKAppLinkBlock _Nonnull)handler;
+ (void)navigateToAppLink:(FBSDKAppLink * _Nonnull)appLink handler:(FBSDKAppLinkNavigationBlock _Nonnull)handler;
/// Returns an AppLinkNavigationType based on a FBSDKAppLink.
/// It’s essentially a no-side-effect version of navigateToAppLink:error:,
/// allowing apps to determine flow based on the link type (e.g. open an
/// internal web view instead of going straight to the browser for regular links.)
+ (FBSDKAppLinkNavigationType)navigationTypeForLink:(FBSDKAppLink * _Nonnull)appLink SWIFT_WARN_UNUSED_RESULT;
/// Navigates to a URL (an asynchronous action) and returns a NavigationType
+ (void)navigateToURL:(NSURL * _Nonnull)destination handler:(FBSDKAppLinkNavigationBlock _Nonnull)handler;
/// Navigates to a URL (an asynchronous action) using the given App Link resolution
/// strategy and returns a NavigationType
+ (void)navigateToURL:(NSURL * _Nonnull)destination resolver:(id <FBSDKAppLinkResolving> _Nonnull)resolver handler:(FBSDKAppLinkNavigationBlock _Nonnull)handler;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end



/// Provides an implementation of the AppLinkResolving protocol that uses the Facebook App Link
/// Index API to resolve App Links given a URL. It also provides an additional helper method that can resolve
/// multiple App Links in a single call.
SWIFT_CLASS_NAMED("AppLinkResolver")
@interface FBSDKAppLinkResolver : NSObject <FBSDKAppLinkResolving>
- (void)appLinkFromURL:(NSURL * _Nonnull)url handler:(FBSDKAppLinkBlock _Nonnull)handler;
/// Asynchronously resolves App Link data for a given array of URLs.
/// @param urls The URLs to resolve into an App Link.
/// @param handler The completion block that will return an App Link for the given URL.
- (void)appLinksFrom:(NSArray<NSURL *> * _Nonnull)urls handler:(FBSDKAppLinksBlock _Nonnull)handler SWIFT_AVAILABILITY(ios_app_extension,unavailable,message="Not available in app extension");
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end



/// Represents a target defined in App Link metadata, consisting of at least
/// a URL, and optionally an App Store ID and name.
SWIFT_CLASS_NAMED("AppLinkTarget")
@interface FBSDKAppLinkTarget : NSObject <FBSDKAppLinkTarget>
/// The URL prefix for this app link target
@property (nonatomic, readonly, copy) NSURL * _Nullable URL;
/// The app ID for the app store
@property (nonatomic, readonly, copy) NSString * _Nullable appStoreId;
/// The name of the app
@property (nonatomic, readonly, copy) NSString * _Nonnull appName;
/// Creates a AppLinkTarget with the given app site and target URL.
- (nonnull instancetype)initWithURL:(NSURL * _Nullable)url appStoreId:(NSString * _Nullable)appStoreId appName:(NSString * _Nonnull)appName OBJC_DESIGNATED_INITIALIZER;
/// Creates a AppLinkTarget with the given app site and target URL.
+ (FBSDKAppLinkTarget * _Nonnull)appLinkTargetWithURL:(NSURL * _Nullable)url appStoreId:(NSString * _Nullable)appStoreId appName:(NSString * _Nonnull)appName SWIFT_WARN_UNUSED_RESULT SWIFT_DEPRECATED_MSG("\n      Please use designated init to instantiate an AppLinkTarget. This method will be removed in future releases.\"\n      ");
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class UIApplication;
@class NSUserActivity;
@protocol FBSDKApplicationObserving;

/// An <code>ApplicationDelegate</code> is designed to post-process the results from Facebook Login
/// or Facebook Dialogs (or any action that requires switching over to the native Facebook
/// app or Safari).
/// The methods in this class are designed to mirror those in <code>UIApplicationDelegate</code>, and you
/// should call them in the respective methods in your application delegate implementation.
SWIFT_CLASS_NAMED("ApplicationDelegate")
@interface FBSDKApplicationDelegate : NSObject
/// Gets the singleton instance.
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) FBSDKApplicationDelegate * _Nonnull sharedInstance;)
+ (FBSDKApplicationDelegate * _Nonnull)sharedInstance SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
/// Initializes the SDK.
/// If you are using the SDK within the context of the <code>UIApplication</code> lifecycle, do not use this method.
/// Instead use <code>application(_:didFinishLaunchingWithOptions:)</code>.
/// As part of SDK initialization, basic auto logging of app events will occur, this can be
/// controlled via the ‘FacebookAutoLogAppEventsEnabled’ key in your project’s Info.plist file.
- (void)initializeSDK;
/// Call this method from the <code>UIApplicationDelegate.application(_:continue:restorationHandler:)</code> method
/// of your application delegate. It should be invoked in order to properly process the web URL (universal link)
/// once the end user is redirected to your app.
/// \param application The application as passed to `UIApplicationDelegate.application(_:continue:restorationHandler:).
///
/// \param userActivity The user activity as passed to <code>UIApplicationDelegate.application(_:continue:restorationHandler:)</code>.
///
///
/// returns:
/// <code>true</code> if the URL was intended for the Facebook SDK, <code>false</code> if not.
- (BOOL)application:(UIApplication * _Nonnull)application continueUserActivity:(NSUserActivity * _Nonnull)userActivity;
/// Call this method from the <code>UIApplicationDelegate.application(_:open:options:)</code> method
/// of your application delegate. It should be invoked for the proper processing of responses during interaction
/// with the native Facebook app or Safari as part of an SSO authorization flow or Facebook dialogs.
/// \param application The application as passed to <code>UIApplicationDelegate.application(_:open:options:)</code>.
///
/// \param url The URL as passed to <code>UIApplicationDelegate.application(_:open:options:)</code>.
///
/// \param options The options dictionary as passed to <code>UIApplicationDelegate.application(_:open:options:)</code>.
///
///
/// returns:
/// <code>true</code> if the URL was intended for the Facebook SDK, <code>false</code> if not.
- (BOOL)application:(UIApplication * _Nonnull)application openURL:(NSURL * _Nonnull)url options:(NSDictionary<UIApplicationOpenURLOptionsKey, id> * _Nonnull)options;
/// Call this method from the <code>UIApplicationDelegate.application(_:openL:sourceApplication:annotation:)</code> method
/// of your application delegate. It should be invoked for the proper processing of responses during interaction
/// with the native Facebook app or Safari as part of an SSO authorization flow or Facebook dialogs.
/// \param application The application as passed to <code>UIApplicationDelegate.application(_:open:sourceApplication:annotation:)</code>.
///
/// \param url The URL as passed to <code>UIApplicationDelegate.application(_:open:sourceApplication:annotation:)</code>.
///
/// \param sourceApplication The source application as passed to <code>UIApplicationDelegate.application(_:open:sourceApplication:annotation:)</code>.
///
/// \param annotation The annotation as passed to <code>UIApplicationDelegate.application(_:open:sourceApplication:annotation:)</code>.
///
///
/// returns:
/// <code>true</code> if the URL was intended for the Facebook SDK, <code>false</code> if not.
- (BOOL)application:(UIApplication * _Nonnull)application openURL:(NSURL * _Nonnull)url sourceApplication:(NSString * _Nullable)sourceApplication annotation:(id _Nullable)annotation;
/// Call this method from the <code>UIApplicationDelegate.application(_:didFinishLaunchingWithOptions:)</code> method
/// of your application delegate. It should be invoked for the proper use of the Facebook SDK.
/// As part of SDK initialization, basic auto-logging of app events will occur; this can be
/// controlled via the <code>FacebookAutoLogAppEventsEnabled</code> key in the project’s Info.plist file.
/// \param application The application as passed to <code>UIApplicationDelegate.application(_:didFinishLaunchingWithOptions:)</code>.
///
/// \param launchOptions The launch options as passed to <code>UIApplicationDelegate.application(_:didFinishLaunchingWithOptions:)</code>.
///
///
/// returns:
/// <code>true</code>
- (BOOL)application:(UIApplication * _Nonnull)application didFinishLaunchingWithOptions:(NSDictionary<UIApplicationLaunchOptionsKey, id> * _Nullable)launchOptions;
/// Adds an observer that will be informed about application lifecycle events.
/// note:
/// Observers are weakly held
- (void)addObserver:(id <FBSDKApplicationObserving> _Nonnull)observer;
/// Removes an observer so that it will no longer be informed about application lifecycle events.
- (void)removeObserver:(id <FBSDKApplicationObserving> _Nonnull)observer;
@end


SWIFT_CLASS_NAMED("AuthenticationTokenClaims")
@interface FBSDKAuthenticationTokenClaims : NSObject
/// Internal method exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
- (nullable instancetype)initWithEncodedClaims:(NSString * _Nonnull)encodedClaims nonce:(NSString * _Nonnull)expectedNonce;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


@protocol FBSDKGraphRequestFactory;

SWIFT_PROTOCOL_NAMED("CAPIReporter")
@protocol FBSDKCAPIReporter
- (void)enable;
- (void)configureWithFactory:(id <FBSDKGraphRequestFactory> _Nonnull)factory settings:(id <FBSDKSettings> _Nonnull)settings;
- (void)recordEvent:(NSDictionary<NSString *, id> * _Nonnull)parameters;
@end

@protocol FBSDKInternalURLOpener;

/// Internal type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS("_TtC12FBSDKCoreKit17CoreUIApplication")
@interface CoreUIApplication : NSObject
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) id <FBSDKInternalURLOpener> _Nonnull shared;)
+ (id <FBSDKInternalURLOpener> _Nonnull)shared SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class UIImage;
@class UIColor;

/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("FBIcon")
@interface FBSDKIcon : NSObject
- (CGPathRef _Nullable)pathWith:(CGSize)size SWIFT_WARN_UNUSED_RESULT;
- (UIImage * _Nullable)imageWithSize:(CGSize)size SWIFT_WARN_UNUSED_RESULT;
- (UIImage * _Nullable)imageWithSize:(CGSize)size color:(UIColor * _Nonnull)color SWIFT_WARN_UNUSED_RESULT;
- (UIImage * _Nullable)imageWithSize:(CGSize)size scale:(CGFloat)scale color:(UIColor * _Nonnull)color SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

enum FBSDKProfilePictureMode : NSUInteger;
@class FBSDKProfile;
@class NSCoder;

/// A view to display a profile picture.
SWIFT_CLASS_NAMED("FBProfilePictureView")
@interface FBSDKProfilePictureView : UIView
/// The mode for the receiver to determine the aspect ratio of the source image.
@property (nonatomic) enum FBSDKProfilePictureMode pictureMode;
/// The profile ID to show the picture for.
@property (nonatomic, copy) NSString * _Nonnull profileID;
@property (nonatomic) CGRect bounds;
@property (nonatomic) UIViewContentMode contentMode;
/// Create a new instance.
/// \param frame Frame rectangle for the view.
///
/// \param profile Optional profile to display a picture for.
///
- (nonnull instancetype)initWith:(CGRect)frame profile:(FBSDKProfile * _Nullable)profile OBJC_DESIGNATED_INITIALIZER;
/// Create a new instance.
/// \param profile Optional profile to display a picture for.
///
- (nonnull instancetype)initWithProfile:(FBSDKProfile * _Nullable)profile;
/// Initializes and returns a newly allocated view object with the specified frame rectangle.
/// \param frame The frame rectangle for the view, measured in points. The origin of the frame is relative to the superview in which you plan to add it.
/// This method uses the frame rectangle to set the center and bounds properties accordingly.
///
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
/// Initializes and returns a newly allocated view object from the specified coder.
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
/// Explicitly marks the receiver as needing to update the image.
/// This method is called whenever any properties that affect the source image are modified, but this can also
/// be used to trigger a manual update of the image if it needs to be re-downloaded.
- (void)setNeedsImageUpdate;
@end


SWIFT_CLASS("_TtC12FBSDKCoreKit25FBSDKAppEventsCAPIManager")
@interface FBSDKAppEventsCAPIManager : NSObject <FBSDKCAPIReporter>
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) FBSDKAppEventsCAPIManager * _Nonnull shared;)
+ (FBSDKAppEventsCAPIManager * _Nonnull)shared SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (void)configureWithFactory:(id <FBSDKGraphRequestFactory> _Nonnull)factory settings:(id <FBSDKSettings> _Nonnull)settings;
- (void)enable;
- (void)recordEvent:(NSDictionary<NSString *, id> * _Nonnull)parameters;
@end


SWIFT_CLASS("_TtC12FBSDKCoreKit35FBSDKTransformerGraphRequestFactory")
@interface FBSDKTransformerGraphRequestFactory : NSObject
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) FBSDKTransformerGraphRequestFactory * _Nonnull shared;)
+ (FBSDKTransformerGraphRequestFactory * _Nonnull)shared SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (void)configureWithDatasetID:(NSString * _Nonnull)datasetID url:(NSString * _Nonnull)url accessKey:(NSString * _Nonnull)accessKey;
- (void)callCapiGatewayAPIWith:(NSDictionary<NSString *, id> * _Nonnull)parameters userAgent:(NSString * _Nonnull)userAgent;
@end


SWIFT_PROTOCOL_NAMED("MACARuleMatching")
@protocol FBSDKMACARuleMatching
- (void)enable;
- (NSDictionary * _Nullable)processParameters:(NSDictionary * _Nullable)params event:(NSString * _Nullable)event SWIFT_WARN_UNUSED_RESULT;
@end

@class NSDate;
@class FBSDKUserAgeRange;
@class FBSDKLocation;

/// Represents an immutable Facebook profile.
/// This class provides a global current profile instance to more easily
/// add social context to your application. When the profile changes, a notification is
/// posted so that you can update relevant parts of your UI. It is persisted to <code>UserDefaults.standard</code>.
/// Typically, you will want to set <code>enableUpdatesOnAccessTokenChange</code> to <code>true</code> so that
/// it automatically observes changes to <code>AccessToken.current</code>.
/// You can use this class to build your own <code>ProfilePictureView</code> or in place of typical requests to the <code>/me</code> endpoint.
SWIFT_CLASS_NAMED("Profile")
@interface FBSDKProfile : NSObject
/// The user identifier.
@property (nonatomic, readonly, copy) FBSDKUserIdentifier _Nonnull userID;
/// The user’s first name.
@property (nonatomic, readonly, copy) NSString * _Nullable firstName;
/// The user’s middle name.
@property (nonatomic, readonly, copy) NSString * _Nullable middleName;
/// The user’s last name.
@property (nonatomic, readonly, copy) NSString * _Nullable lastName;
/// The user’s complete name.
@property (nonatomic, readonly, copy) NSString * _Nullable name;
/// A URL to the user’s profile.
/// important:
/// This field will only be populated if your user has granted your application the <code>user_link</code> permission.
/// Consider using <code>AppLinkResolver</code> to resolve this URL to an app link in order to link directly to
/// the user’s profile in the Facebook app.
@property (nonatomic, readonly, copy) NSURL * _Nullable linkURL;
/// The last time the profile data was fetched.
@property (nonatomic, readonly, copy) NSDate * _Nonnull refreshDate;
/// A URL to use for fetching the user’s profile image.
@property (nonatomic, readonly, copy) NSURL * _Nullable imageURL;
/// The user’s email address.
/// important:
/// This field will only be populated if your user has granted your application the <code>email</code> permission.
@property (nonatomic, readonly, copy) NSString * _Nullable email;
/// A list of identifiers of the user’s friends.
/// important:
/// This field will only be populated if your user has granted your application
/// the <code>user_friends</code> permission.
@property (nonatomic, readonly, copy) NSArray<NSString *> * _Nullable friendIDs;
/// The user’s birthday.
/// important:
/// This field will only be populated if your user has granted your application
/// the <code>user_birthday</code> permission.
@property (nonatomic, readonly, copy) NSDate * _Nullable birthday;
/// The user’s age range.
/// important:
/// This field will only be populated if your user has granted your application
/// the <code>user_age_range</code> permission.
@property (nonatomic, readonly, strong) FBSDKUserAgeRange * _Nullable ageRange;
/// The user’s hometown.
/// important:
/// This field will only be populated if your user has granted your application
/// the <code>user_hometown</code> permission.
@property (nonatomic, readonly, strong) FBSDKLocation * _Nullable hometown;
/// The user’s location.
/// important:
/// This field will only be populated if your user has granted your application
/// the <code>user_location</code> permission.
@property (nonatomic, readonly, strong) FBSDKLocation * _Nullable location;
/// The user’s gender.
/// important:
/// This field will only be populated if your user has granted your application
/// the <code>user_gender</code> permission.
@property (nonatomic, readonly, copy) NSString * _Nullable gender;
/// The user’s granted permissions.
/// important:
/// This field will only be populated if your user has granted permissions to your
/// application.
@property (nonatomic, readonly, copy) NSSet<NSString *> * _Nullable permissions;
/// Indicates whether this type will automatically observe access token changes
/// (via <code>AccessTokenDidChange</code> notifications).
/// If observing changes, this class will issue a Graph request for public profile data when the current token’s user
/// identifier differs from the current profile. You can observe profile changes via <code>ProfileDidChange</code> notifications
/// to handle an updated profile.
/// note:
/// If the current access token is cleared, the current profile instance remains available. It’s also possible
/// for <code>current</code> to return <code>nil</code> until the data is fetched.
SWIFT_CLASS_PROPERTY(@property (nonatomic, class) BOOL isUpdatedWithAccessTokenChange;)
+ (BOOL)isUpdatedWithAccessTokenChange SWIFT_WARN_UNUSED_RESULT;
+ (void)setIsUpdatedWithAccessTokenChange:(BOOL)value;
/// Creates a new profile.
/// \param userID The user’s identifier.
///
/// \param firstName The user’s first name. Defaults to <code>nil</code>.
///
/// \param middleName The user’s middle name. Defaults to <code>nil</code>.
///
/// \param lastName The user’s last name. Defaults to <code>nil</code>.
///
/// \param name The user’s complete name. Defaults to <code>nil</code>.
///
/// \param linkURL The link for the profile. Defaults to <code>nil</code>.
///
/// \param refreshDate The date the profile was fetched. Defaults to the time of instantiation.
///
/// \param permissions The user’s granted permissions. Defaults to <code>nil</code>.
///
- (nonnull instancetype)initWithUserID:(FBSDKUserIdentifier _Nonnull)userID firstName:(NSString * _Nullable)firstName middleName:(NSString * _Nullable)middleName lastName:(NSString * _Nullable)lastName name:(NSString * _Nullable)name linkURL:(NSURL * _Nullable)linkURL refreshDate:(NSDate * _Nullable)refreshDate permissions:(NSSet<NSString *> * _Nullable)permissions;
/// Creates a new profile.
/// \param userID The user’s identifier. Defaults to <code>nil</code>.
///
/// \param firstName The user’s first name. Defaults to <code>nil</code>.
///
/// \param middleName The user’s middle name. Defaults to <code>nil</code>.
///
/// \param lastName The user’s last name. Defaults to <code>nil</code>.
///
/// \param name The user’s complete name. Defaults to <code>nil</code>.
///
/// \param linkURL The link for this profile. Defaults to <code>nil</code>.
///
/// \param refreshDate The date this profile was fetched. Defaults to the time of instantiation.
///
/// \param imageURL A URL to use for fetching a user’s profile image.
///
/// \param email The user’s email address. Defaults to <code>nil</code>.
///
/// \param friendIDs A list of identifiers for the user’s friends. Defaults to <code>nil</code>.
///
/// \param birthday The user’s birthday. Defaults to <code>nil</code>.
///
/// \param ageRange The user’s age range. Defaults to <code>nil</code>.
///
/// \param hometown The user’s hometown. Defaults to <code>nil</code>.
///
/// \param location The user’s location. Defaults to <code>nil</code>.
///
/// \param gender The user’s gender. Defaults to <code>nil</code>.
///
/// \param permissions The user’s granted permissions. Defaults to <code>nil</code>.
///
- (nonnull instancetype)initWithUserID:(FBSDKUserIdentifier _Nonnull)userID firstName:(NSString * _Nullable)firstName middleName:(NSString * _Nullable)middleName lastName:(NSString * _Nullable)lastName name:(NSString * _Nullable)name linkURL:(NSURL * _Nullable)linkURL refreshDate:(NSDate * _Nullable)refreshDate imageURL:(NSURL * _Nullable)imageURL email:(NSString * _Nullable)email friendIDs:(NSArray<NSString *> * _Nullable)friendIDs birthday:(NSDate * _Nullable)birthday ageRange:(FBSDKUserAgeRange * _Nullable)ageRange hometown:(FBSDKLocation * _Nullable)hometown location:(FBSDKLocation * _Nullable)location gender:(NSString * _Nullable)gender permissions:(NSSet<NSString *> * _Nullable)permissions;
/// Creates a new profile.
/// <ul>
///   <li>
///     permissions: The user’s granted permissions. Defaults to <code>nil</code>.
///   </li>
/// </ul>
/// \param userID The user’s identifier. Defaults to <code>nil</code>.
///
/// \param firstName The user’s first name. Defaults to <code>nil</code>.
///
/// \param middleName The user’s middle name. Defaults to <code>nil</code>.
///
/// \param lastName The user’s last name. Defaults to <code>nil</code>.
///
/// \param name The user’s complete name. Defaults to <code>nil</code>.
///
/// \param linkURL The link for the profile. Defaults to <code>nil</code>.
///
/// \param refreshDate The date the profile was fetched. Defaults to the time of instantiation.
///
/// \param imageURL A URL to use for fetching the user’s profile image Defaults to <code>nil</code>.
///
/// \param email The user’s email address. Defaults to <code>nil</code>.
///
/// \param friendIDs A list of identifiers for the user’s friends. Defaults to <code>nil</code>.
///
/// \param birthday The user’s birthday. Defaults to <code>nil</code>.
///
/// \param ageRange The user’s age range. Defaults to <code>nil</code>.
///
/// \param hometown The user’s hometown. Defaults to <code>nil</code>.
///
/// \param location The user’s location. Defaults to <code>nil</code>.
///
/// \param gender The user’s gender. Defaults to <code>nil</code>.
///
/// \param isLimited Indicates whether the information provided is incomplete in some way.
/// When <code>true</code>, <code>loadCurrentProfile(completion:):</code> will assume the profile is incomplete and disregard
/// any cached profile. Defaults to <code>false</code>.
///
- (nonnull instancetype)initWithUserID:(FBSDKUserIdentifier _Nonnull)userID firstName:(NSString * _Nullable)firstName middleName:(NSString * _Nullable)middleName lastName:(NSString * _Nullable)lastName name:(NSString * _Nullable)name linkURL:(NSURL * _Nullable)linkURL refreshDate:(NSDate * _Nullable)refreshDate imageURL:(NSURL * _Nullable)imageURL email:(NSString * _Nullable)email friendIDs:(NSArray<NSString *> * _Nullable)friendIDs birthday:(NSDate * _Nullable)birthday ageRange:(FBSDKUserAgeRange * _Nullable)ageRange hometown:(FBSDKLocation * _Nullable)hometown location:(FBSDKLocation * _Nullable)location gender:(NSString * _Nullable)gender isLimited:(BOOL)isLimited permissions:(NSSet<NSString *> * _Nullable)permissions OBJC_DESIGNATED_INITIALIZER;
/// Indicates whether this type will automatically observe access token changes
/// (via <code>AccessTokenDidChange</code> notifications).
/// If observing changes, this class will issue a Graph request for public profile data when the current token’s user
/// identifier differs from the current profile. You can observe profile changes via <code>ProfileDidChange</code> notifications
/// to handle an updated profile.
/// note:
/// If the current access token is cleared, the current profile instance remains available. It’s also possible
/// for <code>current</code> to return <code>nil</code> until the data is fetched.
+ (void)enableUpdatesOnAccessTokenChange:(BOOL)enabled SWIFT_DEPRECATED_MSG("This method is deprecated and will be removed in the next major release. Use `isUpdatedWithAccessTokenChange` instead.");
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


@interface FBSDKProfile (SWIFT_EXTENSION(FBSDKCoreKit))
/// A convenience method for returning a complete <code>URL</code> for retrieving the user’s profile image.
/// \param pictureMode The picture mode.
///
/// \param size The height and width. This will be rounded to integer precision.
///
- (NSURL * _Nullable)imageURLForPictureMode:(enum FBSDKProfilePictureMode)pictureMode size:(CGSize)size SWIFT_WARN_UNUSED_RESULT;
@end

/// Defines the aspect ratio mode for the source image of the profile picture.
typedef SWIFT_ENUM_NAMED(NSUInteger, FBSDKProfilePictureMode, "PictureMode", open) {
/// A square cropped version of the image will be included in the view.
  FBSDKProfilePictureModeSquare = 0,
/// The original picture’s aspect ratio will be used for the source image in the view.
  FBSDKProfilePictureModeNormal = 1,
/// The original picture’s aspect ratio will be used for the source image in the view.
  FBSDKProfilePictureModeAlbum = 2,
/// The original picture’s aspect ratio will be used for the source image in the view.
  FBSDKProfilePictureModeSmall = 3,
/// The original picture’s aspect ratio will be used for the source image in the view.
  FBSDKProfilePictureModeLarge = 4,
};



/// Internal type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_PROTOCOL_NAMED("ProfileProviding")
@protocol FBSDKProfileProviding
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, strong) FBSDKProfile * _Nullable currentProfile;)
+ (FBSDKProfile * _Nullable)currentProfile SWIFT_WARN_UNUSED_RESULT;
+ (void)setCurrentProfile:(FBSDKProfile * _Nullable)newValue;
+ (FBSDKProfile * _Nullable)fetchCachedProfile SWIFT_WARN_UNUSED_RESULT;
@end


@interface FBSDKProfile (SWIFT_EXTENSION(FBSDKCoreKit)) <FBSDKProfileProviding>
/// The current profile.
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, strong) FBSDKProfile * _Nullable currentProfile;)
+ (FBSDKProfile * _Nullable)currentProfile SWIFT_WARN_UNUSED_RESULT;
+ (void)setCurrentProfile:(FBSDKProfile * _Nullable)newValue;
+ (nullable instancetype)fetchCachedProfile SWIFT_WARN_UNUSED_RESULT;
@end


@interface FBSDKProfile (SWIFT_EXTENSION(FBSDKCoreKit)) <NSSecureCoding>
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly) BOOL supportsSecureCoding;)
+ (BOOL)supportsSecureCoding SWIFT_WARN_UNUSED_RESULT;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)decoder;
- (void)encodeWithCoder:(NSCoder * _Nonnull)encoder;
@end


@interface FBSDKProfile (SWIFT_EXTENSION(FBSDKCoreKit))
/// Loads the current profile and passes it to the completion block.
/// note:
/// If the profile is already loaded, this method will call the completion block synchronously, otherwise it
/// will begin a graph request to update <code>current</code> and then call the completion block when finished.
/// <ul>
///   <li>
///     Parameter: completion The block to be executed once the profile is loaded.
///   </li>
/// </ul>
+ (void)loadCurrentProfileWithCompletion:(FBSDKProfileBlock _Nullable)completion;
@end



SWIFT_CLASS_NAMED("ProtectedModeManager")
@interface FBSDKProtectedModeManager : NSObject <FBSDKAppEventsParameterProcessing>
- (void)enable;
- (NSDictionary<FBSDKAppEventParameterName, id> * _Nullable)processParameters:(NSDictionary<FBSDKAppEventParameterName, id> * _Nullable)parameters eventName:(FBSDKAppEventName _Nullable)eventName SWIFT_WARN_UNUSED_RESULT;
+ (BOOL)isProtectedModeAppliedWithParameters:(NSDictionary<FBSDKAppEventParameterName, id> * _Nullable)parameters SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end





/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS("_TtC12FBSDKCoreKit27ServerConfigurationProvider")
@interface ServerConfigurationProvider : NSObject
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// Internal type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_PROTOCOL_NAMED("SettingsProtocol")
@protocol FBSDKSettings
@property (nonatomic, copy) NSString * _Nullable appID;
@property (nonatomic, copy) NSString * _Nullable clientToken;
@property (nonatomic, copy) NSString * _Nullable userAgentSuffix;
@property (nonatomic, readonly, copy) NSString * _Nonnull sdkVersion;
@property (nonatomic, copy) NSString * _Nullable displayName;
@property (nonatomic, copy) NSString * _Nullable facebookDomainPart;
@property (nonatomic, copy) NSSet<FBSDKLoggingBehavior> * _Nonnull loggingBehaviors;
@property (nonatomic, copy) NSString * _Nullable appURLSchemeSuffix;
@property (nonatomic, readonly) BOOL isDataProcessingRestricted;
@property (nonatomic, readonly) BOOL isAutoLogAppEventsEnabled;
@property (nonatomic) BOOL codelessDebugLogEnabled SWIFT_DEPRECATED_MSG("\n      This property is deprecated and will be removed in the next major release.       Use `isCodelessDebugLogEnabled` instead.\n      ");
@property (nonatomic) BOOL isCodelessDebugLogEnabled;
@property (nonatomic) BOOL advertiserIDCollectionEnabled SWIFT_DEPRECATED_MSG("\n      This property is deprecated and will be removed in the next major release.       Use `isAdvertiserIDCollectionEnabled` instead.\n      ");
@property (nonatomic) BOOL isAdvertiserIDCollectionEnabled;
@property (nonatomic, readonly) BOOL isSetATETimeExceedsInstallTime SWIFT_DEPRECATED_MSG("\n      This property is deprecated and will be removed in the next major release.       Use `isATETimeSufficientlyDelayed` instead.\n      ");
@property (nonatomic, readonly) BOOL isATETimeSufficientlyDelayed;
@property (nonatomic, readonly) BOOL isSKAdNetworkReportEnabled;
@property (nonatomic, readonly) FBSDKAdvertisingTrackingStatus advertisingTrackingStatus;
@property (nonatomic, readonly, copy) NSDate * _Nullable installTimestamp;
@property (nonatomic, readonly, copy) NSDate * _Nullable advertiserTrackingEnabledTimestamp;
@property (nonatomic) BOOL isEventDataUsageLimited;
@property (nonatomic) BOOL shouldUseTokenOptimizations;
@property (nonatomic, copy) NSString * _Nonnull graphAPIVersion;
@property (nonatomic) BOOL isGraphErrorRecoveryEnabled;
@property (nonatomic, readonly, copy) NSString * _Nullable graphAPIDebugParamValue SWIFT_DEPRECATED_MSG("\n      This property is deprecated and will be removed in the next major release.       Use `graphAPIDebugParameterValue` instead.\n      ");
@property (nonatomic, readonly, copy) NSString * _Nullable graphAPIDebugParameterValue;
@property (nonatomic) BOOL advertiserTrackingEnabled SWIFT_DEPRECATED_MSG("\n      This property is deprecated and will be removed in the next major release.       Use `isAdvertiserTrackingEnabled` instead.\n      ");
@property (nonatomic) BOOL isAdvertiserTrackingEnabled;
@property (nonatomic) BOOL shouldUseCachedValuesForExpensiveMetadata;
@property (nonatomic, readonly, copy) NSDictionary<NSString *, id> * _Nullable persistableDataProcessingOptions;
@property (nonatomic) BOOL isDomainErrorEnabled;
/// Sets the data processing options.
/// \param options The list of options.
///
- (void)setDataProcessingOptions:(NSArray<NSString *> * _Nullable)options;
/// Sets the data processing options.
/// \param options The list of the options. 
///
/// \param country The code for the country. 
///
/// \param state The code for the state. 
///
- (void)setDataProcessingOptions:(NSArray<NSString *> * _Nullable)options country:(int32_t)country state:(int32_t)state;
@end


SWIFT_CLASS_NAMED("Settings")
@interface FBSDKSettings : NSObject <FBSDKSettingsLogging, FBSDKSettings, FBSDKClientTokenProviding>
/// The shared settings instance. Prefer this and the exposed instance methods over the type properties and methods.
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) FBSDKSettings * _Nonnull sharedSettings;)
+ (FBSDKSettings * _Nonnull)sharedSettings SWIFT_WARN_UNUSED_RESULT;
/// The Facebook SDK version in use.
@property (nonatomic, readonly, copy) NSString * _Nonnull sdkVersion;
/// The default Graph API version.
@property (nonatomic, readonly, copy) NSString * _Nonnull defaultGraphAPIVersion;
/// The quality of JPEG images sent to Facebook from the SDK expressed as a value from 0.0 to 1.0.
/// The default value is 0.9.
@property (nonatomic) CGFloat JPEGCompressionQuality;
/// Controls the automatic logging of basic app events such as <code>activateApp</code> and <code>deactivateApp</code>.
/// The default value is <code>true</code>.
@property (nonatomic) BOOL autoLogAppEventsEnabled SWIFT_DEPRECATED_MSG("\n      This property is deprecated and will be removed in the next major release.       Use `isAutoLogAppEventsEnabled` instead.\n      ");
/// Controls the automatic logging of basic app events such as <code>activateApp</code> and <code>deactivateApp</code>.
/// The default value is <code>true</code>.
@property (nonatomic) BOOL isAutoLogAppEventsEnabled;
/// Controls the <code>fb_codeless_debug</code> logging event.
/// The default value is <code>false</code>.
@property (nonatomic) BOOL codelessDebugLogEnabled SWIFT_DEPRECATED_MSG("\n      This property is deprecated and will be removed in the next major release.       Use `isCodelessDebugLogEnabled` instead.\n      ");
/// Controls the <code>fb_codeless_debug</code> logging event.
/// The default value is <code>false</code>.
@property (nonatomic) BOOL isCodelessDebugLogEnabled;
/// Controls the access to IDFA.
/// The default value is <code>true</code>.
@property (nonatomic) BOOL advertiserIDCollectionEnabled SWIFT_DEPRECATED_MSG("\n      This property is deprecated and will be removed in the next major release.       Use `isAdvertiserIDCollectionEnabled` instead.\n      ");
/// Controls the access to IDFA.
/// The default value is <code>true</code>.
@property (nonatomic) BOOL isAdvertiserIDCollectionEnabled;
/// Controls the SKAdNetwork report.
/// The default value is <code>true</code>.
@property (nonatomic) BOOL skAdNetworkReportEnabled SWIFT_DEPRECATED_MSG("\n      This property is deprecated and will be removed in the next major release.       Use `isSKAdNetworkReportEnabled` instead.\n      ");
/// Controls the SKAdNetwork report.
/// The default value is <code>true</code>.
@property (nonatomic) BOOL isSKAdNetworkReportEnabled;
/// Whether data such as that generated through <code>AppEvents</code> and sent to Facebook
/// should be restricted from being used for purposes other than analytics and conversions.
/// The default value is <code>false</code>. This value is stored on the device and persists across app launches.
@property (nonatomic) BOOL isEventDataUsageLimited;
/// Whether in-memory cached values should be used for expensive metadata fields, such as
/// carrier and advertiser ID, that are fetched on many <code>applicationDidBecomeActive</code> notifications.
/// The default value is <code>false</code>. This value is stored on the device and persists across app launches.
@property (nonatomic) BOOL shouldUseCachedValuesForExpensiveMetadata;
/// Controls error recovery for all <code>GraphRequest</code> instances created after the value is changed.
@property (nonatomic) BOOL isGraphErrorRecoveryEnabled;
/// The Facebook App ID used by the SDK.
/// The default value will be read from the application’s plist (FacebookAppID).
@property (nonatomic, copy) NSString * _Nullable appID;
/// The default URL scheme suffix used for sessions.
/// The default value will be read from the application’s plist (FacebookUrlSchemeSuffix).
@property (nonatomic, copy) NSString * _Nullable appURLSchemeSuffix;
/// The client token needed for certain anonymous API calls (i.e., those made without a user-based access token).
/// An app’s client token can be found by navigating to https://developers.facebook.com/apps/YOUR-APP-ID
/// (replacing “YOUR-APP-ID” with your actual app ID), choosing “Settings->Advanced” and scrolling to the “Security”.
/// The default value will be read from the application’s plist (FacebookClientToken).
@property (nonatomic, copy) NSString * _Nullable clientToken;
/// The Facebook Display Name used by the SDK.
/// This should match the Display Name that has been set for the app with the corresponding Facebook App ID
/// in the Facebook App Dashboard.
/// The default value will be read from the application’s plist (FacebookDisplayName).
@property (nonatomic, copy) NSString * _Nullable displayName;
/// The Facebook domain part. This can be used to change the Facebook domain
/// (e.g. “beta”) so that requests will be sent to <code>graph.beta.facebook.com</code>.
/// The default value will be read from the application’s plist (FacebookDomainPart).
@property (nonatomic, copy) NSString * _Nullable facebookDomainPart;
/// Overrides the default Graph API version to use with <code>GraphRequest</code> instances.
/// The string should be of the form <code>"v2.7"</code>.
/// The default value is <code>defaultGraphAPIVersion</code>.
@property (nonatomic, copy) NSString * _Nonnull graphAPIVersion;
/// Internal property exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
@property (nonatomic, copy) NSString * _Nullable userAgentSuffix;
/// Controls the advertiser tracking status of the data sent to Facebook.
@property (nonatomic) BOOL advertiserTrackingEnabled SWIFT_DEPRECATED_MSG("\n      This property is deprecated and will be removed in the next major release.       Use `isAdvertiserTrackingEnabled` instead.\n      ");
/// Controls the advertiser tracking status of the data sent to Facebook.
@property (nonatomic) BOOL isAdvertiserTrackingEnabled;
/// Internal property exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
@property (nonatomic) FBSDKAdvertisingTrackingStatus advertisingTrackingStatus;
/// Internal property exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
@property (nonatomic, readonly) BOOL isDataProcessingRestricted;
/// Internal property exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
@property (nonatomic, readonly, copy) NSDictionary<NSString *, id> * _Nullable persistableDataProcessingOptions;
/// Set the data processing options.
/// \param options The list of options.
///
- (void)setDataProcessingOptions:(NSArray<NSString *> * _Nullable)options;
/// Sets the data processing options.
/// <ul>
///   <li>
///     Parameters:
///   </li>
///   <li>
///     options The list of the options.
///   </li>
///   <li>
///     country The code for the country.
///   </li>
///   <li>
///     state The code for the state.
///   </li>
/// </ul>
- (void)setDataProcessingOptions:(NSArray<NSString *> * _Nullable)options country:(int32_t)country state:(int32_t)state;
/// The current Facebook SDK logging behavior. This should consist of strings
/// defined as constants with <code>LoggingBehavior</code> that indicate what information should be logged.
/// Set to an empty set in order to disable all logging.
/// You can also define this via an array in your app’s plist with the key “FacebookLoggingBehavior”; or add/remove
/// individual values via <code>enableLoggingBehavior(_:)</code> or <code>disableLoggingBehavior(_:)</code>
/// The default value is <code>[.developerErrors]</code>.
@property (nonatomic, copy) NSSet<FBSDKLoggingBehavior> * _Nonnull loggingBehaviors;
/// Enable a particular Facebook SDK logging behavior.
/// \param loggingBehavior The logging behavior to enable. This should be a string constant defined
/// as a <code>LoggingBehavior</code>.
///
- (void)enableLoggingBehavior:(FBSDKLoggingBehavior _Nonnull)loggingBehavior;
/// Disable a particular Facebook SDK logging behavior.
/// \param loggingBehavior The logging behavior to disable. This should be a string constant defined
/// as a <code>LoggingBehavior</code>.
///
- (void)disableLoggingBehavior:(FBSDKLoggingBehavior _Nonnull)loggingBehavior;
/// Internal property exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
@property (nonatomic) BOOL shouldUseTokenOptimizations;
/// Internal property exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
@property (nonatomic, readonly) BOOL isSetATETimeExceedsInstallTime SWIFT_DEPRECATED_MSG("\n      This property is deprecated and will be removed in the next major release.       Use `isATETimeSufficientlyDelayed` instead.\n      ");
/// Internal property exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
@property (nonatomic, readonly) BOOL isATETimeSufficientlyDelayed;
/// Internal property exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
@property (nonatomic, readonly, copy) NSDate * _Nullable installTimestamp;
/// Internal property exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
@property (nonatomic, readonly, copy) NSDate * _Nullable advertiserTrackingEnabledTimestamp;
/// Internal property exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
@property (nonatomic, readonly, copy) NSString * _Nullable graphAPIDebugParamValue SWIFT_DEPRECATED_MSG("\n      This property is deprecated and will be removed in the next major release.       Use `graphAPIDebugParameterValue` instead.\n      ");
/// Internal property exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
@property (nonatomic, readonly, copy) NSString * _Nullable graphAPIDebugParameterValue;
/// Controls whether to show domain errors.
@property (nonatomic) BOOL isDomainErrorEnabled;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end







@interface FBSDKSettings (SWIFT_EXTENSION(FBSDKCoreKit))
/// Internal method exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
- (void)recordInstall;
/// Internal method exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
- (void)logWarnings;
/// Internal method exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
- (void)logIfSDKSettingsChanged;
@end


@class NSURLRequest;

/// Internal class exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("ShimGraphRequestInterceptor")
@interface FBSDKShimGraphRequestInterceptor : NSObject
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) FBSDKShimGraphRequestInterceptor * _Nonnull shared;)
+ (FBSDKShimGraphRequestInterceptor * _Nonnull)shared SWIFT_WARN_UNUSED_RESULT;
- (BOOL)shouldInterceptRequest:(NSURLRequest * _Nonnull)request SWIFT_WARN_UNUSED_RESULT;
- (void)executeWithRequest:(NSURLRequest * _Nonnull)request completionHandler:(FBSDKURLSessionTaskBlock _Nonnull)completionHandler;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end



/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_PROTOCOL_NAMED("_AccessTokenExpiring")
@protocol _FBSDKAccessTokenExpiring
@end

@protocol _FBSDKNotificationPosting;
@protocol FBSDKNotificationDelivering;

/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_AccessTokenExpirer")
@interface _FBSDKAccessTokenExpirer : NSObject <_FBSDKAccessTokenExpiring>
- (nonnull instancetype)initWithNotificationCenter:(id <_FBSDKNotificationPosting, FBSDKNotificationDelivering> _Nonnull)notificationCenter OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


@class FBSDKContainerViewController;

/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS("_TtC12FBSDKCoreKit10_BridgeAPI")
@interface _BridgeAPI : NSObject <FBSDKBridgeAPIRequestOpening, FBSDKApplicationObserving, FBSDKURLOpener, FBSDKContainerViewControllerDelegate, SFSafariViewControllerDelegate>
- (void)viewControllerDidDisappear:(FBSDKContainerViewController * _Nonnull)viewController animated:(BOOL)animated;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class SFSafariViewController;

@interface _BridgeAPI (SWIFT_EXTENSION(FBSDKCoreKit))
- (void)safariViewControllerDidFinish:(SFSafariViewController * _Nonnull)safariViewController;
@end

@class ASWebAuthenticationSession;

SWIFT_AVAILABILITY(ios,introduced=13)
@interface _BridgeAPI (SWIFT_EXTENSION(FBSDKCoreKit)) <ASWebAuthenticationPresentationContextProviding>
- (ASPresentationAnchor _Nonnull)presentationAnchorForWebAuthenticationSession:(ASWebAuthenticationSession * _Nonnull)session SWIFT_WARN_UNUSED_RESULT;
@end

@protocol FBSDKURLOpening;
@protocol FBSDKBridgeAPIRequest;
@class UIViewController;

@interface _BridgeAPI (SWIFT_EXTENSION(FBSDKCoreKit))
- (void)openURL:(NSURL * _Nonnull)url sender:(id <FBSDKURLOpening> _Nullable)sender handler:(FBSDKSuccessBlock _Nonnull)handler;
- (void)openBridgeAPIRequest:(id <FBSDKBridgeAPIRequest> _Nonnull)request useSafariViewController:(BOOL)useSafariViewController fromViewController:(UIViewController * _Nullable)fromViewController completionBlock:(FBSDKBridgeAPIResponseBlock _Nonnull)completionBlock;
- (void)openURLWithSafariViewController:(NSURL * _Nonnull)url sender:(id <FBSDKURLOpening> _Nullable)sender fromViewController:(UIViewController * _Nullable)fromViewController handler:(FBSDKSuccessBlock _Nonnull)handler;
@end


@interface _BridgeAPI (SWIFT_EXTENSION(FBSDKCoreKit))
- (void)applicationWillResignActive:(UIApplication * _Nullable)application;
- (void)applicationDidBecomeActive:(UIApplication * _Nullable)application;
- (void)applicationDidEnterBackground:(UIApplication * _Nullable)application;
- (BOOL)application:(UIApplication * _Nonnull)application openURL:(NSURL * _Nonnull)url sourceApplication:(NSString * _Nullable)sourceApplication annotation:(id _Nullable)annotation SWIFT_WARN_UNUSED_RESULT;
- (BOOL)application:(UIApplication * _Nonnull)application didFinishLaunchingWithOptions:(NSDictionary<UIApplicationLaunchOptionsKey, id> * _Nullable)launchOptions SWIFT_WARN_UNUSED_RESULT;
@end

@protocol FBSDKPasteboard;

/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_BridgeAPIProtocolNativeV1")
@interface FBSDKBridgeAPIProtocolNativeV1 : NSObject <FBSDKBridgeAPIProtocol>
- (nonnull instancetype)initWithAppScheme:(NSString * _Nullable)appScheme;
- (nonnull instancetype)initWithAppScheme:(NSString * _Nullable)appScheme pasteboard:(id <FBSDKPasteboard> _Nullable)pasteboard dataLengthThreshold:(NSUInteger)dataLengthThreshold includeAppIcon:(BOOL)shouldIncludeAppIcon OBJC_DESIGNATED_INITIALIZER;
- (NSURL * _Nullable)requestURLWithActionID:(NSString * _Nonnull)actionID scheme:(NSString * _Nonnull)scheme methodName:(NSString * _Nonnull)methodName parameters:(NSDictionary<NSString *, id> * _Nonnull)parameters error:(NSError * _Nullable * _Nullable)error SWIFT_WARN_UNUSED_RESULT;
- (NSDictionary<NSString *, id> * _Nullable)responseParametersForActionID:(NSString * _Nonnull)actionID queryParameters:(NSDictionary<NSString *, id> * _Nonnull)queryParameters cancelled:(BOOL * _Nullable)cancelledRef error:(NSError * _Nullable * _Nullable)error SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end



/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_BridgeAPIRequestFactory")
@interface FBSDKBridgeAPIRequestFactory : NSObject <FBSDKBridgeAPIRequestCreating>
- (id <FBSDKBridgeAPIRequest> _Nullable)bridgeAPIRequestWithProtocolType:(FBSDKBridgeAPIProtocolType)protocolType scheme:(NSString * _Nonnull)scheme methodName:(NSString * _Nullable)methodName parameters:(NSDictionary<NSString *, id> * _Nullable)parameters userInfo:(NSDictionary<NSString *, id> * _Nullable)userInfo SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class FBSDKDialogConfiguration;

/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_DialogConfigurationMapBuilder")
@interface FBSDKDialogConfigurationMapBuilder : NSObject <FBSDKDialogConfigurationMapBuilding>
- (NSDictionary<NSString *, FBSDKDialogConfiguration *> * _Nonnull)buildDialogConfigurationMapWithRawConfigurations:(NSArray<NSDictionary<NSString *, id> *> * _Nonnull)rawConfigurations SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// Internal type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_ErrorFactory")
@interface FBSDKErrorFactory : NSObject <FBSDKErrorCreating>
- (NSError * _Nonnull)errorWithCode:(NSInteger)code userInfo:(NSDictionary<NSString *, id> * _Nullable)userInfo message:(NSString * _Nullable)message underlyingError:(NSError * _Nullable)underlyingError SWIFT_WARN_UNUSED_RESULT;
- (NSError * _Nonnull)errorWithDomain:(NSString * _Nonnull)domain code:(NSInteger)code userInfo:(NSDictionary<NSString *, id> * _Nullable)userInfo message:(NSString * _Nullable)message underlyingError:(NSError * _Nullable)underlyingError SWIFT_WARN_UNUSED_RESULT;
- (NSError * _Nonnull)invalidArgumentErrorWithName:(NSString * _Nonnull)name value:(id _Nullable)value message:(NSString * _Nullable)message underlyingError:(NSError * _Nullable)underlyingError SWIFT_WARN_UNUSED_RESULT;
- (NSError * _Nonnull)invalidArgumentErrorWithDomain:(NSString * _Nonnull)domain name:(NSString * _Nonnull)name value:(id _Nullable)value message:(NSString * _Nullable)message underlyingError:(NSError * _Nullable)underlyingError SWIFT_WARN_UNUSED_RESULT;
- (NSError * _Nonnull)requiredArgumentErrorWithName:(NSString * _Nonnull)name message:(NSString * _Nullable)message underlyingError:(NSError * _Nullable)underlyingError SWIFT_WARN_UNUSED_RESULT;
- (NSError * _Nonnull)requiredArgumentErrorWithDomain:(NSString * _Nonnull)domain name:(NSString * _Nonnull)name message:(NSString * _Nullable)message underlyingError:(NSError * _Nullable)underlyingError SWIFT_WARN_UNUSED_RESULT;
- (NSError * _Nonnull)unknownErrorWithMessage:(NSString * _Nullable)message userInfo:(NSDictionary<NSString *, id> * _Nullable)userInfo SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end



/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_FBCloseIcon")
@interface FBSDKCloseIcon : NSObject
- (UIImage * _Nullable)imageWithSize:(CGSize)size SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_FBLogo")
@interface FBSDKLogo : FBSDKIcon
- (CGPathRef _Nullable)pathWith:(CGSize)size SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_FeatureManager")
@interface FBSDKFeatureManager : NSObject <FBSDKFeatureChecking, FBSDKFeatureDisabling>
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) FBSDKFeatureManager * _Nonnull shared;)
+ (FBSDKFeatureManager * _Nonnull)shared SWIFT_WARN_UNUSED_RESULT;
- (BOOL)isEnabled:(FBSDKFeature)feature SWIFT_WARN_UNUSED_RESULT;
- (void)checkFeature:(FBSDKFeature)feature completionBlock:(FBSDKFeatureManagerBlock _Nonnull)completionBlock;
- (void)disableFeature:(FBSDKFeature)feature;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end



/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_HumanSilhouetteIcon")
@interface FBSDKHumanSilhouetteIcon : FBSDKIcon
- (CGPathRef _Nullable)pathWith:(CGSize)size SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_MeasurementEvent")
@interface FBSDKMeasurementEvent : NSObject <FBSDKAppLinkEventPosting>
- (void)postNotificationForEventName:(NSString * _Nonnull)eventName args:(NSDictionary<NSString *, id> * _Nonnull)arguments;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class SKPaymentQueue;
@protocol FBSDKPaymentProductRequestorCreating;

/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
/// Class to encapsulate implicit logging of purchase events
SWIFT_CLASS_NAMED("_PaymentObserver")
@interface FBSDKPaymentObserver : NSObject <FBSDKPaymentObserving>
- (nonnull instancetype)initWithPaymentQueue:(SKPaymentQueue * _Nonnull)paymentQueue paymentProductRequestorFactory:(id <FBSDKPaymentProductRequestorCreating> _Nonnull)paymentProductRequestorFactory OBJC_DESIGNATED_INITIALIZER;
- (void)startObservingTransactions;
- (void)stopObservingTransactions;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class SKPaymentTransaction;

@interface FBSDKPaymentObserver (SWIFT_EXTENSION(FBSDKCoreKit)) <SKPaymentTransactionObserver>
- (void)paymentQueue:(SKPaymentQueue * _Nonnull)queue updatedTransactions:(NSArray<SKPaymentTransaction *> * _Nonnull)transactions;
@end

@class FBSDKPaymentProductRequestor;

/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_PaymentProductRequestorFactory")
@interface FBSDKPaymentProductRequestorFactory : NSObject <FBSDKPaymentProductRequestorCreating>
- (FBSDKPaymentProductRequestor * _Nonnull)createRequestorWithTransaction:(SKPaymentTransaction * _Nonnull)transaction SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end



/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_RestrictiveEventFilter")
@interface FBSDKRestrictiveEventFilter : NSObject
@property (nonatomic, readonly, copy) NSString * _Nonnull eventName;
@property (nonatomic, readonly, copy) NSDictionary<NSString *, id> * _Nonnull restrictiveParameters;
- (nonnull instancetype)initWithEventName:(NSString * _Nonnull)eventName restrictiveParameters:(NSDictionary<NSString *, id> * _Nonnull)restrictiveParameters OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_SKAdNetworkEvent")
@interface FBSDKSKAdNetworkEvent : NSObject
@property (nonatomic, readonly, copy) NSString * _Nullable eventName;
@property (nonatomic, copy) NSDictionary<NSString *, NSNumber *> * _Nullable values;
- (nullable instancetype)initWithJSON:(NSDictionary<NSString *, id> * _Nonnull)json OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_ViewImpressionLogger")
@interface FBSDKViewImpressionLogger : NSObject <FBSDKImpressionLogging>
- (nonnull instancetype)initWithEventName:(FBSDKAppEventName _Nonnull)eventName OBJC_DESIGNATED_INITIALIZER;
+ (FBSDKViewImpressionLogger * _Nonnull)retrieveLoggerWith:(FBSDKAppEventName _Nonnull)eventName SWIFT_WARN_UNUSED_RESULT;
- (void)logImpressionWithIdentifier:(NSString * _Nonnull)identifier parameters:(NSDictionary<FBSDKAppEventParameterName, id> * _Nullable)parameters;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


@protocol FBSDKWebDialogDelegate;

/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_WebDialog")
@interface FBSDKWebDialog : NSObject
@property (nonatomic) BOOL shouldDeferVisibility;
@property (nonatomic, weak) id <FBSDKWebDialogDelegate> _Nullable delegate;
- (nonnull instancetype)initWithName:(NSString * _Nonnull)name parameters:(NSDictionary<NSString *, NSString *> * _Nullable)parameters webViewFrame:(CGRect)webViewFrame path:(NSString * _Nullable)path OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithName:(NSString * _Nonnull)name;
- (void)show;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class FBSDKWebDialogView;

@interface FBSDKWebDialog (SWIFT_EXTENSION(FBSDKCoreKit)) <FBSDKWebDialogViewDelegate>
- (void)webDialogView:(FBSDKWebDialogView * _Nonnull)webDialogView didCompleteWithResults:(NSDictionary<NSString *, id> * _Nonnull)results;
- (void)webDialogView:(FBSDKWebDialogView * _Nonnull)webDialogView didFailWithError:(NSError * _Nonnull)error;
- (void)webDialogViewDidCancel:(FBSDKWebDialogView * _Nonnull)webDialogView;
- (void)webDialogViewDidFinishLoad:(FBSDKWebDialogView * _Nonnull)webDialogView;
@end


@protocol FBSDKWebView;

/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
SWIFT_CLASS_NAMED("_WebViewFactory")
@interface FBSDKWebViewFactory : NSObject <FBSDKWebViewProviding>
- (id <FBSDKWebView> _Nonnull)createWebViewWithFrame:(CGRect)frame SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif

#else
#error unsupported Swift architecture
#endif
