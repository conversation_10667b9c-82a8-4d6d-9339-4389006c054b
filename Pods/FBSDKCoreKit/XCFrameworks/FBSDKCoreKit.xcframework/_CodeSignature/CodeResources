<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>LICENSE</key>
		<data>
		42oX9oAD1yj/wA6aqkh1wyx+qqA=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/FBSDKCoreKit</key>
		<data>
		SEBBIHt3XpCO9+wPbihgY/+8+Q8=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/af.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Ze3vRmqRphRbVord7pGibO3fPjM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ar.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RU5U5p2+HVYWsFfSq227cr6oPa4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/bn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			062Bxy406hTTH31PhS52P1+Clqs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/cs.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mD61p8uMAsJbHWM5u43pGNZFp74=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/da.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jOzBKA4li06kg94bqglzoOLJD9M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/de.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nEbzzEMN0NGvB2cD2IpkSbWV6NM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/el.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/j4hlLRB9a6Bz377kSUlsx1c+Uk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/en.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			X2K5j4DkrzLxC/mhfhHoIXx5aRk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/en_GB.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			T3svla3GmadkuwnmQcwbAvlYi98=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/es.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8Bwe0QXPL21MImzsItkC041zC+s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/es_ES.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			c5FkRPxpv9zvua45N7qY/2Lnjc4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/fi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cpEfxM3HMb6H6HEuQEa8afs87bA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/fil.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QDnup8k5E1ovyik1vJG+36NnxRY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/fr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+kWYsXEYhJPWju3cpYvxA0fNPIU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/gu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XWLBntIiv6IgAicsZYD1u2/ZCNE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/he.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9CSDzNjRF5Zi4T8lQzEb4BFHrV8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/hi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3UoJKTt98zMp4MoNL+3/r86aYhQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/hr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lwcfpmcTECcYUnEE5hPsizpm3wg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/hu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IzpGhcaXvGa6oKaqvoknvGo7TtU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/id.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EC8VfATHFmjNpky43WLkcretWaY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/it.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xPK7qaQJKfzP3MZ0b3VJyO+gNMw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ja.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CKj+8eG+ZnwNE9X55i68+oS18Ok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/kn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EsrhFpmgw71P+TyRPtinsIr4hDQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ko.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NaTTNshzDqPGr0O5SATt5Ar/Ke4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ml.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mifHGo/qSi071/IqjLRUwRFs640=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/mr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			51s+e6B/gw6shnNo/gbRAQIunSc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ms.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			evzrkbxiAdaJ+exnQUlBrhVkZjE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/nb.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WMN/9oOghCcQVhMJD5HywSIIetM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/nl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cxRuVwKkGzmB3JnxcdYfSlMU+3Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pa.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VuOm5+GxMy4d/JXxpVyHXLsdpBc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Hv1fBzZGisbifod43+Yl7abQPcA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pt.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			70uwcrwkXYlkAfk5UaIrUQkWQI0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pt_PT.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p9PvnBPstHznGo+paZZrb+pNMAc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ru.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bJltHFKTun/WYe6nQvEAlKM2dRM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/sk.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dDydplGYNYff6uM8Gft5tZqTXhI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/sv.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			N7VTH3m8nIqk04E4+7aclr63OsA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ta.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8ujSZal8Bl4w2uQzbRuH+iyid3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/te.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Dzl0I4eYOYud+sVAICju4r/2xwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/th.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i5VzACdOfJFncxh8cooghE3tIdc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/tr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RbNisbiWvkqWwj2C0LDgvQiLYUA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/vi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7KWOQQAFL0LBHlGYTuxqCzW1agQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/zh.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			socx43t1VI649J+QyCOEeZ/YOJQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/zh_Hant_HK.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VWqZ/8HYO+hnD4ncuigmcqdwTfo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/zh_Hant_TW.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rr2kvGmgmXEdbTLOH1CwY/7fGcc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAEMManager.h</key>
		<data>
		Yj5aPKxlx+gRF70J6qlF3vXE4mk=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKATEPublisherCreating.h</key>
		<data>
		HCKpYddhfbeOKPVi+PyxpQEl/Sw=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKATEPublisherFactory.h</key>
		<data>
		o4BqZ5pce+E7bPPdIkkE30cwTuI=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAccessToken.h</key>
		<data>
		52NScPmZOyhPECDbdwVz3SgFfnU=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAccessTokenProviding.h</key>
		<data>
		8ssoFJOvhs7F3QcUOp076HcFlTA=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAdvertiserIDProviding.h</key>
		<data>
		spPSWUz3WKLY8u+9ZEUgThNQTMY=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAdvertisingTrackingStatus.h</key>
		<data>
		s2/tT+xSXPH4xXaQ+yW41JtgT58=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppAvailabilityChecker.h</key>
		<data>
		Wyf9l4OPVlNw4rmgihSwsLLXekY=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventDropDetermining.h</key>
		<data>
		kuHhyp+2GLTjun17XfcnCE5AhEU=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventName.h</key>
		<data>
		BOUOaSiuKli6Qe/v5ySB3h+M5X4=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventParameterName.h</key>
		<data>
		PUlvfFayxBDoZ9T09/HpoVZ7Hgc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventParameterProduct.h</key>
		<data>
		r+KeRcYNIPkXBrMbyUHT2wI7s9Y=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventParameterValue.h</key>
		<data>
		040yhBlHKamIDlwNu0ImpgLTCqc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventParametersExtracting.h</key>
		<data>
		9pOtxV2/CHu04fcr39VsN+sfZ+s=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventUserDataType.h</key>
		<data>
		A3lEI7gxtNx4AHoXWeE0s7u1zK8=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEvents.h</key>
		<data>
		lg573O7sH41xBg8lJrqEit3K/XQ=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfiguration.h</key>
		<data>
		HzthBYIOOFB3e2msa04M0rtBsRo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfigurationManager.h</key>
		<data>
		ANbr0kXkjHZjFTN/QfFH+kdJmYI=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfigurationProtocol.h</key>
		<data>
		v6/aYc8HvZlvKvREulESWKamY/g=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfigurationProviding.h</key>
		<data>
		Q6UHSFjjvfBNsl6lIgbm+E/xdvg=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfiguring.h</key>
		<data>
		mHGiLeCpVuFdv3XqdFxv9vt7374=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsDeviceInfo.h</key>
		<data>
		SxQnYEiX9/yUxdAbzLI6d4eaegc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsFlushBehavior.h</key>
		<data>
		tGoQfvz6uAy5DjnhPnzX/Ai2vl4=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsFlushReason.h</key>
		<data>
		CVXh0F8QRsx+pTFSV0t8r1QBBAQ=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsNotificationName.h</key>
		<data>
		y7c3PKWx/w77oSbeugClHIvTMS8=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsParameterProcessing.h</key>
		<data>
		lSEA6VeT3RQ/9OIKlCWKuogT/fc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsReporter.h</key>
		<data>
		j2aR5673Kbc66xveJIq2h8rsKLk=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsState.h</key>
		<data>
		WuHkw89fZW/JsqsG7+MqdROIuqU=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsStateManager.h</key>
		<data>
		1npaFG2cN1/3fD1rD7gXkZmnUbI=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsStatePersisting.h</key>
		<data>
		oylwsOMHWTY2ag3rphOgwSAfz7c=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsStateProviding.h</key>
		<data>
		KzGafbBYik5zyGw1bm76HKS7Tc0=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsUtility.h</key>
		<data>
		5vrPi5Z7t//eIoz9u9sQrS88V8E=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsUtilityProtocol.h</key>
		<data>
		w++q3FMjWosIk0QoN6chRjfvng8=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkCreating.h</key>
		<data>
		nqhYr/0Yijl6YG6zjg69DkIZMdo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkEventPosting.h</key>
		<data>
		Unlyk7QfK8RMiwI6wrySVV1IWP4=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkNavigationBlock.h</key>
		<data>
		1PU/0JKCKzJJV3POVzX50kAFA0o=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkNavigationType.h</key>
		<data>
		3fSnRUTotphysdDrdAvLMxLPW+k=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkProtocol.h</key>
		<data>
		FaTKQ3PKUnwjWoYivjhBcoWCvf8=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkResolverRequestBuilding.h</key>
		<data>
		SXBVCFpg1usB96KY2Km7m8BU4PQ=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkResolving.h</key>
		<data>
		vW0iP2TWioh9F2xVFhjb96AWH/M=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkTargetCreating.h</key>
		<data>
		CSjhIDvr2Fy0xL6BAuVxNcPFzdg=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkTargetProtocol.h</key>
		<data>
		KjpJOKaE7Hu4fZ/kcuexDfd7h/E=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkURL.h</key>
		<data>
		QygAk/iNI8C9zeo9e0YUgaIWY7g=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkURLCreating.h</key>
		<data>
		oN/+vlRMv1IvKkWrsh+VtDwu92U=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkURLKeys.h</key>
		<data>
		9Ke3NaIqqmJ0mx/UMz1Vw+/X9hA=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkUtility.h</key>
		<data>
		CSxGCvSd3TFNsqT26linPXGDWM4=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkVersion.h</key>
		<data>
		4lyPD97ZcXbRvhIigNtr3LGnzKo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinksBlock.h</key>
		<data>
		TPb6CeLZPTcgS7hT6GviE2JRz4k=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppOperationalDataType.h</key>
		<data>
		Yxnb0SsotnkBXyJBdPIv4DE+uuE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppStoreReceiptProviding.h</key>
		<data>
		YAWUu9gn0hfRwdt2dscR4Pjncb0=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppURLSchemeProviding.h</key>
		<data>
		G7H5ArEaw56tAdukKkeFnHJW3yM=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKApplicationActivating.h</key>
		<data>
		oHcGo1IxNL4whFjkCAqY0Bezu1A=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKApplicationLifecycleNotifications.h</key>
		<data>
		HZOemKjr7HIyL3ND764s1sIhRdA=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKApplicationLifecycleObserving.h</key>
		<data>
		tM9oT+qXMwjCWeGffrZ/7pU9dhk=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKApplicationObserving.h</key>
		<data>
		xJEwA88eDINbA4Kq2mRmWsoZVaE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKApplicationStateSetting.h</key>
		<data>
		fy5bOvCaeEMdMssTZs5062vJuLs=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAuthenticationStatusUtility.h</key>
		<data>
		NzmrmWDBawgvUzFKUTG4Zq+D8PQ=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAuthenticationToken.h</key>
		<data>
		Hbt4TrpEQ1E7m0cPJVPJrvfOMYE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAuthenticationTokenProviding.h</key>
		<data>
		zDTRRbEFRongbmR9iUSO35PCbhs=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAutoSetup.h</key>
		<data>
		ZrZsPjcuQHmtIb0VA/2JxYjo0Bk=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIProtocol.h</key>
		<data>
		tLjX3wSZTxU3/edMWI3RtKPsv+A=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIProtocolType.h</key>
		<data>
		DblZg5KWlfoY7uhozPWbR8A+C+Q=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequest.h</key>
		<data>
		9p88MKTDxjMSnhRmD3tBUBpyKP0=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequestCreating.h</key>
		<data>
		fSrgRjXiEf48iO5SW4tUzCK4Dfw=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequestOpening.h</key>
		<data>
		0yprixA39qr6F++QUQmDaS+kI5c=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequestProtocol.h</key>
		<data>
		QvIJ69YhObUzXyvQTDogepJbLIY=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIResponse.h</key>
		<data>
		0xHREU5UN9V2806Plbv9YnZCtvo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKButton.h</key>
		<data>
		nRauGR6uRcDNSlw5mNra+SReMhI=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKButtonImpressionLogging.h</key>
		<data>
		LsVpQhR6smaHCPnF/ITEWiKGkKA=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKClientTokenProviding.h</key>
		<data>
		W+JNLAn4YexFixxEQGR3Q9RH0KI=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCodelessIndexer.h</key>
		<data>
		ua4gDNRjw0KAFXM4vqI1AxIhmgQ=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCodelessIndexing.h</key>
		<data>
		vvo1NJ1mTuIU5ZOEX1L9qWcyo+s=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKConstants.h</key>
		<data>
		D8KfnDhhBY1wExKSXz3swq16lTE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKContainerViewController.h</key>
		<data>
		U4pg9nowPmFOUC9fvIpLijlN6fg=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKConversionValueUpdating.h</key>
		<data>
		fTxcQJg2+ZGuh6rqfJoDG5p6L94=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCoreKit-Swift.h</key>
		<data>
		7600QDNflQgHY+mG/cwStJOsec8=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCoreKit.h</key>
		<data>
		9NSBO9571MvtFn98up/7ePg4lwo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCoreKitVersions.h</key>
		<data>
		/eqaMAzP93B16wXvakv2GDTKX0k=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCrashObserver.h</key>
		<data>
		i5LsrVb8OhFQsCQv02NggyWl4qk=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCrashShield.h</key>
		<data>
		488qfAna0UU+EgR6ICrzMNPuE+Y=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDataProcessingOptionKey.h</key>
		<data>
		2KTIgp4tO9oDInYHDKE4PkSZtuo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDeviceInformationProviding.h</key>
		<data>
		AhMeLL4pfNP6NOlehkTmCGd908I=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDialogConfiguration.h</key>
		<data>
		BkHb91/H2f5w4nnsjzWvACP+LO8=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDialogConfigurationMapBuilding.h</key>
		<data>
		3JigPqy5HGPxZ1EfvLl6K2r09G4=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDomainConfiguration.h</key>
		<data>
		z42cVELCq+08IS97csqrsv0G3UM=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDomainConfigurationManager.h</key>
		<data>
		vhxTjwLkFlXSayMOST5Du3FF1Ns=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDomainConfigurationProviding.h</key>
		<data>
		bAZKBepNb9Jwher3IYkkHIK+jM4=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDomainHandler.h</key>
		<data>
		4pB423j54rXYv0k1XCxquIvgGII=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDynamicFrameworkLoaderProxy.h</key>
		<data>
		gQze+1wXFmg8HHDE0Ba4/AdlSbk=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorConfiguration.h</key>
		<data>
		t9pebtrwIlsvol930VzK/Afs/8E=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorConfigurationProtocol.h</key>
		<data>
		rWzO75a4AGVSBUj9usd195pV1pw=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorConfigurationProvider.h</key>
		<data>
		/NYL7EwesbS1Jj4zI5j/M36RrP4=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorConfigurationProviding.h</key>
		<data>
		8oEeLY1N7YRaIXucUhviYIeCeUA=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorCreating.h</key>
		<data>
		hSTHauBFdEYzYLgpazD8Nu2mbvA=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorRecoveryAttempting.h</key>
		<data>
		woJpc0K7/L9yeXE4C+9IxIUoyNo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorRecoveryConfiguration.h</key>
		<data>
		ycgx9i+ACoXgcR7EKbYFYCxzlOg=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorReporter.h</key>
		<data>
		mr/DzUsbnLVLYMNNABg7aaa45vw=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorReporting.h</key>
		<data>
		QPo2mKvh+gBY7bUYxLIRori3P84=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKEventLogging.h</key>
		<data>
		DUSe3ilZQO8BqMsmrVYsaoF7LGA=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKEventProcessing.h</key>
		<data>
		gp3AdngU5TgiopBE4rJMoR6ANSg=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKEventsProcessing.h</key>
		<data>
		O75xhVmbjtVsiUJUjeGE1r788nM=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKFeature.h</key>
		<data>
		Oh4B8Z0ZOzOtzlb70Z4TPxT1NQg=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKFeatureChecking.h</key>
		<data>
		rYTkx84W03mL0rrno4sthw6poiM=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKFeatureDisabling.h</key>
		<data>
		5RTMQMD5BH2IHczkFRDwcDa6YEc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKFeatureExtracting.h</key>
		<data>
		rLjAEFGlg1SFdt3LKE/Vyvpy4C4=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKFeatureExtractor.h</key>
		<data>
		UpeejK9e6KddzNj4pJ/187IOAok=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGateKeeperManager.h</key>
		<data>
		UApODVoVBgYDmemuf8pq5KhSZ2A=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGateKeeperManaging.h</key>
		<data>
		MZUyM6YX2TAfccBQg6xiTBypZ18=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphErrorRecoveryProcessor.h</key>
		<data>
		aNj/uzA6QMePztxIxRll77xHLIg=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequest.h</key>
		<data>
		W0+x27i8eT+6EMZ3lVcQXPKsAP0=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnecting.h</key>
		<data>
		sAlo7za+Sf4zj8Xg+Wy83BUtRCE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnection.h</key>
		<data>
		NUnPpwpwUkVhUBU67o81VFaERfo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnectionDelegate.h</key>
		<data>
		FSHiVcHDpJTlfdfBczQNHtjDJ8s=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnectionFactory.h</key>
		<data>
		gAfT3DO/vuTTlEaJPEP8hL5P3Eo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnectionFactoryProtocol.h</key>
		<data>
		A26a5H79Zb1dRO6YHMFB4DbS+D8=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestDataAttachment.h</key>
		<data>
		7vvCqPiZp4o6JKVaJJ4FP9XkXKE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestFactory.h</key>
		<data>
		lAwX1CKv5VHiJ07/xZZylICOdg4=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestFactoryProtocol.h</key>
		<data>
		ydipmJZDsiXmT3/5DNEiX0gzsNs=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestFlags.h</key>
		<data>
		Zas2ccUoNaCrjUffAdLC6TmKLWs=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestHTTPMethod.h</key>
		<data>
		sF4WT7ko2ZXuQ91thBewwSb29Cc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestMetadata.h</key>
		<data>
		4ZYoDzErnijlAw/YdD1e2qQ3eDs=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestPiggybackManaging.h</key>
		<data>
		RzvPVhBs2lDl2xH7PANCNXkswIE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestProtocol.h</key>
		<data>
		2WftYLHKj8D0QUQgx2F/+tCFX3k=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestQueue.h</key>
		<data>
		YazdAJ0rcrHmvRHvxaduooEWglg=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKIAPDedupeProcessing.h</key>
		<data>
		jFfQ2uToisxlUwu5HAehBwtT5yc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKIAPFailedTransactionLogging.h</key>
		<data>
		3naly4poGndtl04IKJLRhIn0y58=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKIAPFailedTransactionLoggingCreating.h</key>
		<data>
		8f1KPldp/xKn7SdJ+extn1OcPWY=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKIAPTransactionCaching.h</key>
		<data>
		EUOfrTb9ijRfv++ZOI1VjUF46+o=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKImpressionLoggerFactory.h</key>
		<data>
		nfa97DKHYMz7v0+pUDy3nlxrUYk=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKImpressionLoggerFactoryProtocol.h</key>
		<data>
		3LEI96v+5GIEFysj3D1C/rG8ihw=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKImpressionLogging.h</key>
		<data>
		wrrEayG2/hN5wUceRYRAU8HrXBk=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKImpressionLoggingButton.h</key>
		<data>
		5jJQoQEgcqekG5Zl8U42hb9oI80=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKInstrumentManager.h</key>
		<data>
		b4LuHg6PamZ7IBBxVoOYpxuYERM=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKIntegrityParametersProcessorProvider.h</key>
		<data>
		YAejrtcnEaVtGKUEB4ZJpyeATvQ=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKIntegrityProcessing.h</key>
		<data>
		87ZiOpw4U2tli0OR6o2FOZV0Szc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKInternalURLOpener.h</key>
		<data>
		FLV0q7bfHv0++Xz2yX93rch6WQk=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKInternalUtility.h</key>
		<data>
		xVnxfL7eTB7Crbqhijh4imgTplo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKInternalUtilityProtocol.h</key>
		<data>
		/ZcaAKHHVRGsTO5fkpWQKXtwRlg=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKKeychainStore.h</key>
		<data>
		VSfBFlzguwgAJhVey77PM3TiKzI=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKKeychainStoreProtocol.h</key>
		<data>
		Vl4nIrUwT7cqcjwlXymbPkKUVDo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKKeychainStoreProviding.h</key>
		<data>
		u2GnomfI+oHCeoe6Ei9pG5quwVE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLocation.h</key>
		<data>
		lVldFN//gmPckkWOntm6/lMe0QE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLogger.h</key>
		<data>
		19xKJkbTRpwMoQwzaC6Nu+w5tME=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLoggerFactory.h</key>
		<data>
		oN6VhKse4/3rPEKNFGojETNkg58=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLogging.h</key>
		<data>
		/DbryGZcqEQACAktvCjPjV6SDG4=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLoggingBehavior.h</key>
		<data>
		lNVc//dYNZlhHrZHLByspMT+TeY=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLoggingNotifying.h</key>
		<data>
		ClxijYzHweoVytnlGNbT1n62Q2I=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLoginTooltip.h</key>
		<data>
		t9qlwGoUeyWhxDfXE8Ky6RnF/gg=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMacCatalystDetermining.h</key>
		<data>
		Fy2901RMaq8v1KZ1nkSvVuhYSN8=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMath.h</key>
		<data>
		rYMaHGq5qMdO0PMhIT/NeaWQMFE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMeasurementEventListener.h</key>
		<data>
		OVZSC/JIKFj+KgzAwt5biOqF7rI=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMeasurementEventNames.h</key>
		<data>
		9XUuzXpHXt6PyqsDNvqKaCYOsaQ=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMetadataIndexer.h</key>
		<data>
		yvEMxozxtAwiiPOoouj17MOx38w=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMetadataIndexing.h</key>
		<data>
		0XbHu0BpTUjfkrPvYn1B9+H0Bww=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKModelManager.h</key>
		<data>
		UTvH2kBUyG0vy9bmR5tucIdzDMU=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMutableCopying.h</key>
		<data>
		CdAKmAi79FHfugMUCBcou38XjyY=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKNetworkErrorChecker.h</key>
		<data>
		lc4ltIsnGN0wefVKZeW3BTQqt8o=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKNetworkErrorChecking.h</key>
		<data>
		DQOOpk+tae6sTARv6zgYkUNQv+4=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKObjectDecoding.h</key>
		<data>
		rsoqTN959edBc1dWsOJAk4SFajc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKOperatingSystemVersionComparing.h</key>
		<data>
		jd/kWOWeuMfcK4hF/tgU6AeS2yU=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKPasteboard.h</key>
		<data>
		4+oJKTiDHC9gezS8CCuhdWHO2gs=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKPaymentObserving.h</key>
		<data>
		2F/w1aia/WcWnKwAGffiRgYimWQ=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKPaymentProductRequestor.h</key>
		<data>
		ZQ6Btyy0z7ZiD/M9eX+7+KDRiLE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKPaymentProductRequestorCreating.h</key>
		<data>
		kofChLuS6UMw2biE8Lcu2yhsmt0=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKProductAvailability.h</key>
		<data>
		4z6lAOLiyG+H6sMmuDzBXlrBO4Q=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKProductCondition.h</key>
		<data>
		p2M86R+0XjuIIHBALGh4qHhF0sg=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKProductRequestFactory.h</key>
		<data>
		7BIaz0Ab6i62/Dq0UaZpdoNPhCs=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKProductsRequestProtocols.h</key>
		<data>
		quFgypTeZ+2XNvJr6deEaYceClo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKProfileBlock.h</key>
		<data>
		m3b2HY43bVO+k7tLfgTUHlel3XM=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKProfileNotifications.h</key>
		<data>
		p/v7YsyAewf4Zx5PgVOw0g9689k=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKRandom.h</key>
		<data>
		rqrUnE5WdNwaBw/Fm/Sauc2KceA=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKRestrictiveDataFilterManager.h</key>
		<data>
		FhBIBCgefnNx6Hx9hBvjEQxlgTk=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKRulesFromKeyProvider.h</key>
		<data>
		9/nqsZZ8Epe4NBnTPqEOptCNdzs=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSKAdNetworkReporter.h</key>
		<data>
		N8+fSt031r3BemNKfykFVGaMU2g=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSKAdNetworkReporterV2.h</key>
		<data>
		t1JQiDuyttg76dcS901/oxfu6tQ=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKServerConfiguration.h</key>
		<data>
		ePai1Ct6rcU1fbdNFDdAFZgZLYo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKServerConfigurationManager.h</key>
		<data>
		MabAusZdKvvZ5Ny2wrhRTzvzRhY=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKServerConfigurationProviding.h</key>
		<data>
		OVcy7K8/6LpbY7Ls9wBkMeiLOXc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSettingsLogging.h</key>
		<data>
		j4NKiO1um7BzI27sPShA+WNNV6E=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSourceApplicationTracking.h</key>
		<data>
		eyc/NF7kaz05PnojBKao9RoOkXo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSuggestedEventsIndexer.h</key>
		<data>
		eLTHBeEjKRaaEKDEDTrfz5o1+pE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSuggestedEventsIndexerProtocol.h</key>
		<data>
		Nlpu1MobSLVgHiTs61VhpCb8F84=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSwizzler.h</key>
		<data>
		PgutzEuZyjT9se/U24OTeoEMo1g=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSwizzling.h</key>
		<data>
		jMRYakKevWrqsOerYuQ9Fb1eCT4=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKTimeSpentData.h</key>
		<data>
		Zgill5e+tNHLsGRpZ8nXHCk2iEc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKTimeSpentRecording.h</key>
		<data>
		NuI7oI0R+b4/s+KiUbS+MEn4x8k=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKTokenCaching.h</key>
		<data>
		cla0KYAtpSCy2GcXC++XdSQrxck=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKTokenStringProviding.h</key>
		<data>
		43wQbjzLSB+cSu9hTQZ4tJq24Mc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKTransactionObserving.h</key>
		<data>
		lZIW9x0Pm8LlHaUourw8qilnS7I=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKTransformer.h</key>
		<data>
		Ui2GFPACS7T6kK9LcCLcdJyCYyo=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURL.h</key>
		<data>
		lg+hfhk8xZ7goFdbGZ8cVtCyNbw=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLHosting.h</key>
		<data>
		O+RAgM4Uu49yrzKRLeqtv9FfVKE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLOpener.h</key>
		<data>
		/9/EmnpZWM6KdVyGGEI1NUvMbYk=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLOpening.h</key>
		<data>
		0r8abqnzwBXvpV5PlCa/kQ+OdW8=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLScheme.h</key>
		<data>
		36HfFNYLwWfRajDYFDJeNZe/evc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLSessionProxyFactory.h</key>
		<data>
		fkszrJOzG1OgfuDotJLKT74xw1w=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLSessionProxyProviding.h</key>
		<data>
		5Hely+G3LbaFH81TEA3aS8tH8Xk=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLSessionProxying.h</key>
		<data>
		LIrnTeexPfrk+a2pllxOcLBqw+Q=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUnarchiverProvider.h</key>
		<data>
		mBxizULPLClFmFkzGvq7DZbedIU=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUnarchiverProviding.h</key>
		<data>
		PU6uhik4cqPkhqWAmJpctYAS+vI=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUserAgeRange.h</key>
		<data>
		paq4PxztwLvJ2EQKujqXLJfgjiA=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUserDataPersisting.h</key>
		<data>
		eiIDv79NpGtVgJHZ+jovQsS4ZsU=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUserDataStore.h</key>
		<data>
		iSw84njE3Q4fckPhhhUCpEMT4Uw=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUserIDProviding.h</key>
		<data>
		1G3kOunQeQMavUujt30qZv1Oj98=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUserIdentifier.h</key>
		<data>
		EqrAGtLbcJRfQwl12songIdMMEE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUtility.h</key>
		<data>
		ACK+e48w6WLwZDhZT9VIaXDWTlk=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKWebDialogDelegate.h</key>
		<data>
		oPLwzJ7KUTr8T5hq/c983EM1rfE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKWebDialogView.h</key>
		<data>
		EHOQKin9zYzG+gmc/LfBegKrvEE=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKWebView.h</key>
		<data>
		jUhFO4/5Ly1VNml/cAryOayE4Ag=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKWebViewAppLinkResolver.h</key>
		<data>
		jEC9UH9Inm7DYqoFZv2qaN3Pe14=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKWebViewProviding.h</key>
		<data>
		6dBqgFJpP3qYvCEw7KHe/d5ieC8=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/NSNotificationCenter+NotificationPosting.h</key>
		<data>
		4IqhFgtQjABnwxNo0vP4+5Q/hSU=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/NSProcessInfo+MacCatalystDetermining.h</key>
		<data>
		P2PZ0k71jzSsWpQg6vH4TOHr6SU=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/NSProcessInfo+OperatingSystemVersionComparing.h</key>
		<data>
		7p95y3GgCy4C7g+8xfGVXW4sr18=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/UIApplication+URLOpener.h</key>
		<data>
		N6kxbbjqI4XB8O7cVqNSb4VtaOs=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/UIPasteboard+Pasteboard.h</key>
		<data>
		MKDA9GrF2o6+h3tbW9e9adSk23k=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/WKWebView+WebViewProtocol.h</key>
		<data>
		9AxT3+g6DPZx5q3rQWhRBMFJu64=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/_FBSDKNotificationPosting.h</key>
		<data>
		w1Gpzlc2q889xvFT3+ffPk2kwQU=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/_FBSDKWindowFinding.h</key>
		<data>
		Gac9mAAYHny41SRhpW53CbfSo2s=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/__FBSDKLoggerCreating.h</key>
		<data>
		TTkFW5xuSEAOVq8O1cbEJ6/RgQg=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Info.plist</key>
		<data>
		ioQQll/iBFQ7MfZ5k8S1w05OKS0=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		OkoPZtcS5lu+Hk/2Cmm3NkUC1LA=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		o8MMQ2RxBsTwTAF4vAwFP1JbYWY=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		CSmfnAungUw15mQMizDDGTV7ElY=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		o8MMQ2RxBsTwTAF4vAwFP1JbYWY=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/Modules/module.modulemap</key>
		<data>
		130ouJhMpZj1Z376F5gsUfoyxyc=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/PrivacyInfo.xcprivacy</key>
		<data>
		+4xzw+xvpntuK5AhSisGTSzsudg=
		</data>
		<key>ios-arm64/FBSDKCoreKit.framework/_CodeSignature/CodeResources</key>
		<data>
		oBbmbiKeJYLes+olDksn9+D2F5s=
		</data>
		<key>ios-arm64/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Info.plist</key>
		<data>
		qG6Ty6t/qhX430RoQk8cuQ7fGXw=
		</data>
		<key>ios-arm64/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/DWARF/FBSDKCoreKit</key>
		<data>
		1ICJ/z1AWgvnW7vov0Zaozwtp/k=
		</data>
		<key>ios-arm64/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKCoreKit.yml</key>
		<data>
		inyN4V/w2oSJWzLsD1cUfuJBsxI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/FBSDKCoreKit</key>
		<data>
		A1M7Ma5ErpZr+BkSYlLpHK41HY0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAEMManager.h</key>
		<data>
		Yj5aPKxlx+gRF70J6qlF3vXE4mk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKATEPublisherCreating.h</key>
		<data>
		HCKpYddhfbeOKPVi+PyxpQEl/Sw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKATEPublisherFactory.h</key>
		<data>
		o4BqZ5pce+E7bPPdIkkE30cwTuI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAccessToken.h</key>
		<data>
		52NScPmZOyhPECDbdwVz3SgFfnU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAccessTokenProviding.h</key>
		<data>
		8ssoFJOvhs7F3QcUOp076HcFlTA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAdvertiserIDProviding.h</key>
		<data>
		spPSWUz3WKLY8u+9ZEUgThNQTMY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAdvertisingTrackingStatus.h</key>
		<data>
		s2/tT+xSXPH4xXaQ+yW41JtgT58=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppAvailabilityChecker.h</key>
		<data>
		Wyf9l4OPVlNw4rmgihSwsLLXekY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventDropDetermining.h</key>
		<data>
		kuHhyp+2GLTjun17XfcnCE5AhEU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventName.h</key>
		<data>
		BOUOaSiuKli6Qe/v5ySB3h+M5X4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventParameterName.h</key>
		<data>
		PUlvfFayxBDoZ9T09/HpoVZ7Hgc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventParameterProduct.h</key>
		<data>
		r+KeRcYNIPkXBrMbyUHT2wI7s9Y=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventParameterValue.h</key>
		<data>
		040yhBlHKamIDlwNu0ImpgLTCqc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventParametersExtracting.h</key>
		<data>
		9pOtxV2/CHu04fcr39VsN+sfZ+s=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventUserDataType.h</key>
		<data>
		A3lEI7gxtNx4AHoXWeE0s7u1zK8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEvents.h</key>
		<data>
		lg573O7sH41xBg8lJrqEit3K/XQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsConfiguration.h</key>
		<data>
		HzthBYIOOFB3e2msa04M0rtBsRo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsConfigurationManager.h</key>
		<data>
		ANbr0kXkjHZjFTN/QfFH+kdJmYI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsConfigurationProtocol.h</key>
		<data>
		v6/aYc8HvZlvKvREulESWKamY/g=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsConfigurationProviding.h</key>
		<data>
		Q6UHSFjjvfBNsl6lIgbm+E/xdvg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsConfiguring.h</key>
		<data>
		mHGiLeCpVuFdv3XqdFxv9vt7374=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsDeviceInfo.h</key>
		<data>
		SxQnYEiX9/yUxdAbzLI6d4eaegc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsFlushBehavior.h</key>
		<data>
		tGoQfvz6uAy5DjnhPnzX/Ai2vl4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsFlushReason.h</key>
		<data>
		CVXh0F8QRsx+pTFSV0t8r1QBBAQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsNotificationName.h</key>
		<data>
		y7c3PKWx/w77oSbeugClHIvTMS8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsParameterProcessing.h</key>
		<data>
		lSEA6VeT3RQ/9OIKlCWKuogT/fc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsReporter.h</key>
		<data>
		j2aR5673Kbc66xveJIq2h8rsKLk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsState.h</key>
		<data>
		WuHkw89fZW/JsqsG7+MqdROIuqU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsStateManager.h</key>
		<data>
		1npaFG2cN1/3fD1rD7gXkZmnUbI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsStatePersisting.h</key>
		<data>
		oylwsOMHWTY2ag3rphOgwSAfz7c=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsStateProviding.h</key>
		<data>
		KzGafbBYik5zyGw1bm76HKS7Tc0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsUtility.h</key>
		<data>
		5vrPi5Z7t//eIoz9u9sQrS88V8E=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsUtilityProtocol.h</key>
		<data>
		w++q3FMjWosIk0QoN6chRjfvng8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkCreating.h</key>
		<data>
		nqhYr/0Yijl6YG6zjg69DkIZMdo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkEventPosting.h</key>
		<data>
		Unlyk7QfK8RMiwI6wrySVV1IWP4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkNavigationBlock.h</key>
		<data>
		1PU/0JKCKzJJV3POVzX50kAFA0o=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkNavigationType.h</key>
		<data>
		3fSnRUTotphysdDrdAvLMxLPW+k=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkProtocol.h</key>
		<data>
		FaTKQ3PKUnwjWoYivjhBcoWCvf8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkResolverRequestBuilding.h</key>
		<data>
		SXBVCFpg1usB96KY2Km7m8BU4PQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkResolving.h</key>
		<data>
		vW0iP2TWioh9F2xVFhjb96AWH/M=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkTargetCreating.h</key>
		<data>
		CSjhIDvr2Fy0xL6BAuVxNcPFzdg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkTargetProtocol.h</key>
		<data>
		KjpJOKaE7Hu4fZ/kcuexDfd7h/E=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkURL.h</key>
		<data>
		QygAk/iNI8C9zeo9e0YUgaIWY7g=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkURLCreating.h</key>
		<data>
		oN/+vlRMv1IvKkWrsh+VtDwu92U=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkURLKeys.h</key>
		<data>
		9Ke3NaIqqmJ0mx/UMz1Vw+/X9hA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkUtility.h</key>
		<data>
		CSxGCvSd3TFNsqT26linPXGDWM4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkVersion.h</key>
		<data>
		4lyPD97ZcXbRvhIigNtr3LGnzKo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinksBlock.h</key>
		<data>
		TPb6CeLZPTcgS7hT6GviE2JRz4k=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppOperationalDataType.h</key>
		<data>
		Yxnb0SsotnkBXyJBdPIv4DE+uuE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppStoreReceiptProviding.h</key>
		<data>
		YAWUu9gn0hfRwdt2dscR4Pjncb0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppURLSchemeProviding.h</key>
		<data>
		G7H5ArEaw56tAdukKkeFnHJW3yM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKApplicationActivating.h</key>
		<data>
		oHcGo1IxNL4whFjkCAqY0Bezu1A=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKApplicationLifecycleNotifications.h</key>
		<data>
		HZOemKjr7HIyL3ND764s1sIhRdA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKApplicationLifecycleObserving.h</key>
		<data>
		tM9oT+qXMwjCWeGffrZ/7pU9dhk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKApplicationObserving.h</key>
		<data>
		xJEwA88eDINbA4Kq2mRmWsoZVaE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKApplicationStateSetting.h</key>
		<data>
		fy5bOvCaeEMdMssTZs5062vJuLs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAuthenticationStatusUtility.h</key>
		<data>
		NzmrmWDBawgvUzFKUTG4Zq+D8PQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAuthenticationToken.h</key>
		<data>
		Hbt4TrpEQ1E7m0cPJVPJrvfOMYE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAuthenticationTokenProviding.h</key>
		<data>
		zDTRRbEFRongbmR9iUSO35PCbhs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAutoSetup.h</key>
		<data>
		ZrZsPjcuQHmtIb0VA/2JxYjo0Bk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIProtocol.h</key>
		<data>
		tLjX3wSZTxU3/edMWI3RtKPsv+A=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIProtocolType.h</key>
		<data>
		DblZg5KWlfoY7uhozPWbR8A+C+Q=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIRequest.h</key>
		<data>
		9p88MKTDxjMSnhRmD3tBUBpyKP0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIRequestCreating.h</key>
		<data>
		fSrgRjXiEf48iO5SW4tUzCK4Dfw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIRequestOpening.h</key>
		<data>
		0yprixA39qr6F++QUQmDaS+kI5c=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIRequestProtocol.h</key>
		<data>
		QvIJ69YhObUzXyvQTDogepJbLIY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIResponse.h</key>
		<data>
		0xHREU5UN9V2806Plbv9YnZCtvo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKButton.h</key>
		<data>
		nRauGR6uRcDNSlw5mNra+SReMhI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKButtonImpressionLogging.h</key>
		<data>
		LsVpQhR6smaHCPnF/ITEWiKGkKA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKClientTokenProviding.h</key>
		<data>
		W+JNLAn4YexFixxEQGR3Q9RH0KI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCodelessIndexer.h</key>
		<data>
		ua4gDNRjw0KAFXM4vqI1AxIhmgQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCodelessIndexing.h</key>
		<data>
		vvo1NJ1mTuIU5ZOEX1L9qWcyo+s=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKConstants.h</key>
		<data>
		D8KfnDhhBY1wExKSXz3swq16lTE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKContainerViewController.h</key>
		<data>
		U4pg9nowPmFOUC9fvIpLijlN6fg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKConversionValueUpdating.h</key>
		<data>
		fTxcQJg2+ZGuh6rqfJoDG5p6L94=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCoreKit-Swift.h</key>
		<data>
		6Q0IXgo7gt89R9ybn7xJCG359Lo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCoreKit.h</key>
		<data>
		9NSBO9571MvtFn98up/7ePg4lwo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCoreKitVersions.h</key>
		<data>
		/eqaMAzP93B16wXvakv2GDTKX0k=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCrashObserver.h</key>
		<data>
		i5LsrVb8OhFQsCQv02NggyWl4qk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCrashShield.h</key>
		<data>
		488qfAna0UU+EgR6ICrzMNPuE+Y=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDataProcessingOptionKey.h</key>
		<data>
		2KTIgp4tO9oDInYHDKE4PkSZtuo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDeviceInformationProviding.h</key>
		<data>
		AhMeLL4pfNP6NOlehkTmCGd908I=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDialogConfiguration.h</key>
		<data>
		BkHb91/H2f5w4nnsjzWvACP+LO8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDialogConfigurationMapBuilding.h</key>
		<data>
		3JigPqy5HGPxZ1EfvLl6K2r09G4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDomainConfiguration.h</key>
		<data>
		z42cVELCq+08IS97csqrsv0G3UM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDomainConfigurationManager.h</key>
		<data>
		vhxTjwLkFlXSayMOST5Du3FF1Ns=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDomainConfigurationProviding.h</key>
		<data>
		bAZKBepNb9Jwher3IYkkHIK+jM4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDomainHandler.h</key>
		<data>
		4pB423j54rXYv0k1XCxquIvgGII=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDynamicFrameworkLoaderProxy.h</key>
		<data>
		gQze+1wXFmg8HHDE0Ba4/AdlSbk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorConfiguration.h</key>
		<data>
		t9pebtrwIlsvol930VzK/Afs/8E=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorConfigurationProtocol.h</key>
		<data>
		rWzO75a4AGVSBUj9usd195pV1pw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorConfigurationProvider.h</key>
		<data>
		/NYL7EwesbS1Jj4zI5j/M36RrP4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorConfigurationProviding.h</key>
		<data>
		8oEeLY1N7YRaIXucUhviYIeCeUA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorCreating.h</key>
		<data>
		hSTHauBFdEYzYLgpazD8Nu2mbvA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorRecoveryAttempting.h</key>
		<data>
		woJpc0K7/L9yeXE4C+9IxIUoyNo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorRecoveryConfiguration.h</key>
		<data>
		ycgx9i+ACoXgcR7EKbYFYCxzlOg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorReporter.h</key>
		<data>
		mr/DzUsbnLVLYMNNABg7aaa45vw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorReporting.h</key>
		<data>
		QPo2mKvh+gBY7bUYxLIRori3P84=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKEventLogging.h</key>
		<data>
		DUSe3ilZQO8BqMsmrVYsaoF7LGA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKEventProcessing.h</key>
		<data>
		gp3AdngU5TgiopBE4rJMoR6ANSg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKEventsProcessing.h</key>
		<data>
		O75xhVmbjtVsiUJUjeGE1r788nM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKFeature.h</key>
		<data>
		Oh4B8Z0ZOzOtzlb70Z4TPxT1NQg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKFeatureChecking.h</key>
		<data>
		rYTkx84W03mL0rrno4sthw6poiM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKFeatureDisabling.h</key>
		<data>
		5RTMQMD5BH2IHczkFRDwcDa6YEc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKFeatureExtracting.h</key>
		<data>
		rLjAEFGlg1SFdt3LKE/Vyvpy4C4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKFeatureExtractor.h</key>
		<data>
		UpeejK9e6KddzNj4pJ/187IOAok=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGateKeeperManager.h</key>
		<data>
		UApODVoVBgYDmemuf8pq5KhSZ2A=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGateKeeperManaging.h</key>
		<data>
		MZUyM6YX2TAfccBQg6xiTBypZ18=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphErrorRecoveryProcessor.h</key>
		<data>
		aNj/uzA6QMePztxIxRll77xHLIg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequest.h</key>
		<data>
		W0+x27i8eT+6EMZ3lVcQXPKsAP0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestConnecting.h</key>
		<data>
		sAlo7za+Sf4zj8Xg+Wy83BUtRCE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestConnection.h</key>
		<data>
		NUnPpwpwUkVhUBU67o81VFaERfo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestConnectionDelegate.h</key>
		<data>
		FSHiVcHDpJTlfdfBczQNHtjDJ8s=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestConnectionFactory.h</key>
		<data>
		gAfT3DO/vuTTlEaJPEP8hL5P3Eo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestConnectionFactoryProtocol.h</key>
		<data>
		A26a5H79Zb1dRO6YHMFB4DbS+D8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestDataAttachment.h</key>
		<data>
		7vvCqPiZp4o6JKVaJJ4FP9XkXKE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestFactory.h</key>
		<data>
		lAwX1CKv5VHiJ07/xZZylICOdg4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestFactoryProtocol.h</key>
		<data>
		ydipmJZDsiXmT3/5DNEiX0gzsNs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestFlags.h</key>
		<data>
		Zas2ccUoNaCrjUffAdLC6TmKLWs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestHTTPMethod.h</key>
		<data>
		sF4WT7ko2ZXuQ91thBewwSb29Cc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestMetadata.h</key>
		<data>
		4ZYoDzErnijlAw/YdD1e2qQ3eDs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestPiggybackManaging.h</key>
		<data>
		RzvPVhBs2lDl2xH7PANCNXkswIE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestProtocol.h</key>
		<data>
		2WftYLHKj8D0QUQgx2F/+tCFX3k=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestQueue.h</key>
		<data>
		YazdAJ0rcrHmvRHvxaduooEWglg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKIAPDedupeProcessing.h</key>
		<data>
		jFfQ2uToisxlUwu5HAehBwtT5yc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKIAPFailedTransactionLogging.h</key>
		<data>
		3naly4poGndtl04IKJLRhIn0y58=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKIAPFailedTransactionLoggingCreating.h</key>
		<data>
		8f1KPldp/xKn7SdJ+extn1OcPWY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKIAPTransactionCaching.h</key>
		<data>
		EUOfrTb9ijRfv++ZOI1VjUF46+o=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKImpressionLoggerFactory.h</key>
		<data>
		nfa97DKHYMz7v0+pUDy3nlxrUYk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKImpressionLoggerFactoryProtocol.h</key>
		<data>
		3LEI96v+5GIEFysj3D1C/rG8ihw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKImpressionLogging.h</key>
		<data>
		wrrEayG2/hN5wUceRYRAU8HrXBk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKImpressionLoggingButton.h</key>
		<data>
		5jJQoQEgcqekG5Zl8U42hb9oI80=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKInstrumentManager.h</key>
		<data>
		b4LuHg6PamZ7IBBxVoOYpxuYERM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKIntegrityParametersProcessorProvider.h</key>
		<data>
		YAejrtcnEaVtGKUEB4ZJpyeATvQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKIntegrityProcessing.h</key>
		<data>
		87ZiOpw4U2tli0OR6o2FOZV0Szc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKInternalURLOpener.h</key>
		<data>
		FLV0q7bfHv0++Xz2yX93rch6WQk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKInternalUtility.h</key>
		<data>
		xVnxfL7eTB7Crbqhijh4imgTplo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKInternalUtilityProtocol.h</key>
		<data>
		/ZcaAKHHVRGsTO5fkpWQKXtwRlg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKKeychainStore.h</key>
		<data>
		VSfBFlzguwgAJhVey77PM3TiKzI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKKeychainStoreProtocol.h</key>
		<data>
		Vl4nIrUwT7cqcjwlXymbPkKUVDo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKKeychainStoreProviding.h</key>
		<data>
		u2GnomfI+oHCeoe6Ei9pG5quwVE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLocation.h</key>
		<data>
		lVldFN//gmPckkWOntm6/lMe0QE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLogger.h</key>
		<data>
		19xKJkbTRpwMoQwzaC6Nu+w5tME=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLoggerFactory.h</key>
		<data>
		oN6VhKse4/3rPEKNFGojETNkg58=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLogging.h</key>
		<data>
		/DbryGZcqEQACAktvCjPjV6SDG4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLoggingBehavior.h</key>
		<data>
		lNVc//dYNZlhHrZHLByspMT+TeY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLoggingNotifying.h</key>
		<data>
		ClxijYzHweoVytnlGNbT1n62Q2I=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLoginTooltip.h</key>
		<data>
		t9qlwGoUeyWhxDfXE8Ky6RnF/gg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMacCatalystDetermining.h</key>
		<data>
		Fy2901RMaq8v1KZ1nkSvVuhYSN8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMath.h</key>
		<data>
		rYMaHGq5qMdO0PMhIT/NeaWQMFE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMeasurementEventListener.h</key>
		<data>
		OVZSC/JIKFj+KgzAwt5biOqF7rI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMeasurementEventNames.h</key>
		<data>
		9XUuzXpHXt6PyqsDNvqKaCYOsaQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMetadataIndexer.h</key>
		<data>
		yvEMxozxtAwiiPOoouj17MOx38w=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMetadataIndexing.h</key>
		<data>
		0XbHu0BpTUjfkrPvYn1B9+H0Bww=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKModelManager.h</key>
		<data>
		UTvH2kBUyG0vy9bmR5tucIdzDMU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMutableCopying.h</key>
		<data>
		CdAKmAi79FHfugMUCBcou38XjyY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKNetworkErrorChecker.h</key>
		<data>
		lc4ltIsnGN0wefVKZeW3BTQqt8o=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKNetworkErrorChecking.h</key>
		<data>
		DQOOpk+tae6sTARv6zgYkUNQv+4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKObjectDecoding.h</key>
		<data>
		rsoqTN959edBc1dWsOJAk4SFajc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKOperatingSystemVersionComparing.h</key>
		<data>
		jd/kWOWeuMfcK4hF/tgU6AeS2yU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKPasteboard.h</key>
		<data>
		4+oJKTiDHC9gezS8CCuhdWHO2gs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKPaymentObserving.h</key>
		<data>
		2F/w1aia/WcWnKwAGffiRgYimWQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKPaymentProductRequestor.h</key>
		<data>
		ZQ6Btyy0z7ZiD/M9eX+7+KDRiLE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKPaymentProductRequestorCreating.h</key>
		<data>
		kofChLuS6UMw2biE8Lcu2yhsmt0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKProductAvailability.h</key>
		<data>
		4z6lAOLiyG+H6sMmuDzBXlrBO4Q=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKProductCondition.h</key>
		<data>
		p2M86R+0XjuIIHBALGh4qHhF0sg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKProductRequestFactory.h</key>
		<data>
		7BIaz0Ab6i62/Dq0UaZpdoNPhCs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKProductsRequestProtocols.h</key>
		<data>
		quFgypTeZ+2XNvJr6deEaYceClo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKProfileBlock.h</key>
		<data>
		m3b2HY43bVO+k7tLfgTUHlel3XM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKProfileNotifications.h</key>
		<data>
		p/v7YsyAewf4Zx5PgVOw0g9689k=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKRandom.h</key>
		<data>
		rqrUnE5WdNwaBw/Fm/Sauc2KceA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKRestrictiveDataFilterManager.h</key>
		<data>
		FhBIBCgefnNx6Hx9hBvjEQxlgTk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKRulesFromKeyProvider.h</key>
		<data>
		9/nqsZZ8Epe4NBnTPqEOptCNdzs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSKAdNetworkReporter.h</key>
		<data>
		N8+fSt031r3BemNKfykFVGaMU2g=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSKAdNetworkReporterV2.h</key>
		<data>
		t1JQiDuyttg76dcS901/oxfu6tQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKServerConfiguration.h</key>
		<data>
		ePai1Ct6rcU1fbdNFDdAFZgZLYo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKServerConfigurationManager.h</key>
		<data>
		MabAusZdKvvZ5Ny2wrhRTzvzRhY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKServerConfigurationProviding.h</key>
		<data>
		OVcy7K8/6LpbY7Ls9wBkMeiLOXc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSettingsLogging.h</key>
		<data>
		j4NKiO1um7BzI27sPShA+WNNV6E=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSourceApplicationTracking.h</key>
		<data>
		eyc/NF7kaz05PnojBKao9RoOkXo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSuggestedEventsIndexer.h</key>
		<data>
		eLTHBeEjKRaaEKDEDTrfz5o1+pE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSuggestedEventsIndexerProtocol.h</key>
		<data>
		Nlpu1MobSLVgHiTs61VhpCb8F84=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSwizzler.h</key>
		<data>
		PgutzEuZyjT9se/U24OTeoEMo1g=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSwizzling.h</key>
		<data>
		jMRYakKevWrqsOerYuQ9Fb1eCT4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKTimeSpentData.h</key>
		<data>
		Zgill5e+tNHLsGRpZ8nXHCk2iEc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKTimeSpentRecording.h</key>
		<data>
		NuI7oI0R+b4/s+KiUbS+MEn4x8k=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKTokenCaching.h</key>
		<data>
		cla0KYAtpSCy2GcXC++XdSQrxck=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKTokenStringProviding.h</key>
		<data>
		43wQbjzLSB+cSu9hTQZ4tJq24Mc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKTransactionObserving.h</key>
		<data>
		lZIW9x0Pm8LlHaUourw8qilnS7I=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKTransformer.h</key>
		<data>
		Ui2GFPACS7T6kK9LcCLcdJyCYyo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURL.h</key>
		<data>
		lg+hfhk8xZ7goFdbGZ8cVtCyNbw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLHosting.h</key>
		<data>
		O+RAgM4Uu49yrzKRLeqtv9FfVKE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLOpener.h</key>
		<data>
		/9/EmnpZWM6KdVyGGEI1NUvMbYk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLOpening.h</key>
		<data>
		0r8abqnzwBXvpV5PlCa/kQ+OdW8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLScheme.h</key>
		<data>
		36HfFNYLwWfRajDYFDJeNZe/evc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLSessionProxyFactory.h</key>
		<data>
		fkszrJOzG1OgfuDotJLKT74xw1w=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLSessionProxyProviding.h</key>
		<data>
		5Hely+G3LbaFH81TEA3aS8tH8Xk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLSessionProxying.h</key>
		<data>
		LIrnTeexPfrk+a2pllxOcLBqw+Q=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUnarchiverProvider.h</key>
		<data>
		mBxizULPLClFmFkzGvq7DZbedIU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUnarchiverProviding.h</key>
		<data>
		PU6uhik4cqPkhqWAmJpctYAS+vI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUserAgeRange.h</key>
		<data>
		paq4PxztwLvJ2EQKujqXLJfgjiA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUserDataPersisting.h</key>
		<data>
		eiIDv79NpGtVgJHZ+jovQsS4ZsU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUserDataStore.h</key>
		<data>
		iSw84njE3Q4fckPhhhUCpEMT4Uw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUserIDProviding.h</key>
		<data>
		1G3kOunQeQMavUujt30qZv1Oj98=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUserIdentifier.h</key>
		<data>
		EqrAGtLbcJRfQwl12songIdMMEE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUtility.h</key>
		<data>
		ACK+e48w6WLwZDhZT9VIaXDWTlk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKWebDialogDelegate.h</key>
		<data>
		oPLwzJ7KUTr8T5hq/c983EM1rfE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKWebDialogView.h</key>
		<data>
		EHOQKin9zYzG+gmc/LfBegKrvEE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKWebView.h</key>
		<data>
		jUhFO4/5Ly1VNml/cAryOayE4Ag=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKWebViewAppLinkResolver.h</key>
		<data>
		jEC9UH9Inm7DYqoFZv2qaN3Pe14=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKWebViewProviding.h</key>
		<data>
		6dBqgFJpP3qYvCEw7KHe/d5ieC8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/NSNotificationCenter+NotificationPosting.h</key>
		<data>
		4IqhFgtQjABnwxNo0vP4+5Q/hSU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/NSProcessInfo+MacCatalystDetermining.h</key>
		<data>
		P2PZ0k71jzSsWpQg6vH4TOHr6SU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/NSProcessInfo+OperatingSystemVersionComparing.h</key>
		<data>
		7p95y3GgCy4C7g+8xfGVXW4sr18=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/UIApplication+URLOpener.h</key>
		<data>
		N6kxbbjqI4XB8O7cVqNSb4VtaOs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/UIPasteboard+Pasteboard.h</key>
		<data>
		MKDA9GrF2o6+h3tbW9e9adSk23k=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/WKWebView+WebViewProtocol.h</key>
		<data>
		9AxT3+g6DPZx5q3rQWhRBMFJu64=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/_FBSDKNotificationPosting.h</key>
		<data>
		w1Gpzlc2q889xvFT3+ffPk2kwQU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/_FBSDKWindowFinding.h</key>
		<data>
		Gac9mAAYHny41SRhpW53CbfSo2s=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/__FBSDKLoggerCreating.h</key>
		<data>
		TTkFW5xuSEAOVq8O1cbEJ6/RgQg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<data>
		mnFamF7Gc38H2o21RWTTfDURTgE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		FeQzanpLn8r0q90XPcUQUGrbvpM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<data>
		Mj7ipDJanXwEXSMXDQw3r60NqAU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<data>
		FeQzanpLn8r0q90XPcUQUGrbvpM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<data>
		mnFamF7Gc38H2o21RWTTfDURTgE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		C8aofSExOxn2EKBH+rg+owyDMlg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<data>
		fsC9PZtjJRoRqtbec7KVYWkmuKc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<data>
		C8aofSExOxn2EKBH+rg+owyDMlg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		130ouJhMpZj1Z376F5gsUfoyxyc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/af.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Ze3vRmqRphRbVord7pGibO3fPjM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ar.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RU5U5p2+HVYWsFfSq227cr6oPa4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/bn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			062Bxy406hTTH31PhS52P1+Clqs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/cs.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mD61p8uMAsJbHWM5u43pGNZFp74=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/da.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jOzBKA4li06kg94bqglzoOLJD9M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/de.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nEbzzEMN0NGvB2cD2IpkSbWV6NM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/el.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/j4hlLRB9a6Bz377kSUlsx1c+Uk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/en.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			X2K5j4DkrzLxC/mhfhHoIXx5aRk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/en_GB.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			T3svla3GmadkuwnmQcwbAvlYi98=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/es.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8Bwe0QXPL21MImzsItkC041zC+s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/es_ES.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			c5FkRPxpv9zvua45N7qY/2Lnjc4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/fi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cpEfxM3HMb6H6HEuQEa8afs87bA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/fil.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QDnup8k5E1ovyik1vJG+36NnxRY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/fr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+kWYsXEYhJPWju3cpYvxA0fNPIU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/gu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XWLBntIiv6IgAicsZYD1u2/ZCNE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/he.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9CSDzNjRF5Zi4T8lQzEb4BFHrV8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/hi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3UoJKTt98zMp4MoNL+3/r86aYhQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/hr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lwcfpmcTECcYUnEE5hPsizpm3wg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/hu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IzpGhcaXvGa6oKaqvoknvGo7TtU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/id.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EC8VfATHFmjNpky43WLkcretWaY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/it.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xPK7qaQJKfzP3MZ0b3VJyO+gNMw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ja.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CKj+8eG+ZnwNE9X55i68+oS18Ok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/kn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EsrhFpmgw71P+TyRPtinsIr4hDQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ko.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NaTTNshzDqPGr0O5SATt5Ar/Ke4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ml.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mifHGo/qSi071/IqjLRUwRFs640=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/mr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			51s+e6B/gw6shnNo/gbRAQIunSc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ms.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			evzrkbxiAdaJ+exnQUlBrhVkZjE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/nb.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WMN/9oOghCcQVhMJD5HywSIIetM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/nl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cxRuVwKkGzmB3JnxcdYfSlMU+3Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/pa.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VuOm5+GxMy4d/JXxpVyHXLsdpBc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/pl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Hv1fBzZGisbifod43+Yl7abQPcA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/pt.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			70uwcrwkXYlkAfk5UaIrUQkWQI0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/pt_PT.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p9PvnBPstHznGo+paZZrb+pNMAc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ru.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bJltHFKTun/WYe6nQvEAlKM2dRM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/sk.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dDydplGYNYff6uM8Gft5tZqTXhI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/sv.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			N7VTH3m8nIqk04E4+7aclr63OsA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ta.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8ujSZal8Bl4w2uQzbRuH+iyid3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/te.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Dzl0I4eYOYud+sVAICju4r/2xwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/th.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i5VzACdOfJFncxh8cooghE3tIdc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/tr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RbNisbiWvkqWwj2C0LDgvQiLYUA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/vi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7KWOQQAFL0LBHlGYTuxqCzW1agQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/zh.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			socx43t1VI649J+QyCOEeZ/YOJQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/zh_Hant_HK.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VWqZ/8HYO+hnD4ncuigmcqdwTfo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/zh_Hant_TW.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rr2kvGmgmXEdbTLOH1CwY/7fGcc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/Info.plist</key>
		<data>
		e7MqrXgFKVo5L1GdLOdUWvBVRww=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<data>
		+4xzw+xvpntuK5AhSisGTSzsudg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/_CodeSignature/CodeResources</key>
		<data>
		lcGBgfUmKFsDuJqiAxchfu5Z+iI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Info.plist</key>
		<data>
		qG6Ty6t/qhX430RoQk8cuQ7fGXw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/DWARF/FBSDKCoreKit</key>
		<data>
		jSjzXhDRRa+ZRGqc/+eA5hrDffI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKCoreKit.yml</key>
		<data>
		zOwvdTFbbHiLf3xZGdvSA5POLuY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBSDKCoreKit.yml</key>
		<data>
		N+aQGPhjXF6J1hSKVDHQD6E7CGc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FBSDKCoreKit</key>
		<data>
		sK+DJRxMV2ggHW4FmnJhpKKEdfY=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/af.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Ze3vRmqRphRbVord7pGibO3fPjM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ar.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RU5U5p2+HVYWsFfSq227cr6oPa4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/bn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			062Bxy406hTTH31PhS52P1+Clqs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/cs.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mD61p8uMAsJbHWM5u43pGNZFp74=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/da.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jOzBKA4li06kg94bqglzoOLJD9M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/de.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nEbzzEMN0NGvB2cD2IpkSbWV6NM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/el.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/j4hlLRB9a6Bz377kSUlsx1c+Uk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/en.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			X2K5j4DkrzLxC/mhfhHoIXx5aRk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/en_GB.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			T3svla3GmadkuwnmQcwbAvlYi98=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/es.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8Bwe0QXPL21MImzsItkC041zC+s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/es_ES.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			c5FkRPxpv9zvua45N7qY/2Lnjc4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/fi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cpEfxM3HMb6H6HEuQEa8afs87bA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/fil.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QDnup8k5E1ovyik1vJG+36NnxRY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/fr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+kWYsXEYhJPWju3cpYvxA0fNPIU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/gu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XWLBntIiv6IgAicsZYD1u2/ZCNE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/he.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9CSDzNjRF5Zi4T8lQzEb4BFHrV8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/hi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3UoJKTt98zMp4MoNL+3/r86aYhQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/hr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lwcfpmcTECcYUnEE5hPsizpm3wg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/hu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IzpGhcaXvGa6oKaqvoknvGo7TtU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/id.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EC8VfATHFmjNpky43WLkcretWaY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/it.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xPK7qaQJKfzP3MZ0b3VJyO+gNMw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ja.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CKj+8eG+ZnwNE9X55i68+oS18Ok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/kn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EsrhFpmgw71P+TyRPtinsIr4hDQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ko.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NaTTNshzDqPGr0O5SATt5Ar/Ke4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ml.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mifHGo/qSi071/IqjLRUwRFs640=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/mr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			51s+e6B/gw6shnNo/gbRAQIunSc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ms.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			evzrkbxiAdaJ+exnQUlBrhVkZjE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/nb.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WMN/9oOghCcQVhMJD5HywSIIetM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/nl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cxRuVwKkGzmB3JnxcdYfSlMU+3Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pa.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VuOm5+GxMy4d/JXxpVyHXLsdpBc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Hv1fBzZGisbifod43+Yl7abQPcA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pt.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			70uwcrwkXYlkAfk5UaIrUQkWQI0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pt_PT.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p9PvnBPstHznGo+paZZrb+pNMAc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ru.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bJltHFKTun/WYe6nQvEAlKM2dRM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/sk.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dDydplGYNYff6uM8Gft5tZqTXhI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/sv.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			N7VTH3m8nIqk04E4+7aclr63OsA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ta.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8ujSZal8Bl4w2uQzbRuH+iyid3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/te.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Dzl0I4eYOYud+sVAICju4r/2xwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/th.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i5VzACdOfJFncxh8cooghE3tIdc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/tr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RbNisbiWvkqWwj2C0LDgvQiLYUA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/vi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7KWOQQAFL0LBHlGYTuxqCzW1agQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/zh.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			socx43t1VI649J+QyCOEeZ/YOJQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/zh_Hant_HK.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VWqZ/8HYO+hnD4ncuigmcqdwTfo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/zh_Hant_TW.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rr2kvGmgmXEdbTLOH1CwY/7fGcc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAEMManager.h</key>
		<data>
		Yj5aPKxlx+gRF70J6qlF3vXE4mk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKATEPublisherCreating.h</key>
		<data>
		HCKpYddhfbeOKPVi+PyxpQEl/Sw=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKATEPublisherFactory.h</key>
		<data>
		o4BqZ5pce+E7bPPdIkkE30cwTuI=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAccessToken.h</key>
		<data>
		52NScPmZOyhPECDbdwVz3SgFfnU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAccessTokenProviding.h</key>
		<data>
		8ssoFJOvhs7F3QcUOp076HcFlTA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAdvertiserIDProviding.h</key>
		<data>
		spPSWUz3WKLY8u+9ZEUgThNQTMY=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAdvertisingTrackingStatus.h</key>
		<data>
		s2/tT+xSXPH4xXaQ+yW41JtgT58=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppAvailabilityChecker.h</key>
		<data>
		Wyf9l4OPVlNw4rmgihSwsLLXekY=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventDropDetermining.h</key>
		<data>
		kuHhyp+2GLTjun17XfcnCE5AhEU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventName.h</key>
		<data>
		BOUOaSiuKli6Qe/v5ySB3h+M5X4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventParameterName.h</key>
		<data>
		PUlvfFayxBDoZ9T09/HpoVZ7Hgc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventParameterProduct.h</key>
		<data>
		r+KeRcYNIPkXBrMbyUHT2wI7s9Y=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventParameterValue.h</key>
		<data>
		040yhBlHKamIDlwNu0ImpgLTCqc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventParametersExtracting.h</key>
		<data>
		9pOtxV2/CHu04fcr39VsN+sfZ+s=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventUserDataType.h</key>
		<data>
		A3lEI7gxtNx4AHoXWeE0s7u1zK8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEvents.h</key>
		<data>
		lg573O7sH41xBg8lJrqEit3K/XQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfiguration.h</key>
		<data>
		HzthBYIOOFB3e2msa04M0rtBsRo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfigurationManager.h</key>
		<data>
		ANbr0kXkjHZjFTN/QfFH+kdJmYI=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfigurationProtocol.h</key>
		<data>
		v6/aYc8HvZlvKvREulESWKamY/g=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfigurationProviding.h</key>
		<data>
		Q6UHSFjjvfBNsl6lIgbm+E/xdvg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfiguring.h</key>
		<data>
		mHGiLeCpVuFdv3XqdFxv9vt7374=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsDeviceInfo.h</key>
		<data>
		SxQnYEiX9/yUxdAbzLI6d4eaegc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsFlushBehavior.h</key>
		<data>
		tGoQfvz6uAy5DjnhPnzX/Ai2vl4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsFlushReason.h</key>
		<data>
		CVXh0F8QRsx+pTFSV0t8r1QBBAQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsNotificationName.h</key>
		<data>
		y7c3PKWx/w77oSbeugClHIvTMS8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsParameterProcessing.h</key>
		<data>
		lSEA6VeT3RQ/9OIKlCWKuogT/fc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsReporter.h</key>
		<data>
		j2aR5673Kbc66xveJIq2h8rsKLk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsState.h</key>
		<data>
		WuHkw89fZW/JsqsG7+MqdROIuqU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsStateManager.h</key>
		<data>
		1npaFG2cN1/3fD1rD7gXkZmnUbI=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsStatePersisting.h</key>
		<data>
		oylwsOMHWTY2ag3rphOgwSAfz7c=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsStateProviding.h</key>
		<data>
		KzGafbBYik5zyGw1bm76HKS7Tc0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsUtility.h</key>
		<data>
		5vrPi5Z7t//eIoz9u9sQrS88V8E=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsUtilityProtocol.h</key>
		<data>
		w++q3FMjWosIk0QoN6chRjfvng8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkCreating.h</key>
		<data>
		nqhYr/0Yijl6YG6zjg69DkIZMdo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkEventPosting.h</key>
		<data>
		Unlyk7QfK8RMiwI6wrySVV1IWP4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkNavigationBlock.h</key>
		<data>
		1PU/0JKCKzJJV3POVzX50kAFA0o=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkNavigationType.h</key>
		<data>
		3fSnRUTotphysdDrdAvLMxLPW+k=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkProtocol.h</key>
		<data>
		FaTKQ3PKUnwjWoYivjhBcoWCvf8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkResolverRequestBuilding.h</key>
		<data>
		SXBVCFpg1usB96KY2Km7m8BU4PQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkResolving.h</key>
		<data>
		vW0iP2TWioh9F2xVFhjb96AWH/M=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkTargetCreating.h</key>
		<data>
		CSjhIDvr2Fy0xL6BAuVxNcPFzdg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkTargetProtocol.h</key>
		<data>
		KjpJOKaE7Hu4fZ/kcuexDfd7h/E=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkURL.h</key>
		<data>
		QygAk/iNI8C9zeo9e0YUgaIWY7g=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkURLCreating.h</key>
		<data>
		oN/+vlRMv1IvKkWrsh+VtDwu92U=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkURLKeys.h</key>
		<data>
		9Ke3NaIqqmJ0mx/UMz1Vw+/X9hA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkUtility.h</key>
		<data>
		CSxGCvSd3TFNsqT26linPXGDWM4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkVersion.h</key>
		<data>
		4lyPD97ZcXbRvhIigNtr3LGnzKo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinksBlock.h</key>
		<data>
		TPb6CeLZPTcgS7hT6GviE2JRz4k=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppOperationalDataType.h</key>
		<data>
		Yxnb0SsotnkBXyJBdPIv4DE+uuE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppStoreReceiptProviding.h</key>
		<data>
		YAWUu9gn0hfRwdt2dscR4Pjncb0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppURLSchemeProviding.h</key>
		<data>
		G7H5ArEaw56tAdukKkeFnHJW3yM=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKApplicationActivating.h</key>
		<data>
		oHcGo1IxNL4whFjkCAqY0Bezu1A=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKApplicationLifecycleNotifications.h</key>
		<data>
		HZOemKjr7HIyL3ND764s1sIhRdA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKApplicationLifecycleObserving.h</key>
		<data>
		tM9oT+qXMwjCWeGffrZ/7pU9dhk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKApplicationObserving.h</key>
		<data>
		xJEwA88eDINbA4Kq2mRmWsoZVaE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKApplicationStateSetting.h</key>
		<data>
		fy5bOvCaeEMdMssTZs5062vJuLs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAuthenticationStatusUtility.h</key>
		<data>
		NzmrmWDBawgvUzFKUTG4Zq+D8PQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAuthenticationToken.h</key>
		<data>
		Hbt4TrpEQ1E7m0cPJVPJrvfOMYE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAuthenticationTokenProviding.h</key>
		<data>
		zDTRRbEFRongbmR9iUSO35PCbhs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAutoSetup.h</key>
		<data>
		ZrZsPjcuQHmtIb0VA/2JxYjo0Bk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIProtocol.h</key>
		<data>
		tLjX3wSZTxU3/edMWI3RtKPsv+A=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIProtocolType.h</key>
		<data>
		DblZg5KWlfoY7uhozPWbR8A+C+Q=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequest.h</key>
		<data>
		9p88MKTDxjMSnhRmD3tBUBpyKP0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequestCreating.h</key>
		<data>
		fSrgRjXiEf48iO5SW4tUzCK4Dfw=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequestOpening.h</key>
		<data>
		0yprixA39qr6F++QUQmDaS+kI5c=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequestProtocol.h</key>
		<data>
		QvIJ69YhObUzXyvQTDogepJbLIY=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIResponse.h</key>
		<data>
		0xHREU5UN9V2806Plbv9YnZCtvo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKButton.h</key>
		<data>
		nRauGR6uRcDNSlw5mNra+SReMhI=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKButtonImpressionLogging.h</key>
		<data>
		LsVpQhR6smaHCPnF/ITEWiKGkKA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKClientTokenProviding.h</key>
		<data>
		W+JNLAn4YexFixxEQGR3Q9RH0KI=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCodelessIndexer.h</key>
		<data>
		ua4gDNRjw0KAFXM4vqI1AxIhmgQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCodelessIndexing.h</key>
		<data>
		vvo1NJ1mTuIU5ZOEX1L9qWcyo+s=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKConstants.h</key>
		<data>
		D8KfnDhhBY1wExKSXz3swq16lTE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKContainerViewController.h</key>
		<data>
		U4pg9nowPmFOUC9fvIpLijlN6fg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKConversionValueUpdating.h</key>
		<data>
		fTxcQJg2+ZGuh6rqfJoDG5p6L94=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCoreKit-Swift.h</key>
		<data>
		6Q0IXgo7gt89R9ybn7xJCG359Lo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCoreKit.h</key>
		<data>
		9NSBO9571MvtFn98up/7ePg4lwo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCoreKitVersions.h</key>
		<data>
		/eqaMAzP93B16wXvakv2GDTKX0k=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCrashObserver.h</key>
		<data>
		i5LsrVb8OhFQsCQv02NggyWl4qk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCrashShield.h</key>
		<data>
		488qfAna0UU+EgR6ICrzMNPuE+Y=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDataProcessingOptionKey.h</key>
		<data>
		2KTIgp4tO9oDInYHDKE4PkSZtuo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDeviceInformationProviding.h</key>
		<data>
		AhMeLL4pfNP6NOlehkTmCGd908I=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDialogConfiguration.h</key>
		<data>
		BkHb91/H2f5w4nnsjzWvACP+LO8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDialogConfigurationMapBuilding.h</key>
		<data>
		3JigPqy5HGPxZ1EfvLl6K2r09G4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDomainConfiguration.h</key>
		<data>
		z42cVELCq+08IS97csqrsv0G3UM=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDomainConfigurationManager.h</key>
		<data>
		vhxTjwLkFlXSayMOST5Du3FF1Ns=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDomainConfigurationProviding.h</key>
		<data>
		bAZKBepNb9Jwher3IYkkHIK+jM4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDomainHandler.h</key>
		<data>
		4pB423j54rXYv0k1XCxquIvgGII=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDynamicFrameworkLoaderProxy.h</key>
		<data>
		gQze+1wXFmg8HHDE0Ba4/AdlSbk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorConfiguration.h</key>
		<data>
		t9pebtrwIlsvol930VzK/Afs/8E=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorConfigurationProtocol.h</key>
		<data>
		rWzO75a4AGVSBUj9usd195pV1pw=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorConfigurationProvider.h</key>
		<data>
		/NYL7EwesbS1Jj4zI5j/M36RrP4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorConfigurationProviding.h</key>
		<data>
		8oEeLY1N7YRaIXucUhviYIeCeUA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorCreating.h</key>
		<data>
		hSTHauBFdEYzYLgpazD8Nu2mbvA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorRecoveryAttempting.h</key>
		<data>
		woJpc0K7/L9yeXE4C+9IxIUoyNo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorRecoveryConfiguration.h</key>
		<data>
		ycgx9i+ACoXgcR7EKbYFYCxzlOg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorReporter.h</key>
		<data>
		mr/DzUsbnLVLYMNNABg7aaa45vw=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorReporting.h</key>
		<data>
		QPo2mKvh+gBY7bUYxLIRori3P84=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKEventLogging.h</key>
		<data>
		DUSe3ilZQO8BqMsmrVYsaoF7LGA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKEventProcessing.h</key>
		<data>
		gp3AdngU5TgiopBE4rJMoR6ANSg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKEventsProcessing.h</key>
		<data>
		O75xhVmbjtVsiUJUjeGE1r788nM=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKFeature.h</key>
		<data>
		Oh4B8Z0ZOzOtzlb70Z4TPxT1NQg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKFeatureChecking.h</key>
		<data>
		rYTkx84W03mL0rrno4sthw6poiM=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKFeatureDisabling.h</key>
		<data>
		5RTMQMD5BH2IHczkFRDwcDa6YEc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKFeatureExtracting.h</key>
		<data>
		rLjAEFGlg1SFdt3LKE/Vyvpy4C4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKFeatureExtractor.h</key>
		<data>
		UpeejK9e6KddzNj4pJ/187IOAok=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGateKeeperManager.h</key>
		<data>
		UApODVoVBgYDmemuf8pq5KhSZ2A=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGateKeeperManaging.h</key>
		<data>
		MZUyM6YX2TAfccBQg6xiTBypZ18=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphErrorRecoveryProcessor.h</key>
		<data>
		aNj/uzA6QMePztxIxRll77xHLIg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequest.h</key>
		<data>
		W0+x27i8eT+6EMZ3lVcQXPKsAP0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnecting.h</key>
		<data>
		sAlo7za+Sf4zj8Xg+Wy83BUtRCE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnection.h</key>
		<data>
		NUnPpwpwUkVhUBU67o81VFaERfo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnectionDelegate.h</key>
		<data>
		FSHiVcHDpJTlfdfBczQNHtjDJ8s=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnectionFactory.h</key>
		<data>
		gAfT3DO/vuTTlEaJPEP8hL5P3Eo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnectionFactoryProtocol.h</key>
		<data>
		A26a5H79Zb1dRO6YHMFB4DbS+D8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestDataAttachment.h</key>
		<data>
		7vvCqPiZp4o6JKVaJJ4FP9XkXKE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestFactory.h</key>
		<data>
		lAwX1CKv5VHiJ07/xZZylICOdg4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestFactoryProtocol.h</key>
		<data>
		ydipmJZDsiXmT3/5DNEiX0gzsNs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestFlags.h</key>
		<data>
		Zas2ccUoNaCrjUffAdLC6TmKLWs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestHTTPMethod.h</key>
		<data>
		sF4WT7ko2ZXuQ91thBewwSb29Cc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestMetadata.h</key>
		<data>
		4ZYoDzErnijlAw/YdD1e2qQ3eDs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestPiggybackManaging.h</key>
		<data>
		RzvPVhBs2lDl2xH7PANCNXkswIE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestProtocol.h</key>
		<data>
		2WftYLHKj8D0QUQgx2F/+tCFX3k=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestQueue.h</key>
		<data>
		YazdAJ0rcrHmvRHvxaduooEWglg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKIAPDedupeProcessing.h</key>
		<data>
		jFfQ2uToisxlUwu5HAehBwtT5yc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKIAPFailedTransactionLogging.h</key>
		<data>
		3naly4poGndtl04IKJLRhIn0y58=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKIAPFailedTransactionLoggingCreating.h</key>
		<data>
		8f1KPldp/xKn7SdJ+extn1OcPWY=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKIAPTransactionCaching.h</key>
		<data>
		EUOfrTb9ijRfv++ZOI1VjUF46+o=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKImpressionLoggerFactory.h</key>
		<data>
		nfa97DKHYMz7v0+pUDy3nlxrUYk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKImpressionLoggerFactoryProtocol.h</key>
		<data>
		3LEI96v+5GIEFysj3D1C/rG8ihw=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKImpressionLogging.h</key>
		<data>
		wrrEayG2/hN5wUceRYRAU8HrXBk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKImpressionLoggingButton.h</key>
		<data>
		5jJQoQEgcqekG5Zl8U42hb9oI80=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKInstrumentManager.h</key>
		<data>
		b4LuHg6PamZ7IBBxVoOYpxuYERM=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKIntegrityParametersProcessorProvider.h</key>
		<data>
		YAejrtcnEaVtGKUEB4ZJpyeATvQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKIntegrityProcessing.h</key>
		<data>
		87ZiOpw4U2tli0OR6o2FOZV0Szc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKInternalURLOpener.h</key>
		<data>
		FLV0q7bfHv0++Xz2yX93rch6WQk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKInternalUtility.h</key>
		<data>
		xVnxfL7eTB7Crbqhijh4imgTplo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKInternalUtilityProtocol.h</key>
		<data>
		/ZcaAKHHVRGsTO5fkpWQKXtwRlg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKKeychainStore.h</key>
		<data>
		VSfBFlzguwgAJhVey77PM3TiKzI=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKKeychainStoreProtocol.h</key>
		<data>
		Vl4nIrUwT7cqcjwlXymbPkKUVDo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKKeychainStoreProviding.h</key>
		<data>
		u2GnomfI+oHCeoe6Ei9pG5quwVE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLocation.h</key>
		<data>
		lVldFN//gmPckkWOntm6/lMe0QE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLogger.h</key>
		<data>
		19xKJkbTRpwMoQwzaC6Nu+w5tME=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLoggerFactory.h</key>
		<data>
		oN6VhKse4/3rPEKNFGojETNkg58=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLogging.h</key>
		<data>
		/DbryGZcqEQACAktvCjPjV6SDG4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLoggingBehavior.h</key>
		<data>
		lNVc//dYNZlhHrZHLByspMT+TeY=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLoggingNotifying.h</key>
		<data>
		ClxijYzHweoVytnlGNbT1n62Q2I=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLoginTooltip.h</key>
		<data>
		t9qlwGoUeyWhxDfXE8Ky6RnF/gg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMacCatalystDetermining.h</key>
		<data>
		Fy2901RMaq8v1KZ1nkSvVuhYSN8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMath.h</key>
		<data>
		rYMaHGq5qMdO0PMhIT/NeaWQMFE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMeasurementEventListener.h</key>
		<data>
		OVZSC/JIKFj+KgzAwt5biOqF7rI=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMeasurementEventNames.h</key>
		<data>
		9XUuzXpHXt6PyqsDNvqKaCYOsaQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMetadataIndexer.h</key>
		<data>
		yvEMxozxtAwiiPOoouj17MOx38w=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMetadataIndexing.h</key>
		<data>
		0XbHu0BpTUjfkrPvYn1B9+H0Bww=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKModelManager.h</key>
		<data>
		UTvH2kBUyG0vy9bmR5tucIdzDMU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMutableCopying.h</key>
		<data>
		CdAKmAi79FHfugMUCBcou38XjyY=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKNetworkErrorChecker.h</key>
		<data>
		lc4ltIsnGN0wefVKZeW3BTQqt8o=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKNetworkErrorChecking.h</key>
		<data>
		DQOOpk+tae6sTARv6zgYkUNQv+4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKObjectDecoding.h</key>
		<data>
		rsoqTN959edBc1dWsOJAk4SFajc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKOperatingSystemVersionComparing.h</key>
		<data>
		jd/kWOWeuMfcK4hF/tgU6AeS2yU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKPasteboard.h</key>
		<data>
		4+oJKTiDHC9gezS8CCuhdWHO2gs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKPaymentObserving.h</key>
		<data>
		2F/w1aia/WcWnKwAGffiRgYimWQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKPaymentProductRequestor.h</key>
		<data>
		ZQ6Btyy0z7ZiD/M9eX+7+KDRiLE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKPaymentProductRequestorCreating.h</key>
		<data>
		kofChLuS6UMw2biE8Lcu2yhsmt0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKProductAvailability.h</key>
		<data>
		4z6lAOLiyG+H6sMmuDzBXlrBO4Q=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKProductCondition.h</key>
		<data>
		p2M86R+0XjuIIHBALGh4qHhF0sg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKProductRequestFactory.h</key>
		<data>
		7BIaz0Ab6i62/Dq0UaZpdoNPhCs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKProductsRequestProtocols.h</key>
		<data>
		quFgypTeZ+2XNvJr6deEaYceClo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKProfileBlock.h</key>
		<data>
		m3b2HY43bVO+k7tLfgTUHlel3XM=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKProfileNotifications.h</key>
		<data>
		p/v7YsyAewf4Zx5PgVOw0g9689k=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKRandom.h</key>
		<data>
		rqrUnE5WdNwaBw/Fm/Sauc2KceA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKRestrictiveDataFilterManager.h</key>
		<data>
		FhBIBCgefnNx6Hx9hBvjEQxlgTk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKRulesFromKeyProvider.h</key>
		<data>
		9/nqsZZ8Epe4NBnTPqEOptCNdzs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSKAdNetworkReporter.h</key>
		<data>
		N8+fSt031r3BemNKfykFVGaMU2g=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSKAdNetworkReporterV2.h</key>
		<data>
		t1JQiDuyttg76dcS901/oxfu6tQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKServerConfiguration.h</key>
		<data>
		ePai1Ct6rcU1fbdNFDdAFZgZLYo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKServerConfigurationManager.h</key>
		<data>
		MabAusZdKvvZ5Ny2wrhRTzvzRhY=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKServerConfigurationProviding.h</key>
		<data>
		OVcy7K8/6LpbY7Ls9wBkMeiLOXc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSettingsLogging.h</key>
		<data>
		j4NKiO1um7BzI27sPShA+WNNV6E=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSourceApplicationTracking.h</key>
		<data>
		eyc/NF7kaz05PnojBKao9RoOkXo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSuggestedEventsIndexer.h</key>
		<data>
		eLTHBeEjKRaaEKDEDTrfz5o1+pE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSuggestedEventsIndexerProtocol.h</key>
		<data>
		Nlpu1MobSLVgHiTs61VhpCb8F84=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSwizzler.h</key>
		<data>
		PgutzEuZyjT9se/U24OTeoEMo1g=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSwizzling.h</key>
		<data>
		jMRYakKevWrqsOerYuQ9Fb1eCT4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKTimeSpentData.h</key>
		<data>
		Zgill5e+tNHLsGRpZ8nXHCk2iEc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKTimeSpentRecording.h</key>
		<data>
		NuI7oI0R+b4/s+KiUbS+MEn4x8k=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKTokenCaching.h</key>
		<data>
		cla0KYAtpSCy2GcXC++XdSQrxck=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKTokenStringProviding.h</key>
		<data>
		43wQbjzLSB+cSu9hTQZ4tJq24Mc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKTransactionObserving.h</key>
		<data>
		lZIW9x0Pm8LlHaUourw8qilnS7I=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKTransformer.h</key>
		<data>
		Ui2GFPACS7T6kK9LcCLcdJyCYyo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURL.h</key>
		<data>
		lg+hfhk8xZ7goFdbGZ8cVtCyNbw=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLHosting.h</key>
		<data>
		O+RAgM4Uu49yrzKRLeqtv9FfVKE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLOpener.h</key>
		<data>
		/9/EmnpZWM6KdVyGGEI1NUvMbYk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLOpening.h</key>
		<data>
		0r8abqnzwBXvpV5PlCa/kQ+OdW8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLScheme.h</key>
		<data>
		36HfFNYLwWfRajDYFDJeNZe/evc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLSessionProxyFactory.h</key>
		<data>
		fkszrJOzG1OgfuDotJLKT74xw1w=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLSessionProxyProviding.h</key>
		<data>
		5Hely+G3LbaFH81TEA3aS8tH8Xk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLSessionProxying.h</key>
		<data>
		LIrnTeexPfrk+a2pllxOcLBqw+Q=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUnarchiverProvider.h</key>
		<data>
		mBxizULPLClFmFkzGvq7DZbedIU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUnarchiverProviding.h</key>
		<data>
		PU6uhik4cqPkhqWAmJpctYAS+vI=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUserAgeRange.h</key>
		<data>
		paq4PxztwLvJ2EQKujqXLJfgjiA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUserDataPersisting.h</key>
		<data>
		eiIDv79NpGtVgJHZ+jovQsS4ZsU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUserDataStore.h</key>
		<data>
		iSw84njE3Q4fckPhhhUCpEMT4Uw=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUserIDProviding.h</key>
		<data>
		1G3kOunQeQMavUujt30qZv1Oj98=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUserIdentifier.h</key>
		<data>
		EqrAGtLbcJRfQwl12songIdMMEE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUtility.h</key>
		<data>
		ACK+e48w6WLwZDhZT9VIaXDWTlk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKWebDialogDelegate.h</key>
		<data>
		oPLwzJ7KUTr8T5hq/c983EM1rfE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKWebDialogView.h</key>
		<data>
		EHOQKin9zYzG+gmc/LfBegKrvEE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKWebView.h</key>
		<data>
		jUhFO4/5Ly1VNml/cAryOayE4Ag=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKWebViewAppLinkResolver.h</key>
		<data>
		jEC9UH9Inm7DYqoFZv2qaN3Pe14=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKWebViewProviding.h</key>
		<data>
		6dBqgFJpP3qYvCEw7KHe/d5ieC8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/NSNotificationCenter+NotificationPosting.h</key>
		<data>
		4IqhFgtQjABnwxNo0vP4+5Q/hSU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/NSProcessInfo+MacCatalystDetermining.h</key>
		<data>
		P2PZ0k71jzSsWpQg6vH4TOHr6SU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/NSProcessInfo+OperatingSystemVersionComparing.h</key>
		<data>
		7p95y3GgCy4C7g+8xfGVXW4sr18=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/UIApplication+URLOpener.h</key>
		<data>
		N6kxbbjqI4XB8O7cVqNSb4VtaOs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/UIPasteboard+Pasteboard.h</key>
		<data>
		MKDA9GrF2o6+h3tbW9e9adSk23k=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/WKWebView+WebViewProtocol.h</key>
		<data>
		9AxT3+g6DPZx5q3rQWhRBMFJu64=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/_FBSDKNotificationPosting.h</key>
		<data>
		w1Gpzlc2q889xvFT3+ffPk2kwQU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/_FBSDKWindowFinding.h</key>
		<data>
		Gac9mAAYHny41SRhpW53CbfSo2s=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/__FBSDKLoggerCreating.h</key>
		<data>
		TTkFW5xuSEAOVq8O1cbEJ6/RgQg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Info.plist</key>
		<data>
		QUelzWz7dqMd+QoK8TNxFAkreF8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		OkoPZtcS5lu+Hk/2Cmm3NkUC1LA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		VOcf6Ms+U2Dy9LSAiruhQJI4zX0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		sGE3AP/1KVSnPWfojA5wQ3t0qB0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		VOcf6Ms+U2Dy9LSAiruhQJI4zX0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		OkoPZtcS5lu+Hk/2Cmm3NkUC1LA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		q9X/As+m8XdKb41fIQdGV52eO7Y=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		NvjKwhBscIDrP7g81PSeDrj+svw=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		q9X/As+m8XdKb41fIQdGV52eO7Y=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/module.modulemap</key>
		<data>
		130ouJhMpZj1Z376F5gsUfoyxyc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/PrivacyInfo.xcprivacy</key>
		<data>
		+4xzw+xvpntuK5AhSisGTSzsudg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/_CodeSignature/CodeResources</key>
		<data>
		GZ71Ja+PkhoMnlUakDMQn3MThQ0=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Info.plist</key>
		<data>
		qG6Ty6t/qhX430RoQk8cuQ7fGXw=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/DWARF/FBSDKCoreKit</key>
		<data>
		wCSnuzPKIzQqOHyFxHGQOr2mbw8=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKCoreKit.yml</key>
		<data>
		awyy358znRrie4VIrALKpm6yD+U=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBSDKCoreKit.yml</key>
		<data>
		PiCM7UxuklwFMb2/bGt1cC3trl0=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>LICENSE</key>
		<dict>
			<key>hash</key>
			<data>
			42oX9oAD1yj/wA6aqkh1wyx+qqA=
			</data>
			<key>hash2</key>
			<data>
			JGiNyKThXtEPUCL2A80E+FzHN+UTW+RkFoApZE8iHm8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FBSDKCoreKit</key>
		<dict>
			<key>hash</key>
			<data>
			SEBBIHt3XpCO9+wPbihgY/+8+Q8=
			</data>
			<key>hash2</key>
			<data>
			Zg7T0LnhxWzoDKysCFTcAO48jJKZe5h4XVoH4TCBdXg=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/af.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Ze3vRmqRphRbVord7pGibO3fPjM=
			</data>
			<key>hash2</key>
			<data>
			aCtbnATpHK8Vi9pb9hBnXBBY6+zqsfeZF+F4H8aujwc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ar.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RU5U5p2+HVYWsFfSq227cr6oPa4=
			</data>
			<key>hash2</key>
			<data>
			s1aJgl1DJYHZsgUSZhMYINgeVIIi1NDjhZ3KkkA6SHY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/bn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			062Bxy406hTTH31PhS52P1+Clqs=
			</data>
			<key>hash2</key>
			<data>
			RYmfdmkMCNbGahvM9NYcmYiySFg8EvXZ2GUMXlSBWN8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/cs.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mD61p8uMAsJbHWM5u43pGNZFp74=
			</data>
			<key>hash2</key>
			<data>
			JIHHymPdIttC0qC3iZx89N8HLHiCT2sFHJ7U09zZQzg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/da.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jOzBKA4li06kg94bqglzoOLJD9M=
			</data>
			<key>hash2</key>
			<data>
			rQLYTaF5jkBlY9WlSinHBEtLGJwU8VJyBAXbMOS1FBw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/de.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nEbzzEMN0NGvB2cD2IpkSbWV6NM=
			</data>
			<key>hash2</key>
			<data>
			jaRdBaX5SEMhMJHA4X7DSIX/mjEKUdH09tKg+e1jkuU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/el.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/j4hlLRB9a6Bz377kSUlsx1c+Uk=
			</data>
			<key>hash2</key>
			<data>
			KH/h3KcObMmRfS3vpIMRtbDX/tuUrUrwt3agnfZKKf4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/en.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			X2K5j4DkrzLxC/mhfhHoIXx5aRk=
			</data>
			<key>hash2</key>
			<data>
			eNYbN/zs5JhZzRo97ltQFilMcvfV7MUlGzmUK8JCOK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/en_GB.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			T3svla3GmadkuwnmQcwbAvlYi98=
			</data>
			<key>hash2</key>
			<data>
			Ehn5jlEk9nHkql1/teEwOXPdlmM0yqLQMkVq95W1lU0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/es.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8Bwe0QXPL21MImzsItkC041zC+s=
			</data>
			<key>hash2</key>
			<data>
			X1CMNL7b4eteT/mu2U4kGGxt77m8h6i9IZlZ+3aXBMc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/es_ES.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			c5FkRPxpv9zvua45N7qY/2Lnjc4=
			</data>
			<key>hash2</key>
			<data>
			AWSgHyoVXbBxs9n2Ty2XM6gGClpTvrWQheR7USy3aHo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/fi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cpEfxM3HMb6H6HEuQEa8afs87bA=
			</data>
			<key>hash2</key>
			<data>
			n0r9aE/uabG+yQ+CTicKtVH2R8NXpmfBfW3pEneb2Gc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/fil.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QDnup8k5E1ovyik1vJG+36NnxRY=
			</data>
			<key>hash2</key>
			<data>
			SrYgrW5xGmEgfJvEg2BMqfBsRxYa1koP9r1J1lyMd7A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/fr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+kWYsXEYhJPWju3cpYvxA0fNPIU=
			</data>
			<key>hash2</key>
			<data>
			5puGnwqyUU34y4BDDWnmrqL9cbHAosqA27c/VZvrQlE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/gu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XWLBntIiv6IgAicsZYD1u2/ZCNE=
			</data>
			<key>hash2</key>
			<data>
			jDOx6ZOVtvUbyWYwon1C1cRzUnAY3r3JmiFTNgJd30M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/he.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9CSDzNjRF5Zi4T8lQzEb4BFHrV8=
			</data>
			<key>hash2</key>
			<data>
			Fg41Nq6D6Yg8CCJhCwqZd3eMLjs/NepPFMy2rB2A+vg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/hi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3UoJKTt98zMp4MoNL+3/r86aYhQ=
			</data>
			<key>hash2</key>
			<data>
			7MIeteCKjPKu7GszF+BrRJqpVxduoAXlg6drRpQPceQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/hr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lwcfpmcTECcYUnEE5hPsizpm3wg=
			</data>
			<key>hash2</key>
			<data>
			iD8FRdiUx28qJEFvtvyQRbOWzsTPikwXwrOEMF5pAWw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/hu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IzpGhcaXvGa6oKaqvoknvGo7TtU=
			</data>
			<key>hash2</key>
			<data>
			GtWEiES81lZil10Skylfrn88mjj2e3NcvieRmzh43Qw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/id.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EC8VfATHFmjNpky43WLkcretWaY=
			</data>
			<key>hash2</key>
			<data>
			Eq9a4nxlLu+lwpJLYXEf46sjwQW6bi3N3y7N8UycWNQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/it.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xPK7qaQJKfzP3MZ0b3VJyO+gNMw=
			</data>
			<key>hash2</key>
			<data>
			kDpMVTknyBjiPj2EtWrRNdRGBZtmXRVPM5ldzU+2X9g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ja.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CKj+8eG+ZnwNE9X55i68+oS18Ok=
			</data>
			<key>hash2</key>
			<data>
			9xk36xdQPl2SRgXbWGMBxwjjzkVXUxtiYtkidSYwVNQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/kn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EsrhFpmgw71P+TyRPtinsIr4hDQ=
			</data>
			<key>hash2</key>
			<data>
			8NH+f4tH0tzd5k5LyfZJ14DN64SkSURQ5CfyFJf/Gmc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ko.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NaTTNshzDqPGr0O5SATt5Ar/Ke4=
			</data>
			<key>hash2</key>
			<data>
			C2sb15n2T4Ad64dDZGVBPhfJ+aew0h2WCmSN6SX8IjQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ml.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mifHGo/qSi071/IqjLRUwRFs640=
			</data>
			<key>hash2</key>
			<data>
			gHIBTSCHftU0BQWZOGcD5td1wykq7tQA+mKClgUkYHM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/mr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			51s+e6B/gw6shnNo/gbRAQIunSc=
			</data>
			<key>hash2</key>
			<data>
			fn0JKkKO/dH0bVur9VfzWntl8ls0yoY8YjgvYr/XsLw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ms.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			evzrkbxiAdaJ+exnQUlBrhVkZjE=
			</data>
			<key>hash2</key>
			<data>
			nNTZclYH1fxv1+Mch6eJbzgnzbwYRtH9f9Vtu+Le3dk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/nb.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WMN/9oOghCcQVhMJD5HywSIIetM=
			</data>
			<key>hash2</key>
			<data>
			QSYAJw601nnnqakR434bP6yeMjFPWZsx8ypSv68A13A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/nl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cxRuVwKkGzmB3JnxcdYfSlMU+3Q=
			</data>
			<key>hash2</key>
			<data>
			yNnGqC//Orb49RDL1a0nOau+xDgkzinH4LUqhsqqkEQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pa.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VuOm5+GxMy4d/JXxpVyHXLsdpBc=
			</data>
			<key>hash2</key>
			<data>
			bRVgGo+3BK4SwH5mPgjOjwn/mV3H6X+N2aAccxRdrdY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Hv1fBzZGisbifod43+Yl7abQPcA=
			</data>
			<key>hash2</key>
			<data>
			hEObnVN8x0e7V7SEKVMLd2+36sqDjInp/lw2GBlVDv8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pt.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			70uwcrwkXYlkAfk5UaIrUQkWQI0=
			</data>
			<key>hash2</key>
			<data>
			fr0opMxcbe0rfDIeWfWEevhQ3S5ydVBvEE5yPFYhBw8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pt_PT.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p9PvnBPstHznGo+paZZrb+pNMAc=
			</data>
			<key>hash2</key>
			<data>
			meQ3C7YBEw7vLepXd6Ck7W8869A1xgLgptLl84sgKhk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ru.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bJltHFKTun/WYe6nQvEAlKM2dRM=
			</data>
			<key>hash2</key>
			<data>
			tLbG0XkAh2lBT62Y0q08V7HUq1yB/QWlDKQ66pvXAtU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/sk.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dDydplGYNYff6uM8Gft5tZqTXhI=
			</data>
			<key>hash2</key>
			<data>
			pNDFOu2O93RtAOzuyC4q2d8DP6wZuffCiHfxEIolnpU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/sv.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			N7VTH3m8nIqk04E4+7aclr63OsA=
			</data>
			<key>hash2</key>
			<data>
			4HoEVPvwpF9IR063NtuNj8j9VEshT8JjvyTRGfaZbKw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ta.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8ujSZal8Bl4w2uQzbRuH+iyid3o=
			</data>
			<key>hash2</key>
			<data>
			l7aWKjZkBRjMSzn2T6GE21BMAi7DPKsf48+MQ/itkbo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/te.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Dzl0I4eYOYud+sVAICju4r/2xwk=
			</data>
			<key>hash2</key>
			<data>
			71qAVzkjqD6kmUs/nmYSfTlJv1FVVG9R4MKcNw3We4k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/th.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i5VzACdOfJFncxh8cooghE3tIdc=
			</data>
			<key>hash2</key>
			<data>
			Gt+X0AF2SJdujyvtLTzRvcWYWA6VH8PZRE0EcpMy0Ws=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/tr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RbNisbiWvkqWwj2C0LDgvQiLYUA=
			</data>
			<key>hash2</key>
			<data>
			MbpGbjBd1dHuUbE6jB8W1a+0xEj5ODzVz6yaQtCidqk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/vi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7KWOQQAFL0LBHlGYTuxqCzW1agQ=
			</data>
			<key>hash2</key>
			<data>
			3KTxqBqodalyJ3jdjQwlZ+RPQbYNJJHRWjXgy1lJSyg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/zh.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			socx43t1VI649J+QyCOEeZ/YOJQ=
			</data>
			<key>hash2</key>
			<data>
			eXBsqwueqa9PpbpzHtzpzN3ztNOYTwY4vCTqsUFhQC0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/zh_Hant_HK.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VWqZ/8HYO+hnD4ncuigmcqdwTfo=
			</data>
			<key>hash2</key>
			<data>
			pnbR7cjq5w2nZG17PXaHRT+19mzFJ44tIEnJCoUqrvo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/zh_Hant_TW.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rr2kvGmgmXEdbTLOH1CwY/7fGcc=
			</data>
			<key>hash2</key>
			<data>
			2XHa9tLW3cf0L3byDYr/+6qjFMSrS3i+xwESUxC9pZc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAEMManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			Yj5aPKxlx+gRF70J6qlF3vXE4mk=
			</data>
			<key>hash2</key>
			<data>
			RRWu3ofQe/GuFslThfnBEdL+ks4YVUnKAckD+t6Kw78=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKATEPublisherCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			HCKpYddhfbeOKPVi+PyxpQEl/Sw=
			</data>
			<key>hash2</key>
			<data>
			4DAoEwvpFC6T6PC5EJZwpa4Ey+A8LZo6rz/k/078BUM=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKATEPublisherFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			o4BqZ5pce+E7bPPdIkkE30cwTuI=
			</data>
			<key>hash2</key>
			<data>
			cW1M/vPKiBNWmJ+XzS8iFtLl9s1+5ahOBCQWQ33lFfA=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAccessToken.h</key>
		<dict>
			<key>hash</key>
			<data>
			52NScPmZOyhPECDbdwVz3SgFfnU=
			</data>
			<key>hash2</key>
			<data>
			oFHJEVxEJrVA9Y7bNOjGQZ28+CrbY+rbVTIA+GC3LWs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAccessTokenProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			8ssoFJOvhs7F3QcUOp076HcFlTA=
			</data>
			<key>hash2</key>
			<data>
			ttnRwTC/lx3T/wwxDPI/n0tOC8tMavvDSKnOHYtwmJ0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAdvertiserIDProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			spPSWUz3WKLY8u+9ZEUgThNQTMY=
			</data>
			<key>hash2</key>
			<data>
			3TS/lxGjmXQaqCF4fgbFoM/w3nXircZe7UfQRibtXh0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAdvertisingTrackingStatus.h</key>
		<dict>
			<key>hash</key>
			<data>
			s2/tT+xSXPH4xXaQ+yW41JtgT58=
			</data>
			<key>hash2</key>
			<data>
			pj5HBFKU2AJRVkryxLDxsNyV+Hq0vhsL7ESLeXA7gco=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppAvailabilityChecker.h</key>
		<dict>
			<key>hash</key>
			<data>
			Wyf9l4OPVlNw4rmgihSwsLLXekY=
			</data>
			<key>hash2</key>
			<data>
			WCKAfRQSLZ76amGNcy7D85Zr0FqbK3yqgD2x9Q2KMVc=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventDropDetermining.h</key>
		<dict>
			<key>hash</key>
			<data>
			kuHhyp+2GLTjun17XfcnCE5AhEU=
			</data>
			<key>hash2</key>
			<data>
			L9oIxJipjM/MQk2hCZ2frITY+t5sCEXfXwbMQZJm5P4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventName.h</key>
		<dict>
			<key>hash</key>
			<data>
			BOUOaSiuKli6Qe/v5ySB3h+M5X4=
			</data>
			<key>hash2</key>
			<data>
			gkInDcjiBNASc1T6fLvTisSC622V3ONdX5cFYRNhq4Q=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventParameterName.h</key>
		<dict>
			<key>hash</key>
			<data>
			PUlvfFayxBDoZ9T09/HpoVZ7Hgc=
			</data>
			<key>hash2</key>
			<data>
			dourp/NLYssFkQ5Dg6AxyxLObwl5JhALDaFz6lFf9Lc=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventParameterProduct.h</key>
		<dict>
			<key>hash</key>
			<data>
			r+KeRcYNIPkXBrMbyUHT2wI7s9Y=
			</data>
			<key>hash2</key>
			<data>
			FIPXmw+JMv7bBSbF0zhfVC2Ib03Sx9JYrwjlNp1XInI=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventParameterValue.h</key>
		<dict>
			<key>hash</key>
			<data>
			040yhBlHKamIDlwNu0ImpgLTCqc=
			</data>
			<key>hash2</key>
			<data>
			Q2olYJJI/DN976h566Nwy3D+obhtAQAKHOJ5lKeTfm8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventParametersExtracting.h</key>
		<dict>
			<key>hash</key>
			<data>
			9pOtxV2/CHu04fcr39VsN+sfZ+s=
			</data>
			<key>hash2</key>
			<data>
			BdY6x122D6MPzxunayL68o6siMnk1pbG9+SIttD3Er0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventUserDataType.h</key>
		<dict>
			<key>hash</key>
			<data>
			A3lEI7gxtNx4AHoXWeE0s7u1zK8=
			</data>
			<key>hash2</key>
			<data>
			7pjsRgcXBeV8tJeLjrQvQ/3ZBmzY9k086Z46TsArMag=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEvents.h</key>
		<dict>
			<key>hash</key>
			<data>
			lg573O7sH41xBg8lJrqEit3K/XQ=
			</data>
			<key>hash2</key>
			<data>
			UXJezVQ0cuGeeCKQaxUnK81y4yXJT55f4K9dAPNv5/A=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			HzthBYIOOFB3e2msa04M0rtBsRo=
			</data>
			<key>hash2</key>
			<data>
			CPd+ImdtELN8bwE7ctjAasJyW7VT89Q5RQaUxTFXz6M=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfigurationManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			ANbr0kXkjHZjFTN/QfFH+kdJmYI=
			</data>
			<key>hash2</key>
			<data>
			9Hd2vCTkBhdhGl2O0GrDnmf5Dk+9LGUCubPXDNOSj9M=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfigurationProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			v6/aYc8HvZlvKvREulESWKamY/g=
			</data>
			<key>hash2</key>
			<data>
			HNsLJH7DmlMk6E5VETGgJDTdrBxFh1tmIqN2HupuauQ=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfigurationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			Q6UHSFjjvfBNsl6lIgbm+E/xdvg=
			</data>
			<key>hash2</key>
			<data>
			Sxr+A8kVMuZThRbBh4I4DH8boozZFBrhAwev32Vh5Hw=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfiguring.h</key>
		<dict>
			<key>hash</key>
			<data>
			mHGiLeCpVuFdv3XqdFxv9vt7374=
			</data>
			<key>hash2</key>
			<data>
			CYre/9YuQpKmC/OI/41C1Pu8lysIfIKLoct7rQEcEdc=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsDeviceInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			SxQnYEiX9/yUxdAbzLI6d4eaegc=
			</data>
			<key>hash2</key>
			<data>
			c8TYlTd79b+8iKRVt8ssLF77TZo5fhxvf5ChNBNfMhc=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsFlushBehavior.h</key>
		<dict>
			<key>hash</key>
			<data>
			tGoQfvz6uAy5DjnhPnzX/Ai2vl4=
			</data>
			<key>hash2</key>
			<data>
			iUxqEKL4pmF7f47Qul2Oe8QI0MjDPnOn3VhWjVQWe90=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsFlushReason.h</key>
		<dict>
			<key>hash</key>
			<data>
			CVXh0F8QRsx+pTFSV0t8r1QBBAQ=
			</data>
			<key>hash2</key>
			<data>
			z4aL5e8KYk3I9292BbvFVfL7VvGSXi8r1ULbNY9qC+o=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsNotificationName.h</key>
		<dict>
			<key>hash</key>
			<data>
			y7c3PKWx/w77oSbeugClHIvTMS8=
			</data>
			<key>hash2</key>
			<data>
			7JmzpHhHPCXS4WcGYrhN2g1u5YXUgR/ltWdRyfv8l0I=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsParameterProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			lSEA6VeT3RQ/9OIKlCWKuogT/fc=
			</data>
			<key>hash2</key>
			<data>
			pCHyiWkvDhXpHUHpcTQYsXWk6DNMxor1yUXbLb1zJoM=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsReporter.h</key>
		<dict>
			<key>hash</key>
			<data>
			j2aR5673Kbc66xveJIq2h8rsKLk=
			</data>
			<key>hash2</key>
			<data>
			wg1FOXDGXyIdKy7bYj+hrqcKxtTG/paGmTO6QAjIyZ0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsState.h</key>
		<dict>
			<key>hash</key>
			<data>
			WuHkw89fZW/JsqsG7+MqdROIuqU=
			</data>
			<key>hash2</key>
			<data>
			R6QD42bulvhwFoDu6RdyZBgBVdXMRl4ysZHuVUj1g9I=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsStateManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			1npaFG2cN1/3fD1rD7gXkZmnUbI=
			</data>
			<key>hash2</key>
			<data>
			QFynSzFRj8ZNs48YxDNrvOzNjk/5/TKdEvOpAV6gfAo=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsStatePersisting.h</key>
		<dict>
			<key>hash</key>
			<data>
			oylwsOMHWTY2ag3rphOgwSAfz7c=
			</data>
			<key>hash2</key>
			<data>
			89C3WF44BG25E9QGywyeFSIhzSN3vIZ8j0Cf1zBc7Nw=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsStateProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			KzGafbBYik5zyGw1bm76HKS7Tc0=
			</data>
			<key>hash2</key>
			<data>
			/cQous3za5Y8sBNc9gJnSblGnlcQJNisDkxD0q+XHUg=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			5vrPi5Z7t//eIoz9u9sQrS88V8E=
			</data>
			<key>hash2</key>
			<data>
			pGuB3yod13iFDnnyRtuUuYaX7VHyqUUmVwj8DUHOU1Q=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppEventsUtilityProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			w++q3FMjWosIk0QoN6chRjfvng8=
			</data>
			<key>hash2</key>
			<data>
			5XYISqTXIOZPBrYNgUjayHCm4HMFKWuvcjLD+DYj3GA=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			nqhYr/0Yijl6YG6zjg69DkIZMdo=
			</data>
			<key>hash2</key>
			<data>
			TIES03/iWapcNCC9q93neFaUJvaUs7+RvhH7EO++7NE=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkEventPosting.h</key>
		<dict>
			<key>hash</key>
			<data>
			Unlyk7QfK8RMiwI6wrySVV1IWP4=
			</data>
			<key>hash2</key>
			<data>
			ynWClusbalHB6D8+QrD6JmUW1Gr2JdcsDTxToJtiJ/I=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkNavigationBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			1PU/0JKCKzJJV3POVzX50kAFA0o=
			</data>
			<key>hash2</key>
			<data>
			JmQsfAZrZwAJH5qlBBRvvfzK10IKeqMWfuyED+KYNmU=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkNavigationType.h</key>
		<dict>
			<key>hash</key>
			<data>
			3fSnRUTotphysdDrdAvLMxLPW+k=
			</data>
			<key>hash2</key>
			<data>
			hwFJL88C4bRrHRe3Y4ljnl5aCOTIvPg7Oqmz7nhwygo=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			FaTKQ3PKUnwjWoYivjhBcoWCvf8=
			</data>
			<key>hash2</key>
			<data>
			4DXqK//wHh7BOXM1el/fpSnI2itx5Xvq5U9scd3DtTE=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkResolverRequestBuilding.h</key>
		<dict>
			<key>hash</key>
			<data>
			SXBVCFpg1usB96KY2Km7m8BU4PQ=
			</data>
			<key>hash2</key>
			<data>
			e+6RKqsAlhAYxmTXpFwikKmionJN86SP5HKrZXbAkNs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkResolving.h</key>
		<dict>
			<key>hash</key>
			<data>
			vW0iP2TWioh9F2xVFhjb96AWH/M=
			</data>
			<key>hash2</key>
			<data>
			qb3BIqDSak/QEJVHaa0UyJE2SIhRWJTvILOUETHKNbU=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkTargetCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			CSjhIDvr2Fy0xL6BAuVxNcPFzdg=
			</data>
			<key>hash2</key>
			<data>
			YPM7wFIveXtTUS5N5/9q/A437m7KU0YQ6H6B206u6R4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkTargetProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			KjpJOKaE7Hu4fZ/kcuexDfd7h/E=
			</data>
			<key>hash2</key>
			<data>
			xRIohRLTU/DyWAlvJeGXzuTsyj3d0Pb9Erqu0nLl2Qo=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkURL.h</key>
		<dict>
			<key>hash</key>
			<data>
			QygAk/iNI8C9zeo9e0YUgaIWY7g=
			</data>
			<key>hash2</key>
			<data>
			GAKSBD0rwQ0puEpcq+BbmYBTAYi4nS6GrhNxJ1vQoKQ=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkURLCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			oN/+vlRMv1IvKkWrsh+VtDwu92U=
			</data>
			<key>hash2</key>
			<data>
			sKw0E6SLYUeeNW0wOHnRp9kQIhstniWV4X8zLvnmzVU=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkURLKeys.h</key>
		<dict>
			<key>hash</key>
			<data>
			9Ke3NaIqqmJ0mx/UMz1Vw+/X9hA=
			</data>
			<key>hash2</key>
			<data>
			Up9j6FGSd/tWYjJkFp+P1BcOj9PPjDtXZlpdfizgL4s=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			CSxGCvSd3TFNsqT26linPXGDWM4=
			</data>
			<key>hash2</key>
			<data>
			kp2CnE92bsV1FLjTg72fYgYnBgr5Jsp2QEI1Efop6JQ=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinkVersion.h</key>
		<dict>
			<key>hash</key>
			<data>
			4lyPD97ZcXbRvhIigNtr3LGnzKo=
			</data>
			<key>hash2</key>
			<data>
			ApoRkmalk5WwUDAjytM+t8cyZVllZTYJC92Fbu0c22s=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppLinksBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			TPb6CeLZPTcgS7hT6GviE2JRz4k=
			</data>
			<key>hash2</key>
			<data>
			ummPfWNsfoi+QpV6yx2bMpP5IvBHvQkfcON2myGbUy4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppOperationalDataType.h</key>
		<dict>
			<key>hash</key>
			<data>
			Yxnb0SsotnkBXyJBdPIv4DE+uuE=
			</data>
			<key>hash2</key>
			<data>
			Kyg3KDbG6mD8rK/gtHc1u8If+GNKh/8Uxmbfib1bpXg=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppStoreReceiptProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			YAWUu9gn0hfRwdt2dscR4Pjncb0=
			</data>
			<key>hash2</key>
			<data>
			x7PPCf3qyw2K0DAl7af8aPMLzR2TVekwBP1nj0J4Ro4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAppURLSchemeProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			G7H5ArEaw56tAdukKkeFnHJW3yM=
			</data>
			<key>hash2</key>
			<data>
			o9vW113QSBrXeTu8w1RgrMfMpi3Li+ZHpavPt/xYGa4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKApplicationActivating.h</key>
		<dict>
			<key>hash</key>
			<data>
			oHcGo1IxNL4whFjkCAqY0Bezu1A=
			</data>
			<key>hash2</key>
			<data>
			N3sSrIIH8n9PUXaO6Ko042/wGj7FjgmjMz09LpHRsKk=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKApplicationLifecycleNotifications.h</key>
		<dict>
			<key>hash</key>
			<data>
			HZOemKjr7HIyL3ND764s1sIhRdA=
			</data>
			<key>hash2</key>
			<data>
			gVCQIdprFkqHFWN03C0LuQuv1i9nrhtP3G3t03o3SUM=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKApplicationLifecycleObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			tM9oT+qXMwjCWeGffrZ/7pU9dhk=
			</data>
			<key>hash2</key>
			<data>
			wPjg8wCzbmbbm8y0dJZMh/wce2hP0UMVUsa2oZnCv6E=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKApplicationObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			xJEwA88eDINbA4Kq2mRmWsoZVaE=
			</data>
			<key>hash2</key>
			<data>
			jUSJX2r8mLJr8u+wAn2jLp571gJ3SriNQhCpnkSNEfw=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKApplicationStateSetting.h</key>
		<dict>
			<key>hash</key>
			<data>
			fy5bOvCaeEMdMssTZs5062vJuLs=
			</data>
			<key>hash2</key>
			<data>
			th4kWyfL9axOR4QeSZnS2+ff21hvfrJBKkVqOS9qpY4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAuthenticationStatusUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			NzmrmWDBawgvUzFKUTG4Zq+D8PQ=
			</data>
			<key>hash2</key>
			<data>
			eSjfSzpHEMWVAvxBo7bJWUgrhMNFvTx8WmBhyasQcEY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAuthenticationToken.h</key>
		<dict>
			<key>hash</key>
			<data>
			Hbt4TrpEQ1E7m0cPJVPJrvfOMYE=
			</data>
			<key>hash2</key>
			<data>
			hc6XPooa0mMb1VzLG43orpr9kKXVeMa+FrWuKr51KE0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAuthenticationTokenProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			zDTRRbEFRongbmR9iUSO35PCbhs=
			</data>
			<key>hash2</key>
			<data>
			mfib/LOFoh5fxGgumdwep5nNFARnqYaIQPovjFKmlwo=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKAutoSetup.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZrZsPjcuQHmtIb0VA/2JxYjo0Bk=
			</data>
			<key>hash2</key>
			<data>
			ex/CIuIAAIQMIaocCGblxTLzDaMcZuJvi3MU6lxdgM0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			tLjX3wSZTxU3/edMWI3RtKPsv+A=
			</data>
			<key>hash2</key>
			<data>
			o6EBabARMQ/jGNCu0KhpTrBsI6PnWr6akob4aCraUv4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIProtocolType.h</key>
		<dict>
			<key>hash</key>
			<data>
			DblZg5KWlfoY7uhozPWbR8A+C+Q=
			</data>
			<key>hash2</key>
			<data>
			A5XYnc2oBVddlLz3wGiibtMvO6i/A+82gXL5XY+AsgA=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			9p88MKTDxjMSnhRmD3tBUBpyKP0=
			</data>
			<key>hash2</key>
			<data>
			/2BV782KXKlCMETLnCxG2DLQWT+tVDZ/Ijo516KxeVY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequestCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			fSrgRjXiEf48iO5SW4tUzCK4Dfw=
			</data>
			<key>hash2</key>
			<data>
			NX+gToQ0lPJ7TSH2qMy4mIDEYMYzkZhwyZbP0XvC1iI=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequestOpening.h</key>
		<dict>
			<key>hash</key>
			<data>
			0yprixA39qr6F++QUQmDaS+kI5c=
			</data>
			<key>hash2</key>
			<data>
			RkLNO6zfPQbD2c3NWSSdmM3tTFBY3GPfYgBqxAleaf8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequestProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			QvIJ69YhObUzXyvQTDogepJbLIY=
			</data>
			<key>hash2</key>
			<data>
			zXyn4IrEQSs/K0vIuWaHZHWeYSHJfjrS2xjq3hrHThs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			0xHREU5UN9V2806Plbv9YnZCtvo=
			</data>
			<key>hash2</key>
			<data>
			VpEoCY99wbTWk0mTFsof9zSkSb9JK0bc0ty0LHM0GGE=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKButton.h</key>
		<dict>
			<key>hash</key>
			<data>
			nRauGR6uRcDNSlw5mNra+SReMhI=
			</data>
			<key>hash2</key>
			<data>
			3PYcJgi9ksT3Oh7qkXSmtIZ4zC05ftjnedLkaQyiEEc=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKButtonImpressionLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			LsVpQhR6smaHCPnF/ITEWiKGkKA=
			</data>
			<key>hash2</key>
			<data>
			pShKA3myYUve8S5W/TI08BTWmhPh0RgoZQ6lY9c5S9g=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKClientTokenProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			W+JNLAn4YexFixxEQGR3Q9RH0KI=
			</data>
			<key>hash2</key>
			<data>
			GvqZ0xNKA6tPwu3fX7MjAlOAhByiiNjI7PR4KRn+IbU=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCodelessIndexer.h</key>
		<dict>
			<key>hash</key>
			<data>
			ua4gDNRjw0KAFXM4vqI1AxIhmgQ=
			</data>
			<key>hash2</key>
			<data>
			FcsOP37OoWFHJHceDyLXTSnifB7NTvxbg8l+Ea2N7KE=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCodelessIndexing.h</key>
		<dict>
			<key>hash</key>
			<data>
			vvo1NJ1mTuIU5ZOEX1L9qWcyo+s=
			</data>
			<key>hash2</key>
			<data>
			pLXLVKuG6Qa9BS6uHD2G6ofjKsDr28Rz0u2tNx1QKBM=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKConstants.h</key>
		<dict>
			<key>hash</key>
			<data>
			D8KfnDhhBY1wExKSXz3swq16lTE=
			</data>
			<key>hash2</key>
			<data>
			FhwOaPhmdYaDveEEpkZpS+FYAManp/GC+vHLW+hAU6A=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKContainerViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			U4pg9nowPmFOUC9fvIpLijlN6fg=
			</data>
			<key>hash2</key>
			<data>
			29xbe5Cc1TOVvV8YSvMCywrkYejv/ToTkWJXi96mSh0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKConversionValueUpdating.h</key>
		<dict>
			<key>hash</key>
			<data>
			fTxcQJg2+ZGuh6rqfJoDG5p6L94=
			</data>
			<key>hash2</key>
			<data>
			6KHP0pS9iTtIWkg6hqMPweWwdf3DKMJVhKQxPa5rhCY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCoreKit-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			7600QDNflQgHY+mG/cwStJOsec8=
			</data>
			<key>hash2</key>
			<data>
			J+PneAPNIzsM6TXXiXj/wEQDSINuc31/znTjvXu7le4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCoreKit.h</key>
		<dict>
			<key>hash</key>
			<data>
			9NSBO9571MvtFn98up/7ePg4lwo=
			</data>
			<key>hash2</key>
			<data>
			o2TE4Ds12wtlfLBvKjQ8VsMS5tnFEdVj2vJfsqI/21I=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCoreKitVersions.h</key>
		<dict>
			<key>hash</key>
			<data>
			/eqaMAzP93B16wXvakv2GDTKX0k=
			</data>
			<key>hash2</key>
			<data>
			Vfex5H/RRx7zB2DpAjP6NiIB1F+xRX28EDuU7fmVphY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCrashObserver.h</key>
		<dict>
			<key>hash</key>
			<data>
			i5LsrVb8OhFQsCQv02NggyWl4qk=
			</data>
			<key>hash2</key>
			<data>
			VLAJlXn/MRpH+6S2wXiJ2MMHTkHfS7Iiq8J4ETcBHo0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKCrashShield.h</key>
		<dict>
			<key>hash</key>
			<data>
			488qfAna0UU+EgR6ICrzMNPuE+Y=
			</data>
			<key>hash2</key>
			<data>
			wrUKA5GCwCODrnCFSkEOYMuhWsXWs+v3tqHYUpWF3JE=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDataProcessingOptionKey.h</key>
		<dict>
			<key>hash</key>
			<data>
			2KTIgp4tO9oDInYHDKE4PkSZtuo=
			</data>
			<key>hash2</key>
			<data>
			N45K4T1fNjF4+O/UVhpkoNMCpF0tstXjwLVZpz1m/E0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDeviceInformationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			AhMeLL4pfNP6NOlehkTmCGd908I=
			</data>
			<key>hash2</key>
			<data>
			o//wG1+zWPP4qO37XPTeCIBSdGTXg1o88LARSZXZBpc=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDialogConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			BkHb91/H2f5w4nnsjzWvACP+LO8=
			</data>
			<key>hash2</key>
			<data>
			sxJ8YoB2cRIbhfFUnxXIe0KRF4COPsi4Zlpo/qsKI/Q=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDialogConfigurationMapBuilding.h</key>
		<dict>
			<key>hash</key>
			<data>
			3JigPqy5HGPxZ1EfvLl6K2r09G4=
			</data>
			<key>hash2</key>
			<data>
			DoHcwC+ItwFyYXOM+3jsJKQDRSVYaPDdnK5QjYY6Bdg=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDomainConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			z42cVELCq+08IS97csqrsv0G3UM=
			</data>
			<key>hash2</key>
			<data>
			ODDfLUp/EjYZAOC3/MOAnDiI7lc8nLJSqY8sennYpO4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDomainConfigurationManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			vhxTjwLkFlXSayMOST5Du3FF1Ns=
			</data>
			<key>hash2</key>
			<data>
			aJ/dMNcl3sV3TF84MdS1OF9uE+sX9Qh6m5P+Wu5QWhQ=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDomainConfigurationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			bAZKBepNb9Jwher3IYkkHIK+jM4=
			</data>
			<key>hash2</key>
			<data>
			A0RxB1Xye7jGvnnjZ0gR4CstN5CfaM7agMWrUsOCmHg=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDomainHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			4pB423j54rXYv0k1XCxquIvgGII=
			</data>
			<key>hash2</key>
			<data>
			/swwaLXUz3BoEglRz+kpA5A5WTy4IyeJyk8eC4k3/4g=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKDynamicFrameworkLoaderProxy.h</key>
		<dict>
			<key>hash</key>
			<data>
			gQze+1wXFmg8HHDE0Ba4/AdlSbk=
			</data>
			<key>hash2</key>
			<data>
			DKzc5A2VHGjb5zjiW11FfYE+be1Je5rxetWvpGpLWP8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			t9pebtrwIlsvol930VzK/Afs/8E=
			</data>
			<key>hash2</key>
			<data>
			DhLqWnWnnr8IYmWabnEPwwtlgNNnXJV/K4weIvUDLz4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorConfigurationProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			rWzO75a4AGVSBUj9usd195pV1pw=
			</data>
			<key>hash2</key>
			<data>
			+cf3Zndluf4/nH6CRo8QwhZjyyogyQr9rRGqFSJJ/Qw=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorConfigurationProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			/NYL7EwesbS1Jj4zI5j/M36RrP4=
			</data>
			<key>hash2</key>
			<data>
			hujKKoeVm3HeeiIxu8JoiRbP5INzeD2/vMsdcL57FLM=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorConfigurationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			8oEeLY1N7YRaIXucUhviYIeCeUA=
			</data>
			<key>hash2</key>
			<data>
			disBMqvoKxnyr7jUVHZSCd0dYEdLy+w18e3cMgW5INU=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			hSTHauBFdEYzYLgpazD8Nu2mbvA=
			</data>
			<key>hash2</key>
			<data>
			J1rXYSPoy877pIwlZKDP6n/b3DufexQeIl0pOymmYVY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorRecoveryAttempting.h</key>
		<dict>
			<key>hash</key>
			<data>
			woJpc0K7/L9yeXE4C+9IxIUoyNo=
			</data>
			<key>hash2</key>
			<data>
			W0d5bC9aorrzc9ohCQNpLCX0lFAN0NEWcKF8J0wW/rs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorRecoveryConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			ycgx9i+ACoXgcR7EKbYFYCxzlOg=
			</data>
			<key>hash2</key>
			<data>
			sxH7BnnlsngUMLwevQeegntSFKDRSmTEKtWhPQj/1Gs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorReporter.h</key>
		<dict>
			<key>hash</key>
			<data>
			mr/DzUsbnLVLYMNNABg7aaa45vw=
			</data>
			<key>hash2</key>
			<data>
			rcdsRdF/ar59iiIPHyagPto5zGT/Sxfh95CnVdi22+w=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKErrorReporting.h</key>
		<dict>
			<key>hash</key>
			<data>
			QPo2mKvh+gBY7bUYxLIRori3P84=
			</data>
			<key>hash2</key>
			<data>
			akNPfvBbZsCgwyqes5+H7hRxxdGAzyQYxlJqnkGK4hA=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKEventLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			DUSe3ilZQO8BqMsmrVYsaoF7LGA=
			</data>
			<key>hash2</key>
			<data>
			0VHOLZCsy4MIa2IyIiw2m0r7HlIaK1PEG6/P55bLZ8o=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKEventProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			gp3AdngU5TgiopBE4rJMoR6ANSg=
			</data>
			<key>hash2</key>
			<data>
			XdMXEDfV+PUzUnWtYK5vvmDq15EbhKM7Msfu34hUKZ8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKEventsProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			O75xhVmbjtVsiUJUjeGE1r788nM=
			</data>
			<key>hash2</key>
			<data>
			yIDs9c5MQ+yOaWXnwlnpInkbHV8GMjI7ZTBwOd+/AT0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKFeature.h</key>
		<dict>
			<key>hash</key>
			<data>
			Oh4B8Z0ZOzOtzlb70Z4TPxT1NQg=
			</data>
			<key>hash2</key>
			<data>
			V1hpI7TAZqwqEOEABosdtY1n2cBJTV/NzjeKNw9UDUc=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKFeatureChecking.h</key>
		<dict>
			<key>hash</key>
			<data>
			rYTkx84W03mL0rrno4sthw6poiM=
			</data>
			<key>hash2</key>
			<data>
			QtYErERzFYGRmUpt4HXd8p062xQtrNl5l+J2nUhhc1k=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKFeatureDisabling.h</key>
		<dict>
			<key>hash</key>
			<data>
			5RTMQMD5BH2IHczkFRDwcDa6YEc=
			</data>
			<key>hash2</key>
			<data>
			TKR1xKxgrd0N8JUx6kxkmWjLK07R7jMGOSWpnHlMoqA=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKFeatureExtracting.h</key>
		<dict>
			<key>hash</key>
			<data>
			rLjAEFGlg1SFdt3LKE/Vyvpy4C4=
			</data>
			<key>hash2</key>
			<data>
			9+2zhi+7pfcv33U40/OTdeLKZgpJVvbygggabihtg/Q=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKFeatureExtractor.h</key>
		<dict>
			<key>hash</key>
			<data>
			UpeejK9e6KddzNj4pJ/187IOAok=
			</data>
			<key>hash2</key>
			<data>
			kEO+YvkRcV483BfguxWKQ5uS0+Qmx3F3mL3dsHGkRks=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGateKeeperManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			UApODVoVBgYDmemuf8pq5KhSZ2A=
			</data>
			<key>hash2</key>
			<data>
			7mWH6MgxmutTpF+sXRQKlG6n5MLh4o5QHROkkRPhs1E=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGateKeeperManaging.h</key>
		<dict>
			<key>hash</key>
			<data>
			MZUyM6YX2TAfccBQg6xiTBypZ18=
			</data>
			<key>hash2</key>
			<data>
			ZRWGI6azeE93UU6MLd4PVLDsl0+khZ9ajsIyRJrwYNU=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphErrorRecoveryProcessor.h</key>
		<dict>
			<key>hash</key>
			<data>
			aNj/uzA6QMePztxIxRll77xHLIg=
			</data>
			<key>hash2</key>
			<data>
			wyEA5FnCIsiLQMekTMTbQfJgpsp5uPtXVy0RVKPm/Co=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			W0+x27i8eT+6EMZ3lVcQXPKsAP0=
			</data>
			<key>hash2</key>
			<data>
			6GPfja4r0l+3PHnf0yfFzGgyV0iPVl+wm7ooUBBX1M0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnecting.h</key>
		<dict>
			<key>hash</key>
			<data>
			sAlo7za+Sf4zj8Xg+Wy83BUtRCE=
			</data>
			<key>hash2</key>
			<data>
			/aivBeJLeVgjejn+J7Ve8A1PgbyFwWSEIOEpNrMZwLw=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnection.h</key>
		<dict>
			<key>hash</key>
			<data>
			NUnPpwpwUkVhUBU67o81VFaERfo=
			</data>
			<key>hash2</key>
			<data>
			1ICPsAdQVISoM+fhXtKdpyBkCX9WIi5bqIEJz2IfkHA=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnectionDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			FSHiVcHDpJTlfdfBczQNHtjDJ8s=
			</data>
			<key>hash2</key>
			<data>
			pbeIgtjILQ+9lWJviGFFqpO8IqXWnVf2DQ094YKDKMY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnectionFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			gAfT3DO/vuTTlEaJPEP8hL5P3Eo=
			</data>
			<key>hash2</key>
			<data>
			xgTvIuiH3O0GP4dAfx+lVweVvj/3VUFzk/ZwDkEd6UM=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnectionFactoryProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			A26a5H79Zb1dRO6YHMFB4DbS+D8=
			</data>
			<key>hash2</key>
			<data>
			ZSBkNkUs6k4hh3lOO6aa5+j5kuh9vwSm6BTbH++KEH4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestDataAttachment.h</key>
		<dict>
			<key>hash</key>
			<data>
			7vvCqPiZp4o6JKVaJJ4FP9XkXKE=
			</data>
			<key>hash2</key>
			<data>
			rrQm0dv7u0VNuBOOr4bOsLq22U2VRKN5+/8z8dvg8ac=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			lAwX1CKv5VHiJ07/xZZylICOdg4=
			</data>
			<key>hash2</key>
			<data>
			VHji6+eQJ/noGhXoav1+rDhYGkeNSPac5f3JMIMO4OQ=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestFactoryProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			ydipmJZDsiXmT3/5DNEiX0gzsNs=
			</data>
			<key>hash2</key>
			<data>
			LGCTe0D34qYvAzYLEDR50t2F4wbgmylanIR+2HAcV/E=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestFlags.h</key>
		<dict>
			<key>hash</key>
			<data>
			Zas2ccUoNaCrjUffAdLC6TmKLWs=
			</data>
			<key>hash2</key>
			<data>
			QaBxTTw493IFQLv0fwkhBkWu57wAPkAZ/fexBSmcWuA=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestHTTPMethod.h</key>
		<dict>
			<key>hash</key>
			<data>
			sF4WT7ko2ZXuQ91thBewwSb29Cc=
			</data>
			<key>hash2</key>
			<data>
			s/ZdV1PYtfb+e5MToTE5eWQ/g8Ea8Lfbn1y5cPXIois=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestMetadata.h</key>
		<dict>
			<key>hash</key>
			<data>
			4ZYoDzErnijlAw/YdD1e2qQ3eDs=
			</data>
			<key>hash2</key>
			<data>
			Twh3qoAgt3ReSFrZuMn8tfYVVZ+eHo/Z01B//qRL/aM=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestPiggybackManaging.h</key>
		<dict>
			<key>hash</key>
			<data>
			RzvPVhBs2lDl2xH7PANCNXkswIE=
			</data>
			<key>hash2</key>
			<data>
			wxiIa9cDFOlxQYXM+TkWOQooJpOkR5UkCGuNOfRHzCo=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			2WftYLHKj8D0QUQgx2F/+tCFX3k=
			</data>
			<key>hash2</key>
			<data>
			aKkuXQ5CLQQh3MSzr4CWjSGHECgsjZlvDwFTFbRrrFI=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestQueue.h</key>
		<dict>
			<key>hash</key>
			<data>
			YazdAJ0rcrHmvRHvxaduooEWglg=
			</data>
			<key>hash2</key>
			<data>
			jg27Rw8BPOqfHG/lqSFvmFumU0quxxvP7wdja0rMIq8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKIAPDedupeProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			jFfQ2uToisxlUwu5HAehBwtT5yc=
			</data>
			<key>hash2</key>
			<data>
			mzR9LdGDTqtc3DYV+JOsBkVoOuodrkRWOO3EnpWU9C0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKIAPFailedTransactionLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			3naly4poGndtl04IKJLRhIn0y58=
			</data>
			<key>hash2</key>
			<data>
			YxYLbyBk6Uw+vYZwpHHGp5GKRCS9QE/hRs6s5QPT0uw=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKIAPFailedTransactionLoggingCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			8f1KPldp/xKn7SdJ+extn1OcPWY=
			</data>
			<key>hash2</key>
			<data>
			ZzaGG8LO/3EvuN9LDMk/455U5A74GiZMmXHsNKy4Zlk=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKIAPTransactionCaching.h</key>
		<dict>
			<key>hash</key>
			<data>
			EUOfrTb9ijRfv++ZOI1VjUF46+o=
			</data>
			<key>hash2</key>
			<data>
			bjiGwxnSAUlt+NVqp+emo6xGvyxyQzPz5qaW1530I/E=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKImpressionLoggerFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			nfa97DKHYMz7v0+pUDy3nlxrUYk=
			</data>
			<key>hash2</key>
			<data>
			QuBRqz4neAxQgnQCCfZVT7P/volyw8iY5KgrMKRJmJs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKImpressionLoggerFactoryProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			3LEI96v+5GIEFysj3D1C/rG8ihw=
			</data>
			<key>hash2</key>
			<data>
			Us0Ez3sk3OsuatGsASCqoKebAXNWPg9S5pMOJ5DxDc4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKImpressionLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			wrrEayG2/hN5wUceRYRAU8HrXBk=
			</data>
			<key>hash2</key>
			<data>
			Vprv+yj1TQTAVjk0AdI+90h3w3DcuHwxVoAECMAadvM=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKImpressionLoggingButton.h</key>
		<dict>
			<key>hash</key>
			<data>
			5jJQoQEgcqekG5Zl8U42hb9oI80=
			</data>
			<key>hash2</key>
			<data>
			ndY9up8dSnK60T7Qxxo1R50wARczYvrrA0RXdqjCzKI=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKInstrumentManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			b4LuHg6PamZ7IBBxVoOYpxuYERM=
			</data>
			<key>hash2</key>
			<data>
			+5qq+ul/kPhxOgEhuLz8XmNc60qQeWJkTw9obciSaz4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKIntegrityParametersProcessorProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			YAejrtcnEaVtGKUEB4ZJpyeATvQ=
			</data>
			<key>hash2</key>
			<data>
			o5WVcgrcNZ9fy9iy8pkmLgt9cY41GNSvTFg3Z4b9k5A=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKIntegrityProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			87ZiOpw4U2tli0OR6o2FOZV0Szc=
			</data>
			<key>hash2</key>
			<data>
			ZIhsJR2yehcaQancBDHC1s72R+hCE/frIqi5p8edPn8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKInternalURLOpener.h</key>
		<dict>
			<key>hash</key>
			<data>
			FLV0q7bfHv0++Xz2yX93rch6WQk=
			</data>
			<key>hash2</key>
			<data>
			d7pu09v171CZOLDOnWFtkt5TfA+u9wD6UYyoWxkSA+w=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKInternalUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			xVnxfL7eTB7Crbqhijh4imgTplo=
			</data>
			<key>hash2</key>
			<data>
			fGPA4wFG0PbwsHF1HMG8uXRBNUpRfuVBclcu0v5TDCk=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKInternalUtilityProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			/ZcaAKHHVRGsTO5fkpWQKXtwRlg=
			</data>
			<key>hash2</key>
			<data>
			QxK2NWyHrM+GqD1pK5e3LD2xbiZX/p/UzObquLThWI0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKKeychainStore.h</key>
		<dict>
			<key>hash</key>
			<data>
			VSfBFlzguwgAJhVey77PM3TiKzI=
			</data>
			<key>hash2</key>
			<data>
			sQGCel/07cMPMZI0kl8wscwPV9WL/JIYf1ns0PSf8v0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKKeychainStoreProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			Vl4nIrUwT7cqcjwlXymbPkKUVDo=
			</data>
			<key>hash2</key>
			<data>
			XNfcYyJYq69j5eL0ARtTlzkh8SdVCq+G/s9eY4Pd8O8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKKeychainStoreProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			u2GnomfI+oHCeoe6Ei9pG5quwVE=
			</data>
			<key>hash2</key>
			<data>
			/INTsqlL5Gr82q1fsrI5Mm6ox4RJoy9zwOvstKFPXrI=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLocation.h</key>
		<dict>
			<key>hash</key>
			<data>
			lVldFN//gmPckkWOntm6/lMe0QE=
			</data>
			<key>hash2</key>
			<data>
			4VM07vWgUKPPsLEMLF29hXYKIHBkc9vETSX506Z++Uw=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			19xKJkbTRpwMoQwzaC6Nu+w5tME=
			</data>
			<key>hash2</key>
			<data>
			O7DI9fO361qB0Y+XI/ka6eMk5/LMGvTE5T0NPkcTBBI=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLoggerFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			oN6VhKse4/3rPEKNFGojETNkg58=
			</data>
			<key>hash2</key>
			<data>
			e8PHQtEWsLuVh+XXwDbUAFSXUORwmsnkmOyyYz+ph8g=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			/DbryGZcqEQACAktvCjPjV6SDG4=
			</data>
			<key>hash2</key>
			<data>
			IvKTyTv5bHSAJcZqLwaHR/lW5CFnjIggFOzHMRDWMk4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLoggingBehavior.h</key>
		<dict>
			<key>hash</key>
			<data>
			lNVc//dYNZlhHrZHLByspMT+TeY=
			</data>
			<key>hash2</key>
			<data>
			f5RLVvxNd/VtlSpoMhH6nO6jFRiC9rudIo+algkCGBs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLoggingNotifying.h</key>
		<dict>
			<key>hash</key>
			<data>
			ClxijYzHweoVytnlGNbT1n62Q2I=
			</data>
			<key>hash2</key>
			<data>
			/H/LDFDn+fA0n3pb11qgMdP8PeuXl02axVuT5C9K6nc=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKLoginTooltip.h</key>
		<dict>
			<key>hash</key>
			<data>
			t9qlwGoUeyWhxDfXE8Ky6RnF/gg=
			</data>
			<key>hash2</key>
			<data>
			C6wHDAq5ukwucR1FkTnnq3ucsw6y7GR9wDadgB3zHZY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMacCatalystDetermining.h</key>
		<dict>
			<key>hash</key>
			<data>
			Fy2901RMaq8v1KZ1nkSvVuhYSN8=
			</data>
			<key>hash2</key>
			<data>
			dOVbOuPHBVVE2h/fz8iXrr824nuLBVRrqymrNwdF5kY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMath.h</key>
		<dict>
			<key>hash</key>
			<data>
			rYMaHGq5qMdO0PMhIT/NeaWQMFE=
			</data>
			<key>hash2</key>
			<data>
			RQb23pN1EiaRezovWmrJpZ289Eby0ndYmkHCpsWKbXo=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMeasurementEventListener.h</key>
		<dict>
			<key>hash</key>
			<data>
			OVZSC/JIKFj+KgzAwt5biOqF7rI=
			</data>
			<key>hash2</key>
			<data>
			ELmJcxc3dSPYAYyrwqHJpwUxM4is2hfU27i90WTOYQ4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMeasurementEventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			9XUuzXpHXt6PyqsDNvqKaCYOsaQ=
			</data>
			<key>hash2</key>
			<data>
			Xy6hXD6E/laXHrWOeDhRU5cQKCbE05HcdVjR5tdUx6E=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMetadataIndexer.h</key>
		<dict>
			<key>hash</key>
			<data>
			yvEMxozxtAwiiPOoouj17MOx38w=
			</data>
			<key>hash2</key>
			<data>
			FC2xPfBPJ+KkA1lGRnOxuFDbWIJc5fChKOpjrK4QYMk=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMetadataIndexing.h</key>
		<dict>
			<key>hash</key>
			<data>
			0XbHu0BpTUjfkrPvYn1B9+H0Bww=
			</data>
			<key>hash2</key>
			<data>
			QjaWgUOa4ftPTfJONsDDsoLADLIhk0afRL0wWiGd9qc=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKModelManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			UTvH2kBUyG0vy9bmR5tucIdzDMU=
			</data>
			<key>hash2</key>
			<data>
			4HNzWyenJC3JFcA8hUl1k235rwmiyBYrmtRHx6i0w2g=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKMutableCopying.h</key>
		<dict>
			<key>hash</key>
			<data>
			CdAKmAi79FHfugMUCBcou38XjyY=
			</data>
			<key>hash2</key>
			<data>
			9WETC6Qraw3B3QY90JfYu/elsAnM/L40JTEsRAOO+hQ=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKNetworkErrorChecker.h</key>
		<dict>
			<key>hash</key>
			<data>
			lc4ltIsnGN0wefVKZeW3BTQqt8o=
			</data>
			<key>hash2</key>
			<data>
			mQPfqbSnxLTJW64KKhoGcZPQYNTYQABLzG1AZ2hcTSs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKNetworkErrorChecking.h</key>
		<dict>
			<key>hash</key>
			<data>
			DQOOpk+tae6sTARv6zgYkUNQv+4=
			</data>
			<key>hash2</key>
			<data>
			JaxujLpfeoL0uJ15AXk/+TxmnQ+XgmtHYz9sb5k3r1w=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKObjectDecoding.h</key>
		<dict>
			<key>hash</key>
			<data>
			rsoqTN959edBc1dWsOJAk4SFajc=
			</data>
			<key>hash2</key>
			<data>
			qsowPp05Itw5uODC4pGjeSY1WE9ntYKWLfZW7A0nNw4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKOperatingSystemVersionComparing.h</key>
		<dict>
			<key>hash</key>
			<data>
			jd/kWOWeuMfcK4hF/tgU6AeS2yU=
			</data>
			<key>hash2</key>
			<data>
			KaQv/BSjIbO1IWul0m3Ckagnf4cBrNKnGm3fhM4GJcg=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKPasteboard.h</key>
		<dict>
			<key>hash</key>
			<data>
			4+oJKTiDHC9gezS8CCuhdWHO2gs=
			</data>
			<key>hash2</key>
			<data>
			UJnUSOEkUYzGWK3fOCD3HGGdwQLxYhZg4znp+HpSNGQ=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKPaymentObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			2F/w1aia/WcWnKwAGffiRgYimWQ=
			</data>
			<key>hash2</key>
			<data>
			g21RW5m5mkv5jnc/g4IZvHnCPQLSjxGoaSvN3vo4UZw=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKPaymentProductRequestor.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZQ6Btyy0z7ZiD/M9eX+7+KDRiLE=
			</data>
			<key>hash2</key>
			<data>
			S2CSCz82/VNHbyry4LOyJvanQu9UC+br3C0NUFHHqe8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKPaymentProductRequestorCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			kofChLuS6UMw2biE8Lcu2yhsmt0=
			</data>
			<key>hash2</key>
			<data>
			fGzWAJQkZyRBklHMViipfo4KyA1fMAOzlOiH4HhweQk=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKProductAvailability.h</key>
		<dict>
			<key>hash</key>
			<data>
			4z6lAOLiyG+H6sMmuDzBXlrBO4Q=
			</data>
			<key>hash2</key>
			<data>
			AfSg3sbP+VegxUAApbWi9NSI+/dlu9LbDGiLvCWo3Z0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKProductCondition.h</key>
		<dict>
			<key>hash</key>
			<data>
			p2M86R+0XjuIIHBALGh4qHhF0sg=
			</data>
			<key>hash2</key>
			<data>
			dNGTpMMgyZMruD+nBPSsD0Y3Bc2L8ZoTcsW1f5tdK7Q=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKProductRequestFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			7BIaz0Ab6i62/Dq0UaZpdoNPhCs=
			</data>
			<key>hash2</key>
			<data>
			6bp/2xb4hfUOim9SEuGgPr9z+Bysu/FK27psJ1WxipQ=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKProductsRequestProtocols.h</key>
		<dict>
			<key>hash</key>
			<data>
			quFgypTeZ+2XNvJr6deEaYceClo=
			</data>
			<key>hash2</key>
			<data>
			a7mWH2GsxBZ9X9N8Z/19nO+toxG/0r99YGYxK28kY0o=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKProfileBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			m3b2HY43bVO+k7tLfgTUHlel3XM=
			</data>
			<key>hash2</key>
			<data>
			4/f1aRcfzmU+yr0ypnHjdfSBpIQ8QxluAy3Yn0XxJPE=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKProfileNotifications.h</key>
		<dict>
			<key>hash</key>
			<data>
			p/v7YsyAewf4Zx5PgVOw0g9689k=
			</data>
			<key>hash2</key>
			<data>
			yWNI2yhxUUa25VEKtZBJViFYRTXMmJxx1JUrJbK6QAs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKRandom.h</key>
		<dict>
			<key>hash</key>
			<data>
			rqrUnE5WdNwaBw/Fm/Sauc2KceA=
			</data>
			<key>hash2</key>
			<data>
			/FYnxRG5dQwkxSXWGGkucUnk7tABHKmrqud2upLxMSk=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKRestrictiveDataFilterManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			FhBIBCgefnNx6Hx9hBvjEQxlgTk=
			</data>
			<key>hash2</key>
			<data>
			mSbOfI4ZD+1zdJOwvNbPEvSXVCr/iH1TcmA+bmwJW5k=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKRulesFromKeyProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			9/nqsZZ8Epe4NBnTPqEOptCNdzs=
			</data>
			<key>hash2</key>
			<data>
			KU18OwgqjgGW8bB4DSVWVWXRm2R5Uh9WKEHg/7D71/c=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSKAdNetworkReporter.h</key>
		<dict>
			<key>hash</key>
			<data>
			N8+fSt031r3BemNKfykFVGaMU2g=
			</data>
			<key>hash2</key>
			<data>
			uqe1+q806a6PrWGJ1z275ZtkYMC6hW+JEjEM1j+UxFY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSKAdNetworkReporterV2.h</key>
		<dict>
			<key>hash</key>
			<data>
			t1JQiDuyttg76dcS901/oxfu6tQ=
			</data>
			<key>hash2</key>
			<data>
			hyqPE+oRV6Q/uWySMPT2HXaeoU0Fhbr/rzJPAblC8zQ=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKServerConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			ePai1Ct6rcU1fbdNFDdAFZgZLYo=
			</data>
			<key>hash2</key>
			<data>
			svD59mf/yaDmtJnt9mqyUETp6BqGqszyESR3t8RUtz0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKServerConfigurationManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			MabAusZdKvvZ5Ny2wrhRTzvzRhY=
			</data>
			<key>hash2</key>
			<data>
			7bxzdmwW3VP1D775pYiEPDzkARci0KU5+G83fUIv+Lk=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKServerConfigurationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			OVcy7K8/6LpbY7Ls9wBkMeiLOXc=
			</data>
			<key>hash2</key>
			<data>
			nggJ6UDcdQ8WAvG9K2eWGIr1+MNgK3/g6Tj4sImbWHM=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSettingsLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			j4NKiO1um7BzI27sPShA+WNNV6E=
			</data>
			<key>hash2</key>
			<data>
			GgfZ+r7AkNT2pA6iA72tqgExzEgxS0MRiVNUSlpsa48=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSourceApplicationTracking.h</key>
		<dict>
			<key>hash</key>
			<data>
			eyc/NF7kaz05PnojBKao9RoOkXo=
			</data>
			<key>hash2</key>
			<data>
			qajy5wvDILF4b7Jcx9hTizBUhaT1i2GXvpxik1TzkNE=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSuggestedEventsIndexer.h</key>
		<dict>
			<key>hash</key>
			<data>
			eLTHBeEjKRaaEKDEDTrfz5o1+pE=
			</data>
			<key>hash2</key>
			<data>
			o+ccIBHzx5V9J1IZLNYIg+BkWS3Kl3rMRYXWeWRdhDQ=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSuggestedEventsIndexerProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			Nlpu1MobSLVgHiTs61VhpCb8F84=
			</data>
			<key>hash2</key>
			<data>
			9kKE4jZryp1a9dSaPrjDlA+9RhscC9+Yq3GlCBL0DpU=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSwizzler.h</key>
		<dict>
			<key>hash</key>
			<data>
			PgutzEuZyjT9se/U24OTeoEMo1g=
			</data>
			<key>hash2</key>
			<data>
			0n+HQ6UGePndL7zICwSJXV5WRg12HV3lB4DiARhtWIU=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKSwizzling.h</key>
		<dict>
			<key>hash</key>
			<data>
			jMRYakKevWrqsOerYuQ9Fb1eCT4=
			</data>
			<key>hash2</key>
			<data>
			KFyWd42cMnaIyExVvONG1g3JPBSvnv/4xdag+5V9zDQ=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKTimeSpentData.h</key>
		<dict>
			<key>hash</key>
			<data>
			Zgill5e+tNHLsGRpZ8nXHCk2iEc=
			</data>
			<key>hash2</key>
			<data>
			4onV/tAXCdp3PH0pHlu7hEtJ7bg+cH1JUhZKPPfhxms=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKTimeSpentRecording.h</key>
		<dict>
			<key>hash</key>
			<data>
			NuI7oI0R+b4/s+KiUbS+MEn4x8k=
			</data>
			<key>hash2</key>
			<data>
			Xi9idQwWvd5DFqQNYAbll1aNfRM0zlSsrHt8LqUVh0E=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKTokenCaching.h</key>
		<dict>
			<key>hash</key>
			<data>
			cla0KYAtpSCy2GcXC++XdSQrxck=
			</data>
			<key>hash2</key>
			<data>
			aqvT3pRj7C448KPxobWR9AZGsr5TeBuYFw6Fe2phX4o=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKTokenStringProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			43wQbjzLSB+cSu9hTQZ4tJq24Mc=
			</data>
			<key>hash2</key>
			<data>
			TTwDb4bRpxFALwHRV7rPZ9zwNFPGZhy34If3avetGjM=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKTransactionObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			lZIW9x0Pm8LlHaUourw8qilnS7I=
			</data>
			<key>hash2</key>
			<data>
			NOPfkdHDnW6uD+jIMqNB/e5UkbR4E7d1qzrjahnOHdo=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKTransformer.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ui2GFPACS7T6kK9LcCLcdJyCYyo=
			</data>
			<key>hash2</key>
			<data>
			76ADDGvmmKAAjwJQmqZOKN1QRhrUmGE9qELPwGG2F4k=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURL.h</key>
		<dict>
			<key>hash</key>
			<data>
			lg+hfhk8xZ7goFdbGZ8cVtCyNbw=
			</data>
			<key>hash2</key>
			<data>
			MC/UJOZYmjruWzpiMns2o9iOpycM+cvnUnx2d+brcDA=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLHosting.h</key>
		<dict>
			<key>hash</key>
			<data>
			O+RAgM4Uu49yrzKRLeqtv9FfVKE=
			</data>
			<key>hash2</key>
			<data>
			tY5HluqD9iw44nsX/G1SrqCii9dfnxqiv8T/kapFGQs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLOpener.h</key>
		<dict>
			<key>hash</key>
			<data>
			/9/EmnpZWM6KdVyGGEI1NUvMbYk=
			</data>
			<key>hash2</key>
			<data>
			DUeUDJEfTX1NM5s8QxHytKu4/avgUtMZIXnRyx+sTF4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLOpening.h</key>
		<dict>
			<key>hash</key>
			<data>
			0r8abqnzwBXvpV5PlCa/kQ+OdW8=
			</data>
			<key>hash2</key>
			<data>
			djQLdPxXd/h2GC826rf/QKm1UM7Fb9poUtZsWIB9K/M=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLScheme.h</key>
		<dict>
			<key>hash</key>
			<data>
			36HfFNYLwWfRajDYFDJeNZe/evc=
			</data>
			<key>hash2</key>
			<data>
			BdpEnJgCwk4m/BX4jdUcFWtch+dOASiPi4b4XuShRow=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLSessionProxyFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			fkszrJOzG1OgfuDotJLKT74xw1w=
			</data>
			<key>hash2</key>
			<data>
			nFIFaAzvqWS22hEvcMkix5YaPcK4GBmjn+7lCb40/RI=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLSessionProxyProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			5Hely+G3LbaFH81TEA3aS8tH8Xk=
			</data>
			<key>hash2</key>
			<data>
			aTA0C16KsECkZ2E8HCXO7ce5CfBhS4krYqHjd2sMT84=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKURLSessionProxying.h</key>
		<dict>
			<key>hash</key>
			<data>
			LIrnTeexPfrk+a2pllxOcLBqw+Q=
			</data>
			<key>hash2</key>
			<data>
			xCWdJ07ZEEhMehbPyCICo2qsde4nyWuid/4r5sZNYM0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUnarchiverProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			mBxizULPLClFmFkzGvq7DZbedIU=
			</data>
			<key>hash2</key>
			<data>
			km9PeIz2mArhmhaSjTUxnyXNDnyTw45ONMOIrFvX9Ng=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUnarchiverProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			PU6uhik4cqPkhqWAmJpctYAS+vI=
			</data>
			<key>hash2</key>
			<data>
			oCPV4SoPYuNAh0Y+MkV7TYC6T/3tFQbq2/dC8tplruQ=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUserAgeRange.h</key>
		<dict>
			<key>hash</key>
			<data>
			paq4PxztwLvJ2EQKujqXLJfgjiA=
			</data>
			<key>hash2</key>
			<data>
			pLTGrXlcRTNXtDJu1omtFNVR7Tl9niHZllskPvHvlLI=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUserDataPersisting.h</key>
		<dict>
			<key>hash</key>
			<data>
			eiIDv79NpGtVgJHZ+jovQsS4ZsU=
			</data>
			<key>hash2</key>
			<data>
			e5bOiJw1uxd3xCQKKVryAoxKqTxaIVcOMbL0cYWLyJo=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUserDataStore.h</key>
		<dict>
			<key>hash</key>
			<data>
			iSw84njE3Q4fckPhhhUCpEMT4Uw=
			</data>
			<key>hash2</key>
			<data>
			RY2Lg4PYOft0K2wbJtT1axcgmL2lJ5PaGtXZga3hQjI=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUserIDProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			1G3kOunQeQMavUujt30qZv1Oj98=
			</data>
			<key>hash2</key>
			<data>
			n8EfpaG2V2BCI5xE/8Hz8+83cLBSVMqXjkEx1Gv6r6E=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUserIdentifier.h</key>
		<dict>
			<key>hash</key>
			<data>
			EqrAGtLbcJRfQwl12songIdMMEE=
			</data>
			<key>hash2</key>
			<data>
			TIFVVKaBj4BvRv/gFJvtxkh3Bm/40McFA0531BAu5AI=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			ACK+e48w6WLwZDhZT9VIaXDWTlk=
			</data>
			<key>hash2</key>
			<data>
			aDJa31ufENPB6jQuSRYbBb60HcsTocEvvcqZw2iWBN8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKWebDialogDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			oPLwzJ7KUTr8T5hq/c983EM1rfE=
			</data>
			<key>hash2</key>
			<data>
			RhnzKhKPGNsn+I0efiSBdbYdU7h+Z/m5R4mQUqXW8xA=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKWebDialogView.h</key>
		<dict>
			<key>hash</key>
			<data>
			EHOQKin9zYzG+gmc/LfBegKrvEE=
			</data>
			<key>hash2</key>
			<data>
			HC8WLkRk5PhVWGbE58hLSP/goSUOleGYhuUzBNm31v4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKWebView.h</key>
		<dict>
			<key>hash</key>
			<data>
			jUhFO4/5Ly1VNml/cAryOayE4Ag=
			</data>
			<key>hash2</key>
			<data>
			yZ2ZBoGlvdRcTOHXhCl80pgVGKFXIQXl9HClExQmfbc=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKWebViewAppLinkResolver.h</key>
		<dict>
			<key>hash</key>
			<data>
			jEC9UH9Inm7DYqoFZv2qaN3Pe14=
			</data>
			<key>hash2</key>
			<data>
			+Ac5AbGpEiHL5SuKIFm5ORqswif+O0w+zlIxL1qgUd4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/FBSDKWebViewProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			6dBqgFJpP3qYvCEw7KHe/d5ieC8=
			</data>
			<key>hash2</key>
			<data>
			2TVzs8L8DTwpM3xFUOklhzjkivHzm2VLIiY+IXFxMnk=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/NSNotificationCenter+NotificationPosting.h</key>
		<dict>
			<key>hash</key>
			<data>
			4IqhFgtQjABnwxNo0vP4+5Q/hSU=
			</data>
			<key>hash2</key>
			<data>
			/hTFkvjgGwyFzsQ6VB5CKDoMAKTrun+jQ7LMU4Bcfws=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/NSProcessInfo+MacCatalystDetermining.h</key>
		<dict>
			<key>hash</key>
			<data>
			P2PZ0k71jzSsWpQg6vH4TOHr6SU=
			</data>
			<key>hash2</key>
			<data>
			N5KvICMBUqwJ3JveHhLX4bomYQIHATUqScz3F4Vyf8Q=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/NSProcessInfo+OperatingSystemVersionComparing.h</key>
		<dict>
			<key>hash</key>
			<data>
			7p95y3GgCy4C7g+8xfGVXW4sr18=
			</data>
			<key>hash2</key>
			<data>
			G6fi3L4ee8mocOfWvq0r6J1Iy2HRUZ7PyHTmpxrxo08=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/UIApplication+URLOpener.h</key>
		<dict>
			<key>hash</key>
			<data>
			N6kxbbjqI4XB8O7cVqNSb4VtaOs=
			</data>
			<key>hash2</key>
			<data>
			LFldGsPohxFxp2EMtRNr9DW5offC4saf8wAKpZaih60=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/UIPasteboard+Pasteboard.h</key>
		<dict>
			<key>hash</key>
			<data>
			MKDA9GrF2o6+h3tbW9e9adSk23k=
			</data>
			<key>hash2</key>
			<data>
			JdRN7qh5XEBay0JfDd6bXvUO+ciilKne5qAXVmNWoyY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/WKWebView+WebViewProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			9AxT3+g6DPZx5q3rQWhRBMFJu64=
			</data>
			<key>hash2</key>
			<data>
			Uo6vnwYxxFh+V/+J5U7GTu7dFxg9xliEGHkxzgAJYTs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/_FBSDKNotificationPosting.h</key>
		<dict>
			<key>hash</key>
			<data>
			w1Gpzlc2q889xvFT3+ffPk2kwQU=
			</data>
			<key>hash2</key>
			<data>
			ZBUZDT5SUs3wBxxP06/A5KDaxedomK4NJ7axY0qD7jY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/_FBSDKWindowFinding.h</key>
		<dict>
			<key>hash</key>
			<data>
			Gac9mAAYHny41SRhpW53CbfSo2s=
			</data>
			<key>hash2</key>
			<data>
			QxPymhBROXgyvxD0bzeN+T5ennsD7zaelwvA1a2l3oE=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Headers/__FBSDKLoggerCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			TTkFW5xuSEAOVq8O1cbEJ6/RgQg=
			</data>
			<key>hash2</key>
			<data>
			KjjsJxMgCNjZYBZHCA+t/dUcdoGwgXLLXtNZjYzHQrA=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			ioQQll/iBFQ7MfZ5k8S1w05OKS0=
			</data>
			<key>hash2</key>
			<data>
			Parz8q2MtbyaJr4fjZd9jAEUDMxUNeTBT/pRqo4c45U=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			OkoPZtcS5lu+Hk/2Cmm3NkUC1LA=
			</data>
			<key>hash2</key>
			<data>
			3vDnj3mq4rNEgbA2If/m00wW5JaGeu+mVlK7rYEf/2s=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			o8MMQ2RxBsTwTAF4vAwFP1JbYWY=
			</data>
			<key>hash2</key>
			<data>
			TMNNZtPUGIU/qs2kUvQPGEC2iIKt5ImZ4RhTyr/LjT0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			CSmfnAungUw15mQMizDDGTV7ElY=
			</data>
			<key>hash2</key>
			<data>
			wrCl6/+Xg6AwkRtGiqR/QaRYQhFdO6LBw7yKXixdOEM=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			o8MMQ2RxBsTwTAF4vAwFP1JbYWY=
			</data>
			<key>hash2</key>
			<data>
			TMNNZtPUGIU/qs2kUvQPGEC2iIKt5ImZ4RhTyr/LjT0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			130ouJhMpZj1Z376F5gsUfoyxyc=
			</data>
			<key>hash2</key>
			<data>
			l8ml0KsKmiLEQKTzAYpZRg2k4nhDb5o3vF3lArU4ZzY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			+4xzw+xvpntuK5AhSisGTSzsudg=
			</data>
			<key>hash2</key>
			<data>
			QgUFyksoQJOBn7vsX2WBw89uQ1s+OjAtb6XqI3vhQ74=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			oBbmbiKeJYLes+olDksn9+D2F5s=
			</data>
			<key>hash2</key>
			<data>
			AVLzLc38nhwPK873gha1zQYQiuxDHMbRT1/SkB1rANg=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			qG6Ty6t/qhX430RoQk8cuQ7fGXw=
			</data>
			<key>hash2</key>
			<data>
			rB6rIBwUCwLIwjj0MHS+vm+oRCTLb8bnr7hT9agltoY=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/DWARF/FBSDKCoreKit</key>
		<dict>
			<key>hash</key>
			<data>
			1ICJ/z1AWgvnW7vov0Zaozwtp/k=
			</data>
			<key>hash2</key>
			<data>
			QNi4I6HrS/iiyA4rN9VAorikb+VgA1LJDWTw+yvMNJo=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKCoreKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			inyN4V/w2oSJWzLsD1cUfuJBsxI=
			</data>
			<key>hash2</key>
			<data>
			AhSYA6BYE2e6pbLdYooaiHyKR4g0F6yBJo7AJbAYMUI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/FBSDKCoreKit</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/FBSDKCoreKit</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/FBSDKCoreKit</key>
		<dict>
			<key>hash</key>
			<data>
			A1M7Ma5ErpZr+BkSYlLpHK41HY0=
			</data>
			<key>hash2</key>
			<data>
			LIJitSrl08R/FprsxffavfQgdBT1CDhdZQDTLZQubek=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAEMManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			Yj5aPKxlx+gRF70J6qlF3vXE4mk=
			</data>
			<key>hash2</key>
			<data>
			RRWu3ofQe/GuFslThfnBEdL+ks4YVUnKAckD+t6Kw78=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKATEPublisherCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			HCKpYddhfbeOKPVi+PyxpQEl/Sw=
			</data>
			<key>hash2</key>
			<data>
			4DAoEwvpFC6T6PC5EJZwpa4Ey+A8LZo6rz/k/078BUM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKATEPublisherFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			o4BqZ5pce+E7bPPdIkkE30cwTuI=
			</data>
			<key>hash2</key>
			<data>
			cW1M/vPKiBNWmJ+XzS8iFtLl9s1+5ahOBCQWQ33lFfA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAccessToken.h</key>
		<dict>
			<key>hash</key>
			<data>
			52NScPmZOyhPECDbdwVz3SgFfnU=
			</data>
			<key>hash2</key>
			<data>
			oFHJEVxEJrVA9Y7bNOjGQZ28+CrbY+rbVTIA+GC3LWs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAccessTokenProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			8ssoFJOvhs7F3QcUOp076HcFlTA=
			</data>
			<key>hash2</key>
			<data>
			ttnRwTC/lx3T/wwxDPI/n0tOC8tMavvDSKnOHYtwmJ0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAdvertiserIDProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			spPSWUz3WKLY8u+9ZEUgThNQTMY=
			</data>
			<key>hash2</key>
			<data>
			3TS/lxGjmXQaqCF4fgbFoM/w3nXircZe7UfQRibtXh0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAdvertisingTrackingStatus.h</key>
		<dict>
			<key>hash</key>
			<data>
			s2/tT+xSXPH4xXaQ+yW41JtgT58=
			</data>
			<key>hash2</key>
			<data>
			pj5HBFKU2AJRVkryxLDxsNyV+Hq0vhsL7ESLeXA7gco=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppAvailabilityChecker.h</key>
		<dict>
			<key>hash</key>
			<data>
			Wyf9l4OPVlNw4rmgihSwsLLXekY=
			</data>
			<key>hash2</key>
			<data>
			WCKAfRQSLZ76amGNcy7D85Zr0FqbK3yqgD2x9Q2KMVc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventDropDetermining.h</key>
		<dict>
			<key>hash</key>
			<data>
			kuHhyp+2GLTjun17XfcnCE5AhEU=
			</data>
			<key>hash2</key>
			<data>
			L9oIxJipjM/MQk2hCZ2frITY+t5sCEXfXwbMQZJm5P4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventName.h</key>
		<dict>
			<key>hash</key>
			<data>
			BOUOaSiuKli6Qe/v5ySB3h+M5X4=
			</data>
			<key>hash2</key>
			<data>
			gkInDcjiBNASc1T6fLvTisSC622V3ONdX5cFYRNhq4Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventParameterName.h</key>
		<dict>
			<key>hash</key>
			<data>
			PUlvfFayxBDoZ9T09/HpoVZ7Hgc=
			</data>
			<key>hash2</key>
			<data>
			dourp/NLYssFkQ5Dg6AxyxLObwl5JhALDaFz6lFf9Lc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventParameterProduct.h</key>
		<dict>
			<key>hash</key>
			<data>
			r+KeRcYNIPkXBrMbyUHT2wI7s9Y=
			</data>
			<key>hash2</key>
			<data>
			FIPXmw+JMv7bBSbF0zhfVC2Ib03Sx9JYrwjlNp1XInI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventParameterValue.h</key>
		<dict>
			<key>hash</key>
			<data>
			040yhBlHKamIDlwNu0ImpgLTCqc=
			</data>
			<key>hash2</key>
			<data>
			Q2olYJJI/DN976h566Nwy3D+obhtAQAKHOJ5lKeTfm8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventParametersExtracting.h</key>
		<dict>
			<key>hash</key>
			<data>
			9pOtxV2/CHu04fcr39VsN+sfZ+s=
			</data>
			<key>hash2</key>
			<data>
			BdY6x122D6MPzxunayL68o6siMnk1pbG9+SIttD3Er0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventUserDataType.h</key>
		<dict>
			<key>hash</key>
			<data>
			A3lEI7gxtNx4AHoXWeE0s7u1zK8=
			</data>
			<key>hash2</key>
			<data>
			7pjsRgcXBeV8tJeLjrQvQ/3ZBmzY9k086Z46TsArMag=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEvents.h</key>
		<dict>
			<key>hash</key>
			<data>
			lg573O7sH41xBg8lJrqEit3K/XQ=
			</data>
			<key>hash2</key>
			<data>
			UXJezVQ0cuGeeCKQaxUnK81y4yXJT55f4K9dAPNv5/A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			HzthBYIOOFB3e2msa04M0rtBsRo=
			</data>
			<key>hash2</key>
			<data>
			CPd+ImdtELN8bwE7ctjAasJyW7VT89Q5RQaUxTFXz6M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsConfigurationManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			ANbr0kXkjHZjFTN/QfFH+kdJmYI=
			</data>
			<key>hash2</key>
			<data>
			9Hd2vCTkBhdhGl2O0GrDnmf5Dk+9LGUCubPXDNOSj9M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsConfigurationProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			v6/aYc8HvZlvKvREulESWKamY/g=
			</data>
			<key>hash2</key>
			<data>
			HNsLJH7DmlMk6E5VETGgJDTdrBxFh1tmIqN2HupuauQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsConfigurationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			Q6UHSFjjvfBNsl6lIgbm+E/xdvg=
			</data>
			<key>hash2</key>
			<data>
			Sxr+A8kVMuZThRbBh4I4DH8boozZFBrhAwev32Vh5Hw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsConfiguring.h</key>
		<dict>
			<key>hash</key>
			<data>
			mHGiLeCpVuFdv3XqdFxv9vt7374=
			</data>
			<key>hash2</key>
			<data>
			CYre/9YuQpKmC/OI/41C1Pu8lysIfIKLoct7rQEcEdc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsDeviceInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			SxQnYEiX9/yUxdAbzLI6d4eaegc=
			</data>
			<key>hash2</key>
			<data>
			c8TYlTd79b+8iKRVt8ssLF77TZo5fhxvf5ChNBNfMhc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsFlushBehavior.h</key>
		<dict>
			<key>hash</key>
			<data>
			tGoQfvz6uAy5DjnhPnzX/Ai2vl4=
			</data>
			<key>hash2</key>
			<data>
			iUxqEKL4pmF7f47Qul2Oe8QI0MjDPnOn3VhWjVQWe90=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsFlushReason.h</key>
		<dict>
			<key>hash</key>
			<data>
			CVXh0F8QRsx+pTFSV0t8r1QBBAQ=
			</data>
			<key>hash2</key>
			<data>
			z4aL5e8KYk3I9292BbvFVfL7VvGSXi8r1ULbNY9qC+o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsNotificationName.h</key>
		<dict>
			<key>hash</key>
			<data>
			y7c3PKWx/w77oSbeugClHIvTMS8=
			</data>
			<key>hash2</key>
			<data>
			7JmzpHhHPCXS4WcGYrhN2g1u5YXUgR/ltWdRyfv8l0I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsParameterProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			lSEA6VeT3RQ/9OIKlCWKuogT/fc=
			</data>
			<key>hash2</key>
			<data>
			pCHyiWkvDhXpHUHpcTQYsXWk6DNMxor1yUXbLb1zJoM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsReporter.h</key>
		<dict>
			<key>hash</key>
			<data>
			j2aR5673Kbc66xveJIq2h8rsKLk=
			</data>
			<key>hash2</key>
			<data>
			wg1FOXDGXyIdKy7bYj+hrqcKxtTG/paGmTO6QAjIyZ0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsState.h</key>
		<dict>
			<key>hash</key>
			<data>
			WuHkw89fZW/JsqsG7+MqdROIuqU=
			</data>
			<key>hash2</key>
			<data>
			R6QD42bulvhwFoDu6RdyZBgBVdXMRl4ysZHuVUj1g9I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsStateManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			1npaFG2cN1/3fD1rD7gXkZmnUbI=
			</data>
			<key>hash2</key>
			<data>
			QFynSzFRj8ZNs48YxDNrvOzNjk/5/TKdEvOpAV6gfAo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsStatePersisting.h</key>
		<dict>
			<key>hash</key>
			<data>
			oylwsOMHWTY2ag3rphOgwSAfz7c=
			</data>
			<key>hash2</key>
			<data>
			89C3WF44BG25E9QGywyeFSIhzSN3vIZ8j0Cf1zBc7Nw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsStateProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			KzGafbBYik5zyGw1bm76HKS7Tc0=
			</data>
			<key>hash2</key>
			<data>
			/cQous3za5Y8sBNc9gJnSblGnlcQJNisDkxD0q+XHUg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			5vrPi5Z7t//eIoz9u9sQrS88V8E=
			</data>
			<key>hash2</key>
			<data>
			pGuB3yod13iFDnnyRtuUuYaX7VHyqUUmVwj8DUHOU1Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppEventsUtilityProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			w++q3FMjWosIk0QoN6chRjfvng8=
			</data>
			<key>hash2</key>
			<data>
			5XYISqTXIOZPBrYNgUjayHCm4HMFKWuvcjLD+DYj3GA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			nqhYr/0Yijl6YG6zjg69DkIZMdo=
			</data>
			<key>hash2</key>
			<data>
			TIES03/iWapcNCC9q93neFaUJvaUs7+RvhH7EO++7NE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkEventPosting.h</key>
		<dict>
			<key>hash</key>
			<data>
			Unlyk7QfK8RMiwI6wrySVV1IWP4=
			</data>
			<key>hash2</key>
			<data>
			ynWClusbalHB6D8+QrD6JmUW1Gr2JdcsDTxToJtiJ/I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkNavigationBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			1PU/0JKCKzJJV3POVzX50kAFA0o=
			</data>
			<key>hash2</key>
			<data>
			JmQsfAZrZwAJH5qlBBRvvfzK10IKeqMWfuyED+KYNmU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkNavigationType.h</key>
		<dict>
			<key>hash</key>
			<data>
			3fSnRUTotphysdDrdAvLMxLPW+k=
			</data>
			<key>hash2</key>
			<data>
			hwFJL88C4bRrHRe3Y4ljnl5aCOTIvPg7Oqmz7nhwygo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			FaTKQ3PKUnwjWoYivjhBcoWCvf8=
			</data>
			<key>hash2</key>
			<data>
			4DXqK//wHh7BOXM1el/fpSnI2itx5Xvq5U9scd3DtTE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkResolverRequestBuilding.h</key>
		<dict>
			<key>hash</key>
			<data>
			SXBVCFpg1usB96KY2Km7m8BU4PQ=
			</data>
			<key>hash2</key>
			<data>
			e+6RKqsAlhAYxmTXpFwikKmionJN86SP5HKrZXbAkNs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkResolving.h</key>
		<dict>
			<key>hash</key>
			<data>
			vW0iP2TWioh9F2xVFhjb96AWH/M=
			</data>
			<key>hash2</key>
			<data>
			qb3BIqDSak/QEJVHaa0UyJE2SIhRWJTvILOUETHKNbU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkTargetCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			CSjhIDvr2Fy0xL6BAuVxNcPFzdg=
			</data>
			<key>hash2</key>
			<data>
			YPM7wFIveXtTUS5N5/9q/A437m7KU0YQ6H6B206u6R4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkTargetProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			KjpJOKaE7Hu4fZ/kcuexDfd7h/E=
			</data>
			<key>hash2</key>
			<data>
			xRIohRLTU/DyWAlvJeGXzuTsyj3d0Pb9Erqu0nLl2Qo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkURL.h</key>
		<dict>
			<key>hash</key>
			<data>
			QygAk/iNI8C9zeo9e0YUgaIWY7g=
			</data>
			<key>hash2</key>
			<data>
			GAKSBD0rwQ0puEpcq+BbmYBTAYi4nS6GrhNxJ1vQoKQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkURLCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			oN/+vlRMv1IvKkWrsh+VtDwu92U=
			</data>
			<key>hash2</key>
			<data>
			sKw0E6SLYUeeNW0wOHnRp9kQIhstniWV4X8zLvnmzVU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkURLKeys.h</key>
		<dict>
			<key>hash</key>
			<data>
			9Ke3NaIqqmJ0mx/UMz1Vw+/X9hA=
			</data>
			<key>hash2</key>
			<data>
			Up9j6FGSd/tWYjJkFp+P1BcOj9PPjDtXZlpdfizgL4s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			CSxGCvSd3TFNsqT26linPXGDWM4=
			</data>
			<key>hash2</key>
			<data>
			kp2CnE92bsV1FLjTg72fYgYnBgr5Jsp2QEI1Efop6JQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinkVersion.h</key>
		<dict>
			<key>hash</key>
			<data>
			4lyPD97ZcXbRvhIigNtr3LGnzKo=
			</data>
			<key>hash2</key>
			<data>
			ApoRkmalk5WwUDAjytM+t8cyZVllZTYJC92Fbu0c22s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppLinksBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			TPb6CeLZPTcgS7hT6GviE2JRz4k=
			</data>
			<key>hash2</key>
			<data>
			ummPfWNsfoi+QpV6yx2bMpP5IvBHvQkfcON2myGbUy4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppOperationalDataType.h</key>
		<dict>
			<key>hash</key>
			<data>
			Yxnb0SsotnkBXyJBdPIv4DE+uuE=
			</data>
			<key>hash2</key>
			<data>
			Kyg3KDbG6mD8rK/gtHc1u8If+GNKh/8Uxmbfib1bpXg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppStoreReceiptProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			YAWUu9gn0hfRwdt2dscR4Pjncb0=
			</data>
			<key>hash2</key>
			<data>
			x7PPCf3qyw2K0DAl7af8aPMLzR2TVekwBP1nj0J4Ro4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAppURLSchemeProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			G7H5ArEaw56tAdukKkeFnHJW3yM=
			</data>
			<key>hash2</key>
			<data>
			o9vW113QSBrXeTu8w1RgrMfMpi3Li+ZHpavPt/xYGa4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKApplicationActivating.h</key>
		<dict>
			<key>hash</key>
			<data>
			oHcGo1IxNL4whFjkCAqY0Bezu1A=
			</data>
			<key>hash2</key>
			<data>
			N3sSrIIH8n9PUXaO6Ko042/wGj7FjgmjMz09LpHRsKk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKApplicationLifecycleNotifications.h</key>
		<dict>
			<key>hash</key>
			<data>
			HZOemKjr7HIyL3ND764s1sIhRdA=
			</data>
			<key>hash2</key>
			<data>
			gVCQIdprFkqHFWN03C0LuQuv1i9nrhtP3G3t03o3SUM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKApplicationLifecycleObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			tM9oT+qXMwjCWeGffrZ/7pU9dhk=
			</data>
			<key>hash2</key>
			<data>
			wPjg8wCzbmbbm8y0dJZMh/wce2hP0UMVUsa2oZnCv6E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKApplicationObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			xJEwA88eDINbA4Kq2mRmWsoZVaE=
			</data>
			<key>hash2</key>
			<data>
			jUSJX2r8mLJr8u+wAn2jLp571gJ3SriNQhCpnkSNEfw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKApplicationStateSetting.h</key>
		<dict>
			<key>hash</key>
			<data>
			fy5bOvCaeEMdMssTZs5062vJuLs=
			</data>
			<key>hash2</key>
			<data>
			th4kWyfL9axOR4QeSZnS2+ff21hvfrJBKkVqOS9qpY4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAuthenticationStatusUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			NzmrmWDBawgvUzFKUTG4Zq+D8PQ=
			</data>
			<key>hash2</key>
			<data>
			eSjfSzpHEMWVAvxBo7bJWUgrhMNFvTx8WmBhyasQcEY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAuthenticationToken.h</key>
		<dict>
			<key>hash</key>
			<data>
			Hbt4TrpEQ1E7m0cPJVPJrvfOMYE=
			</data>
			<key>hash2</key>
			<data>
			hc6XPooa0mMb1VzLG43orpr9kKXVeMa+FrWuKr51KE0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAuthenticationTokenProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			zDTRRbEFRongbmR9iUSO35PCbhs=
			</data>
			<key>hash2</key>
			<data>
			mfib/LOFoh5fxGgumdwep5nNFARnqYaIQPovjFKmlwo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKAutoSetup.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZrZsPjcuQHmtIb0VA/2JxYjo0Bk=
			</data>
			<key>hash2</key>
			<data>
			ex/CIuIAAIQMIaocCGblxTLzDaMcZuJvi3MU6lxdgM0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			tLjX3wSZTxU3/edMWI3RtKPsv+A=
			</data>
			<key>hash2</key>
			<data>
			o6EBabARMQ/jGNCu0KhpTrBsI6PnWr6akob4aCraUv4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIProtocolType.h</key>
		<dict>
			<key>hash</key>
			<data>
			DblZg5KWlfoY7uhozPWbR8A+C+Q=
			</data>
			<key>hash2</key>
			<data>
			A5XYnc2oBVddlLz3wGiibtMvO6i/A+82gXL5XY+AsgA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			9p88MKTDxjMSnhRmD3tBUBpyKP0=
			</data>
			<key>hash2</key>
			<data>
			/2BV782KXKlCMETLnCxG2DLQWT+tVDZ/Ijo516KxeVY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIRequestCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			fSrgRjXiEf48iO5SW4tUzCK4Dfw=
			</data>
			<key>hash2</key>
			<data>
			NX+gToQ0lPJ7TSH2qMy4mIDEYMYzkZhwyZbP0XvC1iI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIRequestOpening.h</key>
		<dict>
			<key>hash</key>
			<data>
			0yprixA39qr6F++QUQmDaS+kI5c=
			</data>
			<key>hash2</key>
			<data>
			RkLNO6zfPQbD2c3NWSSdmM3tTFBY3GPfYgBqxAleaf8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIRequestProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			QvIJ69YhObUzXyvQTDogepJbLIY=
			</data>
			<key>hash2</key>
			<data>
			zXyn4IrEQSs/K0vIuWaHZHWeYSHJfjrS2xjq3hrHThs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKBridgeAPIResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			0xHREU5UN9V2806Plbv9YnZCtvo=
			</data>
			<key>hash2</key>
			<data>
			VpEoCY99wbTWk0mTFsof9zSkSb9JK0bc0ty0LHM0GGE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKButton.h</key>
		<dict>
			<key>hash</key>
			<data>
			nRauGR6uRcDNSlw5mNra+SReMhI=
			</data>
			<key>hash2</key>
			<data>
			3PYcJgi9ksT3Oh7qkXSmtIZ4zC05ftjnedLkaQyiEEc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKButtonImpressionLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			LsVpQhR6smaHCPnF/ITEWiKGkKA=
			</data>
			<key>hash2</key>
			<data>
			pShKA3myYUve8S5W/TI08BTWmhPh0RgoZQ6lY9c5S9g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKClientTokenProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			W+JNLAn4YexFixxEQGR3Q9RH0KI=
			</data>
			<key>hash2</key>
			<data>
			GvqZ0xNKA6tPwu3fX7MjAlOAhByiiNjI7PR4KRn+IbU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCodelessIndexer.h</key>
		<dict>
			<key>hash</key>
			<data>
			ua4gDNRjw0KAFXM4vqI1AxIhmgQ=
			</data>
			<key>hash2</key>
			<data>
			FcsOP37OoWFHJHceDyLXTSnifB7NTvxbg8l+Ea2N7KE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCodelessIndexing.h</key>
		<dict>
			<key>hash</key>
			<data>
			vvo1NJ1mTuIU5ZOEX1L9qWcyo+s=
			</data>
			<key>hash2</key>
			<data>
			pLXLVKuG6Qa9BS6uHD2G6ofjKsDr28Rz0u2tNx1QKBM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKConstants.h</key>
		<dict>
			<key>hash</key>
			<data>
			D8KfnDhhBY1wExKSXz3swq16lTE=
			</data>
			<key>hash2</key>
			<data>
			FhwOaPhmdYaDveEEpkZpS+FYAManp/GC+vHLW+hAU6A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKContainerViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			U4pg9nowPmFOUC9fvIpLijlN6fg=
			</data>
			<key>hash2</key>
			<data>
			29xbe5Cc1TOVvV8YSvMCywrkYejv/ToTkWJXi96mSh0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKConversionValueUpdating.h</key>
		<dict>
			<key>hash</key>
			<data>
			fTxcQJg2+ZGuh6rqfJoDG5p6L94=
			</data>
			<key>hash2</key>
			<data>
			6KHP0pS9iTtIWkg6hqMPweWwdf3DKMJVhKQxPa5rhCY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCoreKit-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			6Q0IXgo7gt89R9ybn7xJCG359Lo=
			</data>
			<key>hash2</key>
			<data>
			g7XEQkenFT0vcIVYTUXN6AZV0llkCsNWEqTD4XZWjys=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCoreKit.h</key>
		<dict>
			<key>hash</key>
			<data>
			9NSBO9571MvtFn98up/7ePg4lwo=
			</data>
			<key>hash2</key>
			<data>
			o2TE4Ds12wtlfLBvKjQ8VsMS5tnFEdVj2vJfsqI/21I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCoreKitVersions.h</key>
		<dict>
			<key>hash</key>
			<data>
			/eqaMAzP93B16wXvakv2GDTKX0k=
			</data>
			<key>hash2</key>
			<data>
			Vfex5H/RRx7zB2DpAjP6NiIB1F+xRX28EDuU7fmVphY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCrashObserver.h</key>
		<dict>
			<key>hash</key>
			<data>
			i5LsrVb8OhFQsCQv02NggyWl4qk=
			</data>
			<key>hash2</key>
			<data>
			VLAJlXn/MRpH+6S2wXiJ2MMHTkHfS7Iiq8J4ETcBHo0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKCrashShield.h</key>
		<dict>
			<key>hash</key>
			<data>
			488qfAna0UU+EgR6ICrzMNPuE+Y=
			</data>
			<key>hash2</key>
			<data>
			wrUKA5GCwCODrnCFSkEOYMuhWsXWs+v3tqHYUpWF3JE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDataProcessingOptionKey.h</key>
		<dict>
			<key>hash</key>
			<data>
			2KTIgp4tO9oDInYHDKE4PkSZtuo=
			</data>
			<key>hash2</key>
			<data>
			N45K4T1fNjF4+O/UVhpkoNMCpF0tstXjwLVZpz1m/E0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDeviceInformationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			AhMeLL4pfNP6NOlehkTmCGd908I=
			</data>
			<key>hash2</key>
			<data>
			o//wG1+zWPP4qO37XPTeCIBSdGTXg1o88LARSZXZBpc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDialogConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			BkHb91/H2f5w4nnsjzWvACP+LO8=
			</data>
			<key>hash2</key>
			<data>
			sxJ8YoB2cRIbhfFUnxXIe0KRF4COPsi4Zlpo/qsKI/Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDialogConfigurationMapBuilding.h</key>
		<dict>
			<key>hash</key>
			<data>
			3JigPqy5HGPxZ1EfvLl6K2r09G4=
			</data>
			<key>hash2</key>
			<data>
			DoHcwC+ItwFyYXOM+3jsJKQDRSVYaPDdnK5QjYY6Bdg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDomainConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			z42cVELCq+08IS97csqrsv0G3UM=
			</data>
			<key>hash2</key>
			<data>
			ODDfLUp/EjYZAOC3/MOAnDiI7lc8nLJSqY8sennYpO4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDomainConfigurationManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			vhxTjwLkFlXSayMOST5Du3FF1Ns=
			</data>
			<key>hash2</key>
			<data>
			aJ/dMNcl3sV3TF84MdS1OF9uE+sX9Qh6m5P+Wu5QWhQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDomainConfigurationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			bAZKBepNb9Jwher3IYkkHIK+jM4=
			</data>
			<key>hash2</key>
			<data>
			A0RxB1Xye7jGvnnjZ0gR4CstN5CfaM7agMWrUsOCmHg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDomainHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			4pB423j54rXYv0k1XCxquIvgGII=
			</data>
			<key>hash2</key>
			<data>
			/swwaLXUz3BoEglRz+kpA5A5WTy4IyeJyk8eC4k3/4g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKDynamicFrameworkLoaderProxy.h</key>
		<dict>
			<key>hash</key>
			<data>
			gQze+1wXFmg8HHDE0Ba4/AdlSbk=
			</data>
			<key>hash2</key>
			<data>
			DKzc5A2VHGjb5zjiW11FfYE+be1Je5rxetWvpGpLWP8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			t9pebtrwIlsvol930VzK/Afs/8E=
			</data>
			<key>hash2</key>
			<data>
			DhLqWnWnnr8IYmWabnEPwwtlgNNnXJV/K4weIvUDLz4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorConfigurationProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			rWzO75a4AGVSBUj9usd195pV1pw=
			</data>
			<key>hash2</key>
			<data>
			+cf3Zndluf4/nH6CRo8QwhZjyyogyQr9rRGqFSJJ/Qw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorConfigurationProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			/NYL7EwesbS1Jj4zI5j/M36RrP4=
			</data>
			<key>hash2</key>
			<data>
			hujKKoeVm3HeeiIxu8JoiRbP5INzeD2/vMsdcL57FLM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorConfigurationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			8oEeLY1N7YRaIXucUhviYIeCeUA=
			</data>
			<key>hash2</key>
			<data>
			disBMqvoKxnyr7jUVHZSCd0dYEdLy+w18e3cMgW5INU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			hSTHauBFdEYzYLgpazD8Nu2mbvA=
			</data>
			<key>hash2</key>
			<data>
			J1rXYSPoy877pIwlZKDP6n/b3DufexQeIl0pOymmYVY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorRecoveryAttempting.h</key>
		<dict>
			<key>hash</key>
			<data>
			woJpc0K7/L9yeXE4C+9IxIUoyNo=
			</data>
			<key>hash2</key>
			<data>
			W0d5bC9aorrzc9ohCQNpLCX0lFAN0NEWcKF8J0wW/rs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorRecoveryConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			ycgx9i+ACoXgcR7EKbYFYCxzlOg=
			</data>
			<key>hash2</key>
			<data>
			sxH7BnnlsngUMLwevQeegntSFKDRSmTEKtWhPQj/1Gs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorReporter.h</key>
		<dict>
			<key>hash</key>
			<data>
			mr/DzUsbnLVLYMNNABg7aaa45vw=
			</data>
			<key>hash2</key>
			<data>
			rcdsRdF/ar59iiIPHyagPto5zGT/Sxfh95CnVdi22+w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKErrorReporting.h</key>
		<dict>
			<key>hash</key>
			<data>
			QPo2mKvh+gBY7bUYxLIRori3P84=
			</data>
			<key>hash2</key>
			<data>
			akNPfvBbZsCgwyqes5+H7hRxxdGAzyQYxlJqnkGK4hA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKEventLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			DUSe3ilZQO8BqMsmrVYsaoF7LGA=
			</data>
			<key>hash2</key>
			<data>
			0VHOLZCsy4MIa2IyIiw2m0r7HlIaK1PEG6/P55bLZ8o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKEventProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			gp3AdngU5TgiopBE4rJMoR6ANSg=
			</data>
			<key>hash2</key>
			<data>
			XdMXEDfV+PUzUnWtYK5vvmDq15EbhKM7Msfu34hUKZ8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKEventsProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			O75xhVmbjtVsiUJUjeGE1r788nM=
			</data>
			<key>hash2</key>
			<data>
			yIDs9c5MQ+yOaWXnwlnpInkbHV8GMjI7ZTBwOd+/AT0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKFeature.h</key>
		<dict>
			<key>hash</key>
			<data>
			Oh4B8Z0ZOzOtzlb70Z4TPxT1NQg=
			</data>
			<key>hash2</key>
			<data>
			V1hpI7TAZqwqEOEABosdtY1n2cBJTV/NzjeKNw9UDUc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKFeatureChecking.h</key>
		<dict>
			<key>hash</key>
			<data>
			rYTkx84W03mL0rrno4sthw6poiM=
			</data>
			<key>hash2</key>
			<data>
			QtYErERzFYGRmUpt4HXd8p062xQtrNl5l+J2nUhhc1k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKFeatureDisabling.h</key>
		<dict>
			<key>hash</key>
			<data>
			5RTMQMD5BH2IHczkFRDwcDa6YEc=
			</data>
			<key>hash2</key>
			<data>
			TKR1xKxgrd0N8JUx6kxkmWjLK07R7jMGOSWpnHlMoqA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKFeatureExtracting.h</key>
		<dict>
			<key>hash</key>
			<data>
			rLjAEFGlg1SFdt3LKE/Vyvpy4C4=
			</data>
			<key>hash2</key>
			<data>
			9+2zhi+7pfcv33U40/OTdeLKZgpJVvbygggabihtg/Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKFeatureExtractor.h</key>
		<dict>
			<key>hash</key>
			<data>
			UpeejK9e6KddzNj4pJ/187IOAok=
			</data>
			<key>hash2</key>
			<data>
			kEO+YvkRcV483BfguxWKQ5uS0+Qmx3F3mL3dsHGkRks=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGateKeeperManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			UApODVoVBgYDmemuf8pq5KhSZ2A=
			</data>
			<key>hash2</key>
			<data>
			7mWH6MgxmutTpF+sXRQKlG6n5MLh4o5QHROkkRPhs1E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGateKeeperManaging.h</key>
		<dict>
			<key>hash</key>
			<data>
			MZUyM6YX2TAfccBQg6xiTBypZ18=
			</data>
			<key>hash2</key>
			<data>
			ZRWGI6azeE93UU6MLd4PVLDsl0+khZ9ajsIyRJrwYNU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphErrorRecoveryProcessor.h</key>
		<dict>
			<key>hash</key>
			<data>
			aNj/uzA6QMePztxIxRll77xHLIg=
			</data>
			<key>hash2</key>
			<data>
			wyEA5FnCIsiLQMekTMTbQfJgpsp5uPtXVy0RVKPm/Co=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			W0+x27i8eT+6EMZ3lVcQXPKsAP0=
			</data>
			<key>hash2</key>
			<data>
			6GPfja4r0l+3PHnf0yfFzGgyV0iPVl+wm7ooUBBX1M0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestConnecting.h</key>
		<dict>
			<key>hash</key>
			<data>
			sAlo7za+Sf4zj8Xg+Wy83BUtRCE=
			</data>
			<key>hash2</key>
			<data>
			/aivBeJLeVgjejn+J7Ve8A1PgbyFwWSEIOEpNrMZwLw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestConnection.h</key>
		<dict>
			<key>hash</key>
			<data>
			NUnPpwpwUkVhUBU67o81VFaERfo=
			</data>
			<key>hash2</key>
			<data>
			1ICPsAdQVISoM+fhXtKdpyBkCX9WIi5bqIEJz2IfkHA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestConnectionDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			FSHiVcHDpJTlfdfBczQNHtjDJ8s=
			</data>
			<key>hash2</key>
			<data>
			pbeIgtjILQ+9lWJviGFFqpO8IqXWnVf2DQ094YKDKMY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestConnectionFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			gAfT3DO/vuTTlEaJPEP8hL5P3Eo=
			</data>
			<key>hash2</key>
			<data>
			xgTvIuiH3O0GP4dAfx+lVweVvj/3VUFzk/ZwDkEd6UM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestConnectionFactoryProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			A26a5H79Zb1dRO6YHMFB4DbS+D8=
			</data>
			<key>hash2</key>
			<data>
			ZSBkNkUs6k4hh3lOO6aa5+j5kuh9vwSm6BTbH++KEH4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestDataAttachment.h</key>
		<dict>
			<key>hash</key>
			<data>
			7vvCqPiZp4o6JKVaJJ4FP9XkXKE=
			</data>
			<key>hash2</key>
			<data>
			rrQm0dv7u0VNuBOOr4bOsLq22U2VRKN5+/8z8dvg8ac=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			lAwX1CKv5VHiJ07/xZZylICOdg4=
			</data>
			<key>hash2</key>
			<data>
			VHji6+eQJ/noGhXoav1+rDhYGkeNSPac5f3JMIMO4OQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestFactoryProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			ydipmJZDsiXmT3/5DNEiX0gzsNs=
			</data>
			<key>hash2</key>
			<data>
			LGCTe0D34qYvAzYLEDR50t2F4wbgmylanIR+2HAcV/E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestFlags.h</key>
		<dict>
			<key>hash</key>
			<data>
			Zas2ccUoNaCrjUffAdLC6TmKLWs=
			</data>
			<key>hash2</key>
			<data>
			QaBxTTw493IFQLv0fwkhBkWu57wAPkAZ/fexBSmcWuA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestHTTPMethod.h</key>
		<dict>
			<key>hash</key>
			<data>
			sF4WT7ko2ZXuQ91thBewwSb29Cc=
			</data>
			<key>hash2</key>
			<data>
			s/ZdV1PYtfb+e5MToTE5eWQ/g8Ea8Lfbn1y5cPXIois=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestMetadata.h</key>
		<dict>
			<key>hash</key>
			<data>
			4ZYoDzErnijlAw/YdD1e2qQ3eDs=
			</data>
			<key>hash2</key>
			<data>
			Twh3qoAgt3ReSFrZuMn8tfYVVZ+eHo/Z01B//qRL/aM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestPiggybackManaging.h</key>
		<dict>
			<key>hash</key>
			<data>
			RzvPVhBs2lDl2xH7PANCNXkswIE=
			</data>
			<key>hash2</key>
			<data>
			wxiIa9cDFOlxQYXM+TkWOQooJpOkR5UkCGuNOfRHzCo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			2WftYLHKj8D0QUQgx2F/+tCFX3k=
			</data>
			<key>hash2</key>
			<data>
			aKkuXQ5CLQQh3MSzr4CWjSGHECgsjZlvDwFTFbRrrFI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKGraphRequestQueue.h</key>
		<dict>
			<key>hash</key>
			<data>
			YazdAJ0rcrHmvRHvxaduooEWglg=
			</data>
			<key>hash2</key>
			<data>
			jg27Rw8BPOqfHG/lqSFvmFumU0quxxvP7wdja0rMIq8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKIAPDedupeProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			jFfQ2uToisxlUwu5HAehBwtT5yc=
			</data>
			<key>hash2</key>
			<data>
			mzR9LdGDTqtc3DYV+JOsBkVoOuodrkRWOO3EnpWU9C0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKIAPFailedTransactionLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			3naly4poGndtl04IKJLRhIn0y58=
			</data>
			<key>hash2</key>
			<data>
			YxYLbyBk6Uw+vYZwpHHGp5GKRCS9QE/hRs6s5QPT0uw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKIAPFailedTransactionLoggingCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			8f1KPldp/xKn7SdJ+extn1OcPWY=
			</data>
			<key>hash2</key>
			<data>
			ZzaGG8LO/3EvuN9LDMk/455U5A74GiZMmXHsNKy4Zlk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKIAPTransactionCaching.h</key>
		<dict>
			<key>hash</key>
			<data>
			EUOfrTb9ijRfv++ZOI1VjUF46+o=
			</data>
			<key>hash2</key>
			<data>
			bjiGwxnSAUlt+NVqp+emo6xGvyxyQzPz5qaW1530I/E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKImpressionLoggerFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			nfa97DKHYMz7v0+pUDy3nlxrUYk=
			</data>
			<key>hash2</key>
			<data>
			QuBRqz4neAxQgnQCCfZVT7P/volyw8iY5KgrMKRJmJs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKImpressionLoggerFactoryProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			3LEI96v+5GIEFysj3D1C/rG8ihw=
			</data>
			<key>hash2</key>
			<data>
			Us0Ez3sk3OsuatGsASCqoKebAXNWPg9S5pMOJ5DxDc4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKImpressionLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			wrrEayG2/hN5wUceRYRAU8HrXBk=
			</data>
			<key>hash2</key>
			<data>
			Vprv+yj1TQTAVjk0AdI+90h3w3DcuHwxVoAECMAadvM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKImpressionLoggingButton.h</key>
		<dict>
			<key>hash</key>
			<data>
			5jJQoQEgcqekG5Zl8U42hb9oI80=
			</data>
			<key>hash2</key>
			<data>
			ndY9up8dSnK60T7Qxxo1R50wARczYvrrA0RXdqjCzKI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKInstrumentManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			b4LuHg6PamZ7IBBxVoOYpxuYERM=
			</data>
			<key>hash2</key>
			<data>
			+5qq+ul/kPhxOgEhuLz8XmNc60qQeWJkTw9obciSaz4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKIntegrityParametersProcessorProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			YAejrtcnEaVtGKUEB4ZJpyeATvQ=
			</data>
			<key>hash2</key>
			<data>
			o5WVcgrcNZ9fy9iy8pkmLgt9cY41GNSvTFg3Z4b9k5A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKIntegrityProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			87ZiOpw4U2tli0OR6o2FOZV0Szc=
			</data>
			<key>hash2</key>
			<data>
			ZIhsJR2yehcaQancBDHC1s72R+hCE/frIqi5p8edPn8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKInternalURLOpener.h</key>
		<dict>
			<key>hash</key>
			<data>
			FLV0q7bfHv0++Xz2yX93rch6WQk=
			</data>
			<key>hash2</key>
			<data>
			d7pu09v171CZOLDOnWFtkt5TfA+u9wD6UYyoWxkSA+w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKInternalUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			xVnxfL7eTB7Crbqhijh4imgTplo=
			</data>
			<key>hash2</key>
			<data>
			fGPA4wFG0PbwsHF1HMG8uXRBNUpRfuVBclcu0v5TDCk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKInternalUtilityProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			/ZcaAKHHVRGsTO5fkpWQKXtwRlg=
			</data>
			<key>hash2</key>
			<data>
			QxK2NWyHrM+GqD1pK5e3LD2xbiZX/p/UzObquLThWI0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKKeychainStore.h</key>
		<dict>
			<key>hash</key>
			<data>
			VSfBFlzguwgAJhVey77PM3TiKzI=
			</data>
			<key>hash2</key>
			<data>
			sQGCel/07cMPMZI0kl8wscwPV9WL/JIYf1ns0PSf8v0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKKeychainStoreProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			Vl4nIrUwT7cqcjwlXymbPkKUVDo=
			</data>
			<key>hash2</key>
			<data>
			XNfcYyJYq69j5eL0ARtTlzkh8SdVCq+G/s9eY4Pd8O8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKKeychainStoreProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			u2GnomfI+oHCeoe6Ei9pG5quwVE=
			</data>
			<key>hash2</key>
			<data>
			/INTsqlL5Gr82q1fsrI5Mm6ox4RJoy9zwOvstKFPXrI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLocation.h</key>
		<dict>
			<key>hash</key>
			<data>
			lVldFN//gmPckkWOntm6/lMe0QE=
			</data>
			<key>hash2</key>
			<data>
			4VM07vWgUKPPsLEMLF29hXYKIHBkc9vETSX506Z++Uw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			19xKJkbTRpwMoQwzaC6Nu+w5tME=
			</data>
			<key>hash2</key>
			<data>
			O7DI9fO361qB0Y+XI/ka6eMk5/LMGvTE5T0NPkcTBBI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLoggerFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			oN6VhKse4/3rPEKNFGojETNkg58=
			</data>
			<key>hash2</key>
			<data>
			e8PHQtEWsLuVh+XXwDbUAFSXUORwmsnkmOyyYz+ph8g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			/DbryGZcqEQACAktvCjPjV6SDG4=
			</data>
			<key>hash2</key>
			<data>
			IvKTyTv5bHSAJcZqLwaHR/lW5CFnjIggFOzHMRDWMk4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLoggingBehavior.h</key>
		<dict>
			<key>hash</key>
			<data>
			lNVc//dYNZlhHrZHLByspMT+TeY=
			</data>
			<key>hash2</key>
			<data>
			f5RLVvxNd/VtlSpoMhH6nO6jFRiC9rudIo+algkCGBs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLoggingNotifying.h</key>
		<dict>
			<key>hash</key>
			<data>
			ClxijYzHweoVytnlGNbT1n62Q2I=
			</data>
			<key>hash2</key>
			<data>
			/H/LDFDn+fA0n3pb11qgMdP8PeuXl02axVuT5C9K6nc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKLoginTooltip.h</key>
		<dict>
			<key>hash</key>
			<data>
			t9qlwGoUeyWhxDfXE8Ky6RnF/gg=
			</data>
			<key>hash2</key>
			<data>
			C6wHDAq5ukwucR1FkTnnq3ucsw6y7GR9wDadgB3zHZY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMacCatalystDetermining.h</key>
		<dict>
			<key>hash</key>
			<data>
			Fy2901RMaq8v1KZ1nkSvVuhYSN8=
			</data>
			<key>hash2</key>
			<data>
			dOVbOuPHBVVE2h/fz8iXrr824nuLBVRrqymrNwdF5kY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMath.h</key>
		<dict>
			<key>hash</key>
			<data>
			rYMaHGq5qMdO0PMhIT/NeaWQMFE=
			</data>
			<key>hash2</key>
			<data>
			RQb23pN1EiaRezovWmrJpZ289Eby0ndYmkHCpsWKbXo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMeasurementEventListener.h</key>
		<dict>
			<key>hash</key>
			<data>
			OVZSC/JIKFj+KgzAwt5biOqF7rI=
			</data>
			<key>hash2</key>
			<data>
			ELmJcxc3dSPYAYyrwqHJpwUxM4is2hfU27i90WTOYQ4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMeasurementEventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			9XUuzXpHXt6PyqsDNvqKaCYOsaQ=
			</data>
			<key>hash2</key>
			<data>
			Xy6hXD6E/laXHrWOeDhRU5cQKCbE05HcdVjR5tdUx6E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMetadataIndexer.h</key>
		<dict>
			<key>hash</key>
			<data>
			yvEMxozxtAwiiPOoouj17MOx38w=
			</data>
			<key>hash2</key>
			<data>
			FC2xPfBPJ+KkA1lGRnOxuFDbWIJc5fChKOpjrK4QYMk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMetadataIndexing.h</key>
		<dict>
			<key>hash</key>
			<data>
			0XbHu0BpTUjfkrPvYn1B9+H0Bww=
			</data>
			<key>hash2</key>
			<data>
			QjaWgUOa4ftPTfJONsDDsoLADLIhk0afRL0wWiGd9qc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKModelManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			UTvH2kBUyG0vy9bmR5tucIdzDMU=
			</data>
			<key>hash2</key>
			<data>
			4HNzWyenJC3JFcA8hUl1k235rwmiyBYrmtRHx6i0w2g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKMutableCopying.h</key>
		<dict>
			<key>hash</key>
			<data>
			CdAKmAi79FHfugMUCBcou38XjyY=
			</data>
			<key>hash2</key>
			<data>
			9WETC6Qraw3B3QY90JfYu/elsAnM/L40JTEsRAOO+hQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKNetworkErrorChecker.h</key>
		<dict>
			<key>hash</key>
			<data>
			lc4ltIsnGN0wefVKZeW3BTQqt8o=
			</data>
			<key>hash2</key>
			<data>
			mQPfqbSnxLTJW64KKhoGcZPQYNTYQABLzG1AZ2hcTSs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKNetworkErrorChecking.h</key>
		<dict>
			<key>hash</key>
			<data>
			DQOOpk+tae6sTARv6zgYkUNQv+4=
			</data>
			<key>hash2</key>
			<data>
			JaxujLpfeoL0uJ15AXk/+TxmnQ+XgmtHYz9sb5k3r1w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKObjectDecoding.h</key>
		<dict>
			<key>hash</key>
			<data>
			rsoqTN959edBc1dWsOJAk4SFajc=
			</data>
			<key>hash2</key>
			<data>
			qsowPp05Itw5uODC4pGjeSY1WE9ntYKWLfZW7A0nNw4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKOperatingSystemVersionComparing.h</key>
		<dict>
			<key>hash</key>
			<data>
			jd/kWOWeuMfcK4hF/tgU6AeS2yU=
			</data>
			<key>hash2</key>
			<data>
			KaQv/BSjIbO1IWul0m3Ckagnf4cBrNKnGm3fhM4GJcg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKPasteboard.h</key>
		<dict>
			<key>hash</key>
			<data>
			4+oJKTiDHC9gezS8CCuhdWHO2gs=
			</data>
			<key>hash2</key>
			<data>
			UJnUSOEkUYzGWK3fOCD3HGGdwQLxYhZg4znp+HpSNGQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKPaymentObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			2F/w1aia/WcWnKwAGffiRgYimWQ=
			</data>
			<key>hash2</key>
			<data>
			g21RW5m5mkv5jnc/g4IZvHnCPQLSjxGoaSvN3vo4UZw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKPaymentProductRequestor.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZQ6Btyy0z7ZiD/M9eX+7+KDRiLE=
			</data>
			<key>hash2</key>
			<data>
			S2CSCz82/VNHbyry4LOyJvanQu9UC+br3C0NUFHHqe8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKPaymentProductRequestorCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			kofChLuS6UMw2biE8Lcu2yhsmt0=
			</data>
			<key>hash2</key>
			<data>
			fGzWAJQkZyRBklHMViipfo4KyA1fMAOzlOiH4HhweQk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKProductAvailability.h</key>
		<dict>
			<key>hash</key>
			<data>
			4z6lAOLiyG+H6sMmuDzBXlrBO4Q=
			</data>
			<key>hash2</key>
			<data>
			AfSg3sbP+VegxUAApbWi9NSI+/dlu9LbDGiLvCWo3Z0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKProductCondition.h</key>
		<dict>
			<key>hash</key>
			<data>
			p2M86R+0XjuIIHBALGh4qHhF0sg=
			</data>
			<key>hash2</key>
			<data>
			dNGTpMMgyZMruD+nBPSsD0Y3Bc2L8ZoTcsW1f5tdK7Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKProductRequestFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			7BIaz0Ab6i62/Dq0UaZpdoNPhCs=
			</data>
			<key>hash2</key>
			<data>
			6bp/2xb4hfUOim9SEuGgPr9z+Bysu/FK27psJ1WxipQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKProductsRequestProtocols.h</key>
		<dict>
			<key>hash</key>
			<data>
			quFgypTeZ+2XNvJr6deEaYceClo=
			</data>
			<key>hash2</key>
			<data>
			a7mWH2GsxBZ9X9N8Z/19nO+toxG/0r99YGYxK28kY0o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKProfileBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			m3b2HY43bVO+k7tLfgTUHlel3XM=
			</data>
			<key>hash2</key>
			<data>
			4/f1aRcfzmU+yr0ypnHjdfSBpIQ8QxluAy3Yn0XxJPE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKProfileNotifications.h</key>
		<dict>
			<key>hash</key>
			<data>
			p/v7YsyAewf4Zx5PgVOw0g9689k=
			</data>
			<key>hash2</key>
			<data>
			yWNI2yhxUUa25VEKtZBJViFYRTXMmJxx1JUrJbK6QAs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKRandom.h</key>
		<dict>
			<key>hash</key>
			<data>
			rqrUnE5WdNwaBw/Fm/Sauc2KceA=
			</data>
			<key>hash2</key>
			<data>
			/FYnxRG5dQwkxSXWGGkucUnk7tABHKmrqud2upLxMSk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKRestrictiveDataFilterManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			FhBIBCgefnNx6Hx9hBvjEQxlgTk=
			</data>
			<key>hash2</key>
			<data>
			mSbOfI4ZD+1zdJOwvNbPEvSXVCr/iH1TcmA+bmwJW5k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKRulesFromKeyProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			9/nqsZZ8Epe4NBnTPqEOptCNdzs=
			</data>
			<key>hash2</key>
			<data>
			KU18OwgqjgGW8bB4DSVWVWXRm2R5Uh9WKEHg/7D71/c=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSKAdNetworkReporter.h</key>
		<dict>
			<key>hash</key>
			<data>
			N8+fSt031r3BemNKfykFVGaMU2g=
			</data>
			<key>hash2</key>
			<data>
			uqe1+q806a6PrWGJ1z275ZtkYMC6hW+JEjEM1j+UxFY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSKAdNetworkReporterV2.h</key>
		<dict>
			<key>hash</key>
			<data>
			t1JQiDuyttg76dcS901/oxfu6tQ=
			</data>
			<key>hash2</key>
			<data>
			hyqPE+oRV6Q/uWySMPT2HXaeoU0Fhbr/rzJPAblC8zQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKServerConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			ePai1Ct6rcU1fbdNFDdAFZgZLYo=
			</data>
			<key>hash2</key>
			<data>
			svD59mf/yaDmtJnt9mqyUETp6BqGqszyESR3t8RUtz0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKServerConfigurationManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			MabAusZdKvvZ5Ny2wrhRTzvzRhY=
			</data>
			<key>hash2</key>
			<data>
			7bxzdmwW3VP1D775pYiEPDzkARci0KU5+G83fUIv+Lk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKServerConfigurationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			OVcy7K8/6LpbY7Ls9wBkMeiLOXc=
			</data>
			<key>hash2</key>
			<data>
			nggJ6UDcdQ8WAvG9K2eWGIr1+MNgK3/g6Tj4sImbWHM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSettingsLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			j4NKiO1um7BzI27sPShA+WNNV6E=
			</data>
			<key>hash2</key>
			<data>
			GgfZ+r7AkNT2pA6iA72tqgExzEgxS0MRiVNUSlpsa48=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSourceApplicationTracking.h</key>
		<dict>
			<key>hash</key>
			<data>
			eyc/NF7kaz05PnojBKao9RoOkXo=
			</data>
			<key>hash2</key>
			<data>
			qajy5wvDILF4b7Jcx9hTizBUhaT1i2GXvpxik1TzkNE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSuggestedEventsIndexer.h</key>
		<dict>
			<key>hash</key>
			<data>
			eLTHBeEjKRaaEKDEDTrfz5o1+pE=
			</data>
			<key>hash2</key>
			<data>
			o+ccIBHzx5V9J1IZLNYIg+BkWS3Kl3rMRYXWeWRdhDQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSuggestedEventsIndexerProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			Nlpu1MobSLVgHiTs61VhpCb8F84=
			</data>
			<key>hash2</key>
			<data>
			9kKE4jZryp1a9dSaPrjDlA+9RhscC9+Yq3GlCBL0DpU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSwizzler.h</key>
		<dict>
			<key>hash</key>
			<data>
			PgutzEuZyjT9se/U24OTeoEMo1g=
			</data>
			<key>hash2</key>
			<data>
			0n+HQ6UGePndL7zICwSJXV5WRg12HV3lB4DiARhtWIU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKSwizzling.h</key>
		<dict>
			<key>hash</key>
			<data>
			jMRYakKevWrqsOerYuQ9Fb1eCT4=
			</data>
			<key>hash2</key>
			<data>
			KFyWd42cMnaIyExVvONG1g3JPBSvnv/4xdag+5V9zDQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKTimeSpentData.h</key>
		<dict>
			<key>hash</key>
			<data>
			Zgill5e+tNHLsGRpZ8nXHCk2iEc=
			</data>
			<key>hash2</key>
			<data>
			4onV/tAXCdp3PH0pHlu7hEtJ7bg+cH1JUhZKPPfhxms=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKTimeSpentRecording.h</key>
		<dict>
			<key>hash</key>
			<data>
			NuI7oI0R+b4/s+KiUbS+MEn4x8k=
			</data>
			<key>hash2</key>
			<data>
			Xi9idQwWvd5DFqQNYAbll1aNfRM0zlSsrHt8LqUVh0E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKTokenCaching.h</key>
		<dict>
			<key>hash</key>
			<data>
			cla0KYAtpSCy2GcXC++XdSQrxck=
			</data>
			<key>hash2</key>
			<data>
			aqvT3pRj7C448KPxobWR9AZGsr5TeBuYFw6Fe2phX4o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKTokenStringProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			43wQbjzLSB+cSu9hTQZ4tJq24Mc=
			</data>
			<key>hash2</key>
			<data>
			TTwDb4bRpxFALwHRV7rPZ9zwNFPGZhy34If3avetGjM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKTransactionObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			lZIW9x0Pm8LlHaUourw8qilnS7I=
			</data>
			<key>hash2</key>
			<data>
			NOPfkdHDnW6uD+jIMqNB/e5UkbR4E7d1qzrjahnOHdo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKTransformer.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ui2GFPACS7T6kK9LcCLcdJyCYyo=
			</data>
			<key>hash2</key>
			<data>
			76ADDGvmmKAAjwJQmqZOKN1QRhrUmGE9qELPwGG2F4k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURL.h</key>
		<dict>
			<key>hash</key>
			<data>
			lg+hfhk8xZ7goFdbGZ8cVtCyNbw=
			</data>
			<key>hash2</key>
			<data>
			MC/UJOZYmjruWzpiMns2o9iOpycM+cvnUnx2d+brcDA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLHosting.h</key>
		<dict>
			<key>hash</key>
			<data>
			O+RAgM4Uu49yrzKRLeqtv9FfVKE=
			</data>
			<key>hash2</key>
			<data>
			tY5HluqD9iw44nsX/G1SrqCii9dfnxqiv8T/kapFGQs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLOpener.h</key>
		<dict>
			<key>hash</key>
			<data>
			/9/EmnpZWM6KdVyGGEI1NUvMbYk=
			</data>
			<key>hash2</key>
			<data>
			DUeUDJEfTX1NM5s8QxHytKu4/avgUtMZIXnRyx+sTF4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLOpening.h</key>
		<dict>
			<key>hash</key>
			<data>
			0r8abqnzwBXvpV5PlCa/kQ+OdW8=
			</data>
			<key>hash2</key>
			<data>
			djQLdPxXd/h2GC826rf/QKm1UM7Fb9poUtZsWIB9K/M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLScheme.h</key>
		<dict>
			<key>hash</key>
			<data>
			36HfFNYLwWfRajDYFDJeNZe/evc=
			</data>
			<key>hash2</key>
			<data>
			BdpEnJgCwk4m/BX4jdUcFWtch+dOASiPi4b4XuShRow=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLSessionProxyFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			fkszrJOzG1OgfuDotJLKT74xw1w=
			</data>
			<key>hash2</key>
			<data>
			nFIFaAzvqWS22hEvcMkix5YaPcK4GBmjn+7lCb40/RI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLSessionProxyProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			5Hely+G3LbaFH81TEA3aS8tH8Xk=
			</data>
			<key>hash2</key>
			<data>
			aTA0C16KsECkZ2E8HCXO7ce5CfBhS4krYqHjd2sMT84=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKURLSessionProxying.h</key>
		<dict>
			<key>hash</key>
			<data>
			LIrnTeexPfrk+a2pllxOcLBqw+Q=
			</data>
			<key>hash2</key>
			<data>
			xCWdJ07ZEEhMehbPyCICo2qsde4nyWuid/4r5sZNYM0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUnarchiverProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			mBxizULPLClFmFkzGvq7DZbedIU=
			</data>
			<key>hash2</key>
			<data>
			km9PeIz2mArhmhaSjTUxnyXNDnyTw45ONMOIrFvX9Ng=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUnarchiverProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			PU6uhik4cqPkhqWAmJpctYAS+vI=
			</data>
			<key>hash2</key>
			<data>
			oCPV4SoPYuNAh0Y+MkV7TYC6T/3tFQbq2/dC8tplruQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUserAgeRange.h</key>
		<dict>
			<key>hash</key>
			<data>
			paq4PxztwLvJ2EQKujqXLJfgjiA=
			</data>
			<key>hash2</key>
			<data>
			pLTGrXlcRTNXtDJu1omtFNVR7Tl9niHZllskPvHvlLI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUserDataPersisting.h</key>
		<dict>
			<key>hash</key>
			<data>
			eiIDv79NpGtVgJHZ+jovQsS4ZsU=
			</data>
			<key>hash2</key>
			<data>
			e5bOiJw1uxd3xCQKKVryAoxKqTxaIVcOMbL0cYWLyJo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUserDataStore.h</key>
		<dict>
			<key>hash</key>
			<data>
			iSw84njE3Q4fckPhhhUCpEMT4Uw=
			</data>
			<key>hash2</key>
			<data>
			RY2Lg4PYOft0K2wbJtT1axcgmL2lJ5PaGtXZga3hQjI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUserIDProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			1G3kOunQeQMavUujt30qZv1Oj98=
			</data>
			<key>hash2</key>
			<data>
			n8EfpaG2V2BCI5xE/8Hz8+83cLBSVMqXjkEx1Gv6r6E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUserIdentifier.h</key>
		<dict>
			<key>hash</key>
			<data>
			EqrAGtLbcJRfQwl12songIdMMEE=
			</data>
			<key>hash2</key>
			<data>
			TIFVVKaBj4BvRv/gFJvtxkh3Bm/40McFA0531BAu5AI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			ACK+e48w6WLwZDhZT9VIaXDWTlk=
			</data>
			<key>hash2</key>
			<data>
			aDJa31ufENPB6jQuSRYbBb60HcsTocEvvcqZw2iWBN8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKWebDialogDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			oPLwzJ7KUTr8T5hq/c983EM1rfE=
			</data>
			<key>hash2</key>
			<data>
			RhnzKhKPGNsn+I0efiSBdbYdU7h+Z/m5R4mQUqXW8xA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKWebDialogView.h</key>
		<dict>
			<key>hash</key>
			<data>
			EHOQKin9zYzG+gmc/LfBegKrvEE=
			</data>
			<key>hash2</key>
			<data>
			HC8WLkRk5PhVWGbE58hLSP/goSUOleGYhuUzBNm31v4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKWebView.h</key>
		<dict>
			<key>hash</key>
			<data>
			jUhFO4/5Ly1VNml/cAryOayE4Ag=
			</data>
			<key>hash2</key>
			<data>
			yZ2ZBoGlvdRcTOHXhCl80pgVGKFXIQXl9HClExQmfbc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKWebViewAppLinkResolver.h</key>
		<dict>
			<key>hash</key>
			<data>
			jEC9UH9Inm7DYqoFZv2qaN3Pe14=
			</data>
			<key>hash2</key>
			<data>
			+Ac5AbGpEiHL5SuKIFm5ORqswif+O0w+zlIxL1qgUd4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/FBSDKWebViewProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			6dBqgFJpP3qYvCEw7KHe/d5ieC8=
			</data>
			<key>hash2</key>
			<data>
			2TVzs8L8DTwpM3xFUOklhzjkivHzm2VLIiY+IXFxMnk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/NSNotificationCenter+NotificationPosting.h</key>
		<dict>
			<key>hash</key>
			<data>
			4IqhFgtQjABnwxNo0vP4+5Q/hSU=
			</data>
			<key>hash2</key>
			<data>
			/hTFkvjgGwyFzsQ6VB5CKDoMAKTrun+jQ7LMU4Bcfws=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/NSProcessInfo+MacCatalystDetermining.h</key>
		<dict>
			<key>hash</key>
			<data>
			P2PZ0k71jzSsWpQg6vH4TOHr6SU=
			</data>
			<key>hash2</key>
			<data>
			N5KvICMBUqwJ3JveHhLX4bomYQIHATUqScz3F4Vyf8Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/NSProcessInfo+OperatingSystemVersionComparing.h</key>
		<dict>
			<key>hash</key>
			<data>
			7p95y3GgCy4C7g+8xfGVXW4sr18=
			</data>
			<key>hash2</key>
			<data>
			G6fi3L4ee8mocOfWvq0r6J1Iy2HRUZ7PyHTmpxrxo08=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/UIApplication+URLOpener.h</key>
		<dict>
			<key>hash</key>
			<data>
			N6kxbbjqI4XB8O7cVqNSb4VtaOs=
			</data>
			<key>hash2</key>
			<data>
			LFldGsPohxFxp2EMtRNr9DW5offC4saf8wAKpZaih60=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/UIPasteboard+Pasteboard.h</key>
		<dict>
			<key>hash</key>
			<data>
			MKDA9GrF2o6+h3tbW9e9adSk23k=
			</data>
			<key>hash2</key>
			<data>
			JdRN7qh5XEBay0JfDd6bXvUO+ciilKne5qAXVmNWoyY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/WKWebView+WebViewProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			9AxT3+g6DPZx5q3rQWhRBMFJu64=
			</data>
			<key>hash2</key>
			<data>
			Uo6vnwYxxFh+V/+J5U7GTu7dFxg9xliEGHkxzgAJYTs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/_FBSDKNotificationPosting.h</key>
		<dict>
			<key>hash</key>
			<data>
			w1Gpzlc2q889xvFT3+ffPk2kwQU=
			</data>
			<key>hash2</key>
			<data>
			ZBUZDT5SUs3wBxxP06/A5KDaxedomK4NJ7axY0qD7jY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/_FBSDKWindowFinding.h</key>
		<dict>
			<key>hash</key>
			<data>
			Gac9mAAYHny41SRhpW53CbfSo2s=
			</data>
			<key>hash2</key>
			<data>
			QxPymhBROXgyvxD0bzeN+T5ennsD7zaelwvA1a2l3oE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Headers/__FBSDKLoggerCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			TTkFW5xuSEAOVq8O1cbEJ6/RgQg=
			</data>
			<key>hash2</key>
			<data>
			KjjsJxMgCNjZYBZHCA+t/dUcdoGwgXLLXtNZjYzHQrA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			mnFamF7Gc38H2o21RWTTfDURTgE=
			</data>
			<key>hash2</key>
			<data>
			LLjPiY/OCkprLw8wapFjBaET7cMNKbOtVipQPYHElfg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			FeQzanpLn8r0q90XPcUQUGrbvpM=
			</data>
			<key>hash2</key>
			<data>
			9vqiFoTW/zYeS6mZ80D6PlnQpuMgrIjcU/+646FQOZc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			Mj7ipDJanXwEXSMXDQw3r60NqAU=
			</data>
			<key>hash2</key>
			<data>
			DQLbZaJFnhnaV5DNWVoUAtskGyHw/CC5zVNBAm3jyuo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			FeQzanpLn8r0q90XPcUQUGrbvpM=
			</data>
			<key>hash2</key>
			<data>
			9vqiFoTW/zYeS6mZ80D6PlnQpuMgrIjcU/+646FQOZc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			mnFamF7Gc38H2o21RWTTfDURTgE=
			</data>
			<key>hash2</key>
			<data>
			LLjPiY/OCkprLw8wapFjBaET7cMNKbOtVipQPYHElfg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			C8aofSExOxn2EKBH+rg+owyDMlg=
			</data>
			<key>hash2</key>
			<data>
			kNS2UvbYCcdm7tSxK/+/hS0M8iwsjKI2THYlE7m5EtA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			fsC9PZtjJRoRqtbec7KVYWkmuKc=
			</data>
			<key>hash2</key>
			<data>
			6u3B4M2JgDUgiBTy1C4xTfFiSfGMEjMDK7+iRcoaZvc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			C8aofSExOxn2EKBH+rg+owyDMlg=
			</data>
			<key>hash2</key>
			<data>
			kNS2UvbYCcdm7tSxK/+/hS0M8iwsjKI2THYlE7m5EtA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			130ouJhMpZj1Z376F5gsUfoyxyc=
			</data>
			<key>hash2</key>
			<data>
			l8ml0KsKmiLEQKTzAYpZRg2k4nhDb5o3vF3lArU4ZzY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/af.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Ze3vRmqRphRbVord7pGibO3fPjM=
			</data>
			<key>hash2</key>
			<data>
			aCtbnATpHK8Vi9pb9hBnXBBY6+zqsfeZF+F4H8aujwc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ar.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RU5U5p2+HVYWsFfSq227cr6oPa4=
			</data>
			<key>hash2</key>
			<data>
			s1aJgl1DJYHZsgUSZhMYINgeVIIi1NDjhZ3KkkA6SHY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/bn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			062Bxy406hTTH31PhS52P1+Clqs=
			</data>
			<key>hash2</key>
			<data>
			RYmfdmkMCNbGahvM9NYcmYiySFg8EvXZ2GUMXlSBWN8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/cs.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mD61p8uMAsJbHWM5u43pGNZFp74=
			</data>
			<key>hash2</key>
			<data>
			JIHHymPdIttC0qC3iZx89N8HLHiCT2sFHJ7U09zZQzg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/da.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jOzBKA4li06kg94bqglzoOLJD9M=
			</data>
			<key>hash2</key>
			<data>
			rQLYTaF5jkBlY9WlSinHBEtLGJwU8VJyBAXbMOS1FBw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/de.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nEbzzEMN0NGvB2cD2IpkSbWV6NM=
			</data>
			<key>hash2</key>
			<data>
			jaRdBaX5SEMhMJHA4X7DSIX/mjEKUdH09tKg+e1jkuU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/el.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/j4hlLRB9a6Bz377kSUlsx1c+Uk=
			</data>
			<key>hash2</key>
			<data>
			KH/h3KcObMmRfS3vpIMRtbDX/tuUrUrwt3agnfZKKf4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/en.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			X2K5j4DkrzLxC/mhfhHoIXx5aRk=
			</data>
			<key>hash2</key>
			<data>
			eNYbN/zs5JhZzRo97ltQFilMcvfV7MUlGzmUK8JCOK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/en_GB.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			T3svla3GmadkuwnmQcwbAvlYi98=
			</data>
			<key>hash2</key>
			<data>
			Ehn5jlEk9nHkql1/teEwOXPdlmM0yqLQMkVq95W1lU0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/es.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8Bwe0QXPL21MImzsItkC041zC+s=
			</data>
			<key>hash2</key>
			<data>
			X1CMNL7b4eteT/mu2U4kGGxt77m8h6i9IZlZ+3aXBMc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/es_ES.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			c5FkRPxpv9zvua45N7qY/2Lnjc4=
			</data>
			<key>hash2</key>
			<data>
			AWSgHyoVXbBxs9n2Ty2XM6gGClpTvrWQheR7USy3aHo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/fi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cpEfxM3HMb6H6HEuQEa8afs87bA=
			</data>
			<key>hash2</key>
			<data>
			n0r9aE/uabG+yQ+CTicKtVH2R8NXpmfBfW3pEneb2Gc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/fil.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QDnup8k5E1ovyik1vJG+36NnxRY=
			</data>
			<key>hash2</key>
			<data>
			SrYgrW5xGmEgfJvEg2BMqfBsRxYa1koP9r1J1lyMd7A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/fr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+kWYsXEYhJPWju3cpYvxA0fNPIU=
			</data>
			<key>hash2</key>
			<data>
			5puGnwqyUU34y4BDDWnmrqL9cbHAosqA27c/VZvrQlE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/gu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XWLBntIiv6IgAicsZYD1u2/ZCNE=
			</data>
			<key>hash2</key>
			<data>
			jDOx6ZOVtvUbyWYwon1C1cRzUnAY3r3JmiFTNgJd30M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/he.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9CSDzNjRF5Zi4T8lQzEb4BFHrV8=
			</data>
			<key>hash2</key>
			<data>
			Fg41Nq6D6Yg8CCJhCwqZd3eMLjs/NepPFMy2rB2A+vg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/hi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3UoJKTt98zMp4MoNL+3/r86aYhQ=
			</data>
			<key>hash2</key>
			<data>
			7MIeteCKjPKu7GszF+BrRJqpVxduoAXlg6drRpQPceQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/hr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lwcfpmcTECcYUnEE5hPsizpm3wg=
			</data>
			<key>hash2</key>
			<data>
			iD8FRdiUx28qJEFvtvyQRbOWzsTPikwXwrOEMF5pAWw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/hu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IzpGhcaXvGa6oKaqvoknvGo7TtU=
			</data>
			<key>hash2</key>
			<data>
			GtWEiES81lZil10Skylfrn88mjj2e3NcvieRmzh43Qw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/id.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EC8VfATHFmjNpky43WLkcretWaY=
			</data>
			<key>hash2</key>
			<data>
			Eq9a4nxlLu+lwpJLYXEf46sjwQW6bi3N3y7N8UycWNQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/it.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xPK7qaQJKfzP3MZ0b3VJyO+gNMw=
			</data>
			<key>hash2</key>
			<data>
			kDpMVTknyBjiPj2EtWrRNdRGBZtmXRVPM5ldzU+2X9g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ja.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CKj+8eG+ZnwNE9X55i68+oS18Ok=
			</data>
			<key>hash2</key>
			<data>
			9xk36xdQPl2SRgXbWGMBxwjjzkVXUxtiYtkidSYwVNQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/kn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EsrhFpmgw71P+TyRPtinsIr4hDQ=
			</data>
			<key>hash2</key>
			<data>
			8NH+f4tH0tzd5k5LyfZJ14DN64SkSURQ5CfyFJf/Gmc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ko.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NaTTNshzDqPGr0O5SATt5Ar/Ke4=
			</data>
			<key>hash2</key>
			<data>
			C2sb15n2T4Ad64dDZGVBPhfJ+aew0h2WCmSN6SX8IjQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ml.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mifHGo/qSi071/IqjLRUwRFs640=
			</data>
			<key>hash2</key>
			<data>
			gHIBTSCHftU0BQWZOGcD5td1wykq7tQA+mKClgUkYHM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/mr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			51s+e6B/gw6shnNo/gbRAQIunSc=
			</data>
			<key>hash2</key>
			<data>
			fn0JKkKO/dH0bVur9VfzWntl8ls0yoY8YjgvYr/XsLw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ms.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			evzrkbxiAdaJ+exnQUlBrhVkZjE=
			</data>
			<key>hash2</key>
			<data>
			nNTZclYH1fxv1+Mch6eJbzgnzbwYRtH9f9Vtu+Le3dk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/nb.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WMN/9oOghCcQVhMJD5HywSIIetM=
			</data>
			<key>hash2</key>
			<data>
			QSYAJw601nnnqakR434bP6yeMjFPWZsx8ypSv68A13A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/nl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cxRuVwKkGzmB3JnxcdYfSlMU+3Q=
			</data>
			<key>hash2</key>
			<data>
			yNnGqC//Orb49RDL1a0nOau+xDgkzinH4LUqhsqqkEQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/pa.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VuOm5+GxMy4d/JXxpVyHXLsdpBc=
			</data>
			<key>hash2</key>
			<data>
			bRVgGo+3BK4SwH5mPgjOjwn/mV3H6X+N2aAccxRdrdY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/pl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Hv1fBzZGisbifod43+Yl7abQPcA=
			</data>
			<key>hash2</key>
			<data>
			hEObnVN8x0e7V7SEKVMLd2+36sqDjInp/lw2GBlVDv8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/pt.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			70uwcrwkXYlkAfk5UaIrUQkWQI0=
			</data>
			<key>hash2</key>
			<data>
			fr0opMxcbe0rfDIeWfWEevhQ3S5ydVBvEE5yPFYhBw8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/pt_PT.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p9PvnBPstHznGo+paZZrb+pNMAc=
			</data>
			<key>hash2</key>
			<data>
			meQ3C7YBEw7vLepXd6Ck7W8869A1xgLgptLl84sgKhk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ru.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bJltHFKTun/WYe6nQvEAlKM2dRM=
			</data>
			<key>hash2</key>
			<data>
			tLbG0XkAh2lBT62Y0q08V7HUq1yB/QWlDKQ66pvXAtU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/sk.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dDydplGYNYff6uM8Gft5tZqTXhI=
			</data>
			<key>hash2</key>
			<data>
			pNDFOu2O93RtAOzuyC4q2d8DP6wZuffCiHfxEIolnpU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/sv.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			N7VTH3m8nIqk04E4+7aclr63OsA=
			</data>
			<key>hash2</key>
			<data>
			4HoEVPvwpF9IR063NtuNj8j9VEshT8JjvyTRGfaZbKw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/ta.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8ujSZal8Bl4w2uQzbRuH+iyid3o=
			</data>
			<key>hash2</key>
			<data>
			l7aWKjZkBRjMSzn2T6GE21BMAi7DPKsf48+MQ/itkbo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/te.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Dzl0I4eYOYud+sVAICju4r/2xwk=
			</data>
			<key>hash2</key>
			<data>
			71qAVzkjqD6kmUs/nmYSfTlJv1FVVG9R4MKcNw3We4k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/th.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i5VzACdOfJFncxh8cooghE3tIdc=
			</data>
			<key>hash2</key>
			<data>
			Gt+X0AF2SJdujyvtLTzRvcWYWA6VH8PZRE0EcpMy0Ws=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/tr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RbNisbiWvkqWwj2C0LDgvQiLYUA=
			</data>
			<key>hash2</key>
			<data>
			MbpGbjBd1dHuUbE6jB8W1a+0xEj5ODzVz6yaQtCidqk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/vi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7KWOQQAFL0LBHlGYTuxqCzW1agQ=
			</data>
			<key>hash2</key>
			<data>
			3KTxqBqodalyJ3jdjQwlZ+RPQbYNJJHRWjXgy1lJSyg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/zh.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			socx43t1VI649J+QyCOEeZ/YOJQ=
			</data>
			<key>hash2</key>
			<data>
			eXBsqwueqa9PpbpzHtzpzN3ztNOYTwY4vCTqsUFhQC0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/zh_Hant_HK.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VWqZ/8HYO+hnD4ncuigmcqdwTfo=
			</data>
			<key>hash2</key>
			<data>
			pnbR7cjq5w2nZG17PXaHRT+19mzFJ44tIEnJCoUqrvo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/FacebookSDKStrings.bundle/Resources/zh_Hant_TW.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rr2kvGmgmXEdbTLOH1CwY/7fGcc=
			</data>
			<key>hash2</key>
			<data>
			2XHa9tLW3cf0L3byDYr/+6qjFMSrS3i+xwESUxC9pZc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			e7MqrXgFKVo5L1GdLOdUWvBVRww=
			</data>
			<key>hash2</key>
			<data>
			B7Hmpw5YJQkyyBpI1tSrMykio4/TnudqUfVhYiWhHLU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			+4xzw+xvpntuK5AhSisGTSzsudg=
			</data>
			<key>hash2</key>
			<data>
			QgUFyksoQJOBn7vsX2WBw89uQ1s+OjAtb6XqI3vhQ74=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/A/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			lcGBgfUmKFsDuJqiAxchfu5Z+iI=
			</data>
			<key>hash2</key>
			<data>
			ZdQQ04XXOoCPMSgcHnuxUGQRVXiS8FMO/cTx3G+fZj4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			qG6Ty6t/qhX430RoQk8cuQ7fGXw=
			</data>
			<key>hash2</key>
			<data>
			rB6rIBwUCwLIwjj0MHS+vm+oRCTLb8bnr7hT9agltoY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/DWARF/FBSDKCoreKit</key>
		<dict>
			<key>hash</key>
			<data>
			jSjzXhDRRa+ZRGqc/+eA5hrDffI=
			</data>
			<key>hash2</key>
			<data>
			ryCeRvbwThMBu7qGW5YYCRdiV+YeQruePn2VEqDfBs4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKCoreKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			zOwvdTFbbHiLf3xZGdvSA5POLuY=
			</data>
			<key>hash2</key>
			<data>
			4Mk19ELznA38uvAdOAGyU03RLBSSob0NACPuVihlteQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBSDKCoreKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			N+aQGPhjXF6J1hSKVDHQD6E7CGc=
			</data>
			<key>hash2</key>
			<data>
			qOJy+l14Gy+DYMrvwXEmJ1IXv+tX8d20y+t/Fewae1E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FBSDKCoreKit</key>
		<dict>
			<key>hash</key>
			<data>
			sK+DJRxMV2ggHW4FmnJhpKKEdfY=
			</data>
			<key>hash2</key>
			<data>
			+OuH6OduF9nVGC0m0ooZocsr7fJjGKyaZ8xCVsAMvEc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/af.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Ze3vRmqRphRbVord7pGibO3fPjM=
			</data>
			<key>hash2</key>
			<data>
			aCtbnATpHK8Vi9pb9hBnXBBY6+zqsfeZF+F4H8aujwc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ar.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RU5U5p2+HVYWsFfSq227cr6oPa4=
			</data>
			<key>hash2</key>
			<data>
			s1aJgl1DJYHZsgUSZhMYINgeVIIi1NDjhZ3KkkA6SHY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/bn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			062Bxy406hTTH31PhS52P1+Clqs=
			</data>
			<key>hash2</key>
			<data>
			RYmfdmkMCNbGahvM9NYcmYiySFg8EvXZ2GUMXlSBWN8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/cs.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mD61p8uMAsJbHWM5u43pGNZFp74=
			</data>
			<key>hash2</key>
			<data>
			JIHHymPdIttC0qC3iZx89N8HLHiCT2sFHJ7U09zZQzg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/da.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jOzBKA4li06kg94bqglzoOLJD9M=
			</data>
			<key>hash2</key>
			<data>
			rQLYTaF5jkBlY9WlSinHBEtLGJwU8VJyBAXbMOS1FBw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/de.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nEbzzEMN0NGvB2cD2IpkSbWV6NM=
			</data>
			<key>hash2</key>
			<data>
			jaRdBaX5SEMhMJHA4X7DSIX/mjEKUdH09tKg+e1jkuU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/el.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/j4hlLRB9a6Bz377kSUlsx1c+Uk=
			</data>
			<key>hash2</key>
			<data>
			KH/h3KcObMmRfS3vpIMRtbDX/tuUrUrwt3agnfZKKf4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/en.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			X2K5j4DkrzLxC/mhfhHoIXx5aRk=
			</data>
			<key>hash2</key>
			<data>
			eNYbN/zs5JhZzRo97ltQFilMcvfV7MUlGzmUK8JCOK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/en_GB.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			T3svla3GmadkuwnmQcwbAvlYi98=
			</data>
			<key>hash2</key>
			<data>
			Ehn5jlEk9nHkql1/teEwOXPdlmM0yqLQMkVq95W1lU0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/es.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8Bwe0QXPL21MImzsItkC041zC+s=
			</data>
			<key>hash2</key>
			<data>
			X1CMNL7b4eteT/mu2U4kGGxt77m8h6i9IZlZ+3aXBMc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/es_ES.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			c5FkRPxpv9zvua45N7qY/2Lnjc4=
			</data>
			<key>hash2</key>
			<data>
			AWSgHyoVXbBxs9n2Ty2XM6gGClpTvrWQheR7USy3aHo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/fi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cpEfxM3HMb6H6HEuQEa8afs87bA=
			</data>
			<key>hash2</key>
			<data>
			n0r9aE/uabG+yQ+CTicKtVH2R8NXpmfBfW3pEneb2Gc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/fil.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QDnup8k5E1ovyik1vJG+36NnxRY=
			</data>
			<key>hash2</key>
			<data>
			SrYgrW5xGmEgfJvEg2BMqfBsRxYa1koP9r1J1lyMd7A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/fr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+kWYsXEYhJPWju3cpYvxA0fNPIU=
			</data>
			<key>hash2</key>
			<data>
			5puGnwqyUU34y4BDDWnmrqL9cbHAosqA27c/VZvrQlE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/gu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XWLBntIiv6IgAicsZYD1u2/ZCNE=
			</data>
			<key>hash2</key>
			<data>
			jDOx6ZOVtvUbyWYwon1C1cRzUnAY3r3JmiFTNgJd30M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/he.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9CSDzNjRF5Zi4T8lQzEb4BFHrV8=
			</data>
			<key>hash2</key>
			<data>
			Fg41Nq6D6Yg8CCJhCwqZd3eMLjs/NepPFMy2rB2A+vg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/hi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3UoJKTt98zMp4MoNL+3/r86aYhQ=
			</data>
			<key>hash2</key>
			<data>
			7MIeteCKjPKu7GszF+BrRJqpVxduoAXlg6drRpQPceQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/hr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lwcfpmcTECcYUnEE5hPsizpm3wg=
			</data>
			<key>hash2</key>
			<data>
			iD8FRdiUx28qJEFvtvyQRbOWzsTPikwXwrOEMF5pAWw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/hu.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IzpGhcaXvGa6oKaqvoknvGo7TtU=
			</data>
			<key>hash2</key>
			<data>
			GtWEiES81lZil10Skylfrn88mjj2e3NcvieRmzh43Qw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/id.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EC8VfATHFmjNpky43WLkcretWaY=
			</data>
			<key>hash2</key>
			<data>
			Eq9a4nxlLu+lwpJLYXEf46sjwQW6bi3N3y7N8UycWNQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/it.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xPK7qaQJKfzP3MZ0b3VJyO+gNMw=
			</data>
			<key>hash2</key>
			<data>
			kDpMVTknyBjiPj2EtWrRNdRGBZtmXRVPM5ldzU+2X9g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ja.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CKj+8eG+ZnwNE9X55i68+oS18Ok=
			</data>
			<key>hash2</key>
			<data>
			9xk36xdQPl2SRgXbWGMBxwjjzkVXUxtiYtkidSYwVNQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/kn.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EsrhFpmgw71P+TyRPtinsIr4hDQ=
			</data>
			<key>hash2</key>
			<data>
			8NH+f4tH0tzd5k5LyfZJ14DN64SkSURQ5CfyFJf/Gmc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ko.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NaTTNshzDqPGr0O5SATt5Ar/Ke4=
			</data>
			<key>hash2</key>
			<data>
			C2sb15n2T4Ad64dDZGVBPhfJ+aew0h2WCmSN6SX8IjQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ml.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mifHGo/qSi071/IqjLRUwRFs640=
			</data>
			<key>hash2</key>
			<data>
			gHIBTSCHftU0BQWZOGcD5td1wykq7tQA+mKClgUkYHM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/mr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			51s+e6B/gw6shnNo/gbRAQIunSc=
			</data>
			<key>hash2</key>
			<data>
			fn0JKkKO/dH0bVur9VfzWntl8ls0yoY8YjgvYr/XsLw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ms.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			evzrkbxiAdaJ+exnQUlBrhVkZjE=
			</data>
			<key>hash2</key>
			<data>
			nNTZclYH1fxv1+Mch6eJbzgnzbwYRtH9f9Vtu+Le3dk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/nb.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WMN/9oOghCcQVhMJD5HywSIIetM=
			</data>
			<key>hash2</key>
			<data>
			QSYAJw601nnnqakR434bP6yeMjFPWZsx8ypSv68A13A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/nl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cxRuVwKkGzmB3JnxcdYfSlMU+3Q=
			</data>
			<key>hash2</key>
			<data>
			yNnGqC//Orb49RDL1a0nOau+xDgkzinH4LUqhsqqkEQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pa.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VuOm5+GxMy4d/JXxpVyHXLsdpBc=
			</data>
			<key>hash2</key>
			<data>
			bRVgGo+3BK4SwH5mPgjOjwn/mV3H6X+N2aAccxRdrdY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pl.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Hv1fBzZGisbifod43+Yl7abQPcA=
			</data>
			<key>hash2</key>
			<data>
			hEObnVN8x0e7V7SEKVMLd2+36sqDjInp/lw2GBlVDv8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pt.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			70uwcrwkXYlkAfk5UaIrUQkWQI0=
			</data>
			<key>hash2</key>
			<data>
			fr0opMxcbe0rfDIeWfWEevhQ3S5ydVBvEE5yPFYhBw8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/pt_PT.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p9PvnBPstHznGo+paZZrb+pNMAc=
			</data>
			<key>hash2</key>
			<data>
			meQ3C7YBEw7vLepXd6Ck7W8869A1xgLgptLl84sgKhk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ru.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bJltHFKTun/WYe6nQvEAlKM2dRM=
			</data>
			<key>hash2</key>
			<data>
			tLbG0XkAh2lBT62Y0q08V7HUq1yB/QWlDKQ66pvXAtU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/sk.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dDydplGYNYff6uM8Gft5tZqTXhI=
			</data>
			<key>hash2</key>
			<data>
			pNDFOu2O93RtAOzuyC4q2d8DP6wZuffCiHfxEIolnpU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/sv.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			N7VTH3m8nIqk04E4+7aclr63OsA=
			</data>
			<key>hash2</key>
			<data>
			4HoEVPvwpF9IR063NtuNj8j9VEshT8JjvyTRGfaZbKw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/ta.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8ujSZal8Bl4w2uQzbRuH+iyid3o=
			</data>
			<key>hash2</key>
			<data>
			l7aWKjZkBRjMSzn2T6GE21BMAi7DPKsf48+MQ/itkbo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/te.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Dzl0I4eYOYud+sVAICju4r/2xwk=
			</data>
			<key>hash2</key>
			<data>
			71qAVzkjqD6kmUs/nmYSfTlJv1FVVG9R4MKcNw3We4k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/th.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i5VzACdOfJFncxh8cooghE3tIdc=
			</data>
			<key>hash2</key>
			<data>
			Gt+X0AF2SJdujyvtLTzRvcWYWA6VH8PZRE0EcpMy0Ws=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/tr.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RbNisbiWvkqWwj2C0LDgvQiLYUA=
			</data>
			<key>hash2</key>
			<data>
			MbpGbjBd1dHuUbE6jB8W1a+0xEj5ODzVz6yaQtCidqk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/vi.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7KWOQQAFL0LBHlGYTuxqCzW1agQ=
			</data>
			<key>hash2</key>
			<data>
			3KTxqBqodalyJ3jdjQwlZ+RPQbYNJJHRWjXgy1lJSyg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/zh.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			socx43t1VI649J+QyCOEeZ/YOJQ=
			</data>
			<key>hash2</key>
			<data>
			eXBsqwueqa9PpbpzHtzpzN3ztNOYTwY4vCTqsUFhQC0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/zh_Hant_HK.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VWqZ/8HYO+hnD4ncuigmcqdwTfo=
			</data>
			<key>hash2</key>
			<data>
			pnbR7cjq5w2nZG17PXaHRT+19mzFJ44tIEnJCoUqrvo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/FacebookSDKStrings.bundle/Resources/zh_Hant_TW.lproj/FacebookSDK.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rr2kvGmgmXEdbTLOH1CwY/7fGcc=
			</data>
			<key>hash2</key>
			<data>
			2XHa9tLW3cf0L3byDYr/+6qjFMSrS3i+xwESUxC9pZc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAEMManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			Yj5aPKxlx+gRF70J6qlF3vXE4mk=
			</data>
			<key>hash2</key>
			<data>
			RRWu3ofQe/GuFslThfnBEdL+ks4YVUnKAckD+t6Kw78=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKATEPublisherCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			HCKpYddhfbeOKPVi+PyxpQEl/Sw=
			</data>
			<key>hash2</key>
			<data>
			4DAoEwvpFC6T6PC5EJZwpa4Ey+A8LZo6rz/k/078BUM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKATEPublisherFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			o4BqZ5pce+E7bPPdIkkE30cwTuI=
			</data>
			<key>hash2</key>
			<data>
			cW1M/vPKiBNWmJ+XzS8iFtLl9s1+5ahOBCQWQ33lFfA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAccessToken.h</key>
		<dict>
			<key>hash</key>
			<data>
			52NScPmZOyhPECDbdwVz3SgFfnU=
			</data>
			<key>hash2</key>
			<data>
			oFHJEVxEJrVA9Y7bNOjGQZ28+CrbY+rbVTIA+GC3LWs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAccessTokenProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			8ssoFJOvhs7F3QcUOp076HcFlTA=
			</data>
			<key>hash2</key>
			<data>
			ttnRwTC/lx3T/wwxDPI/n0tOC8tMavvDSKnOHYtwmJ0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAdvertiserIDProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			spPSWUz3WKLY8u+9ZEUgThNQTMY=
			</data>
			<key>hash2</key>
			<data>
			3TS/lxGjmXQaqCF4fgbFoM/w3nXircZe7UfQRibtXh0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAdvertisingTrackingStatus.h</key>
		<dict>
			<key>hash</key>
			<data>
			s2/tT+xSXPH4xXaQ+yW41JtgT58=
			</data>
			<key>hash2</key>
			<data>
			pj5HBFKU2AJRVkryxLDxsNyV+Hq0vhsL7ESLeXA7gco=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppAvailabilityChecker.h</key>
		<dict>
			<key>hash</key>
			<data>
			Wyf9l4OPVlNw4rmgihSwsLLXekY=
			</data>
			<key>hash2</key>
			<data>
			WCKAfRQSLZ76amGNcy7D85Zr0FqbK3yqgD2x9Q2KMVc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventDropDetermining.h</key>
		<dict>
			<key>hash</key>
			<data>
			kuHhyp+2GLTjun17XfcnCE5AhEU=
			</data>
			<key>hash2</key>
			<data>
			L9oIxJipjM/MQk2hCZ2frITY+t5sCEXfXwbMQZJm5P4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventName.h</key>
		<dict>
			<key>hash</key>
			<data>
			BOUOaSiuKli6Qe/v5ySB3h+M5X4=
			</data>
			<key>hash2</key>
			<data>
			gkInDcjiBNASc1T6fLvTisSC622V3ONdX5cFYRNhq4Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventParameterName.h</key>
		<dict>
			<key>hash</key>
			<data>
			PUlvfFayxBDoZ9T09/HpoVZ7Hgc=
			</data>
			<key>hash2</key>
			<data>
			dourp/NLYssFkQ5Dg6AxyxLObwl5JhALDaFz6lFf9Lc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventParameterProduct.h</key>
		<dict>
			<key>hash</key>
			<data>
			r+KeRcYNIPkXBrMbyUHT2wI7s9Y=
			</data>
			<key>hash2</key>
			<data>
			FIPXmw+JMv7bBSbF0zhfVC2Ib03Sx9JYrwjlNp1XInI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventParameterValue.h</key>
		<dict>
			<key>hash</key>
			<data>
			040yhBlHKamIDlwNu0ImpgLTCqc=
			</data>
			<key>hash2</key>
			<data>
			Q2olYJJI/DN976h566Nwy3D+obhtAQAKHOJ5lKeTfm8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventParametersExtracting.h</key>
		<dict>
			<key>hash</key>
			<data>
			9pOtxV2/CHu04fcr39VsN+sfZ+s=
			</data>
			<key>hash2</key>
			<data>
			BdY6x122D6MPzxunayL68o6siMnk1pbG9+SIttD3Er0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventUserDataType.h</key>
		<dict>
			<key>hash</key>
			<data>
			A3lEI7gxtNx4AHoXWeE0s7u1zK8=
			</data>
			<key>hash2</key>
			<data>
			7pjsRgcXBeV8tJeLjrQvQ/3ZBmzY9k086Z46TsArMag=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEvents.h</key>
		<dict>
			<key>hash</key>
			<data>
			lg573O7sH41xBg8lJrqEit3K/XQ=
			</data>
			<key>hash2</key>
			<data>
			UXJezVQ0cuGeeCKQaxUnK81y4yXJT55f4K9dAPNv5/A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			HzthBYIOOFB3e2msa04M0rtBsRo=
			</data>
			<key>hash2</key>
			<data>
			CPd+ImdtELN8bwE7ctjAasJyW7VT89Q5RQaUxTFXz6M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfigurationManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			ANbr0kXkjHZjFTN/QfFH+kdJmYI=
			</data>
			<key>hash2</key>
			<data>
			9Hd2vCTkBhdhGl2O0GrDnmf5Dk+9LGUCubPXDNOSj9M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfigurationProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			v6/aYc8HvZlvKvREulESWKamY/g=
			</data>
			<key>hash2</key>
			<data>
			HNsLJH7DmlMk6E5VETGgJDTdrBxFh1tmIqN2HupuauQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfigurationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			Q6UHSFjjvfBNsl6lIgbm+E/xdvg=
			</data>
			<key>hash2</key>
			<data>
			Sxr+A8kVMuZThRbBh4I4DH8boozZFBrhAwev32Vh5Hw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsConfiguring.h</key>
		<dict>
			<key>hash</key>
			<data>
			mHGiLeCpVuFdv3XqdFxv9vt7374=
			</data>
			<key>hash2</key>
			<data>
			CYre/9YuQpKmC/OI/41C1Pu8lysIfIKLoct7rQEcEdc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsDeviceInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			SxQnYEiX9/yUxdAbzLI6d4eaegc=
			</data>
			<key>hash2</key>
			<data>
			c8TYlTd79b+8iKRVt8ssLF77TZo5fhxvf5ChNBNfMhc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsFlushBehavior.h</key>
		<dict>
			<key>hash</key>
			<data>
			tGoQfvz6uAy5DjnhPnzX/Ai2vl4=
			</data>
			<key>hash2</key>
			<data>
			iUxqEKL4pmF7f47Qul2Oe8QI0MjDPnOn3VhWjVQWe90=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsFlushReason.h</key>
		<dict>
			<key>hash</key>
			<data>
			CVXh0F8QRsx+pTFSV0t8r1QBBAQ=
			</data>
			<key>hash2</key>
			<data>
			z4aL5e8KYk3I9292BbvFVfL7VvGSXi8r1ULbNY9qC+o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsNotificationName.h</key>
		<dict>
			<key>hash</key>
			<data>
			y7c3PKWx/w77oSbeugClHIvTMS8=
			</data>
			<key>hash2</key>
			<data>
			7JmzpHhHPCXS4WcGYrhN2g1u5YXUgR/ltWdRyfv8l0I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsParameterProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			lSEA6VeT3RQ/9OIKlCWKuogT/fc=
			</data>
			<key>hash2</key>
			<data>
			pCHyiWkvDhXpHUHpcTQYsXWk6DNMxor1yUXbLb1zJoM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsReporter.h</key>
		<dict>
			<key>hash</key>
			<data>
			j2aR5673Kbc66xveJIq2h8rsKLk=
			</data>
			<key>hash2</key>
			<data>
			wg1FOXDGXyIdKy7bYj+hrqcKxtTG/paGmTO6QAjIyZ0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsState.h</key>
		<dict>
			<key>hash</key>
			<data>
			WuHkw89fZW/JsqsG7+MqdROIuqU=
			</data>
			<key>hash2</key>
			<data>
			R6QD42bulvhwFoDu6RdyZBgBVdXMRl4ysZHuVUj1g9I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsStateManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			1npaFG2cN1/3fD1rD7gXkZmnUbI=
			</data>
			<key>hash2</key>
			<data>
			QFynSzFRj8ZNs48YxDNrvOzNjk/5/TKdEvOpAV6gfAo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsStatePersisting.h</key>
		<dict>
			<key>hash</key>
			<data>
			oylwsOMHWTY2ag3rphOgwSAfz7c=
			</data>
			<key>hash2</key>
			<data>
			89C3WF44BG25E9QGywyeFSIhzSN3vIZ8j0Cf1zBc7Nw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsStateProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			KzGafbBYik5zyGw1bm76HKS7Tc0=
			</data>
			<key>hash2</key>
			<data>
			/cQous3za5Y8sBNc9gJnSblGnlcQJNisDkxD0q+XHUg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			5vrPi5Z7t//eIoz9u9sQrS88V8E=
			</data>
			<key>hash2</key>
			<data>
			pGuB3yod13iFDnnyRtuUuYaX7VHyqUUmVwj8DUHOU1Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppEventsUtilityProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			w++q3FMjWosIk0QoN6chRjfvng8=
			</data>
			<key>hash2</key>
			<data>
			5XYISqTXIOZPBrYNgUjayHCm4HMFKWuvcjLD+DYj3GA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			nqhYr/0Yijl6YG6zjg69DkIZMdo=
			</data>
			<key>hash2</key>
			<data>
			TIES03/iWapcNCC9q93neFaUJvaUs7+RvhH7EO++7NE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkEventPosting.h</key>
		<dict>
			<key>hash</key>
			<data>
			Unlyk7QfK8RMiwI6wrySVV1IWP4=
			</data>
			<key>hash2</key>
			<data>
			ynWClusbalHB6D8+QrD6JmUW1Gr2JdcsDTxToJtiJ/I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkNavigationBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			1PU/0JKCKzJJV3POVzX50kAFA0o=
			</data>
			<key>hash2</key>
			<data>
			JmQsfAZrZwAJH5qlBBRvvfzK10IKeqMWfuyED+KYNmU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkNavigationType.h</key>
		<dict>
			<key>hash</key>
			<data>
			3fSnRUTotphysdDrdAvLMxLPW+k=
			</data>
			<key>hash2</key>
			<data>
			hwFJL88C4bRrHRe3Y4ljnl5aCOTIvPg7Oqmz7nhwygo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			FaTKQ3PKUnwjWoYivjhBcoWCvf8=
			</data>
			<key>hash2</key>
			<data>
			4DXqK//wHh7BOXM1el/fpSnI2itx5Xvq5U9scd3DtTE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkResolverRequestBuilding.h</key>
		<dict>
			<key>hash</key>
			<data>
			SXBVCFpg1usB96KY2Km7m8BU4PQ=
			</data>
			<key>hash2</key>
			<data>
			e+6RKqsAlhAYxmTXpFwikKmionJN86SP5HKrZXbAkNs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkResolving.h</key>
		<dict>
			<key>hash</key>
			<data>
			vW0iP2TWioh9F2xVFhjb96AWH/M=
			</data>
			<key>hash2</key>
			<data>
			qb3BIqDSak/QEJVHaa0UyJE2SIhRWJTvILOUETHKNbU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkTargetCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			CSjhIDvr2Fy0xL6BAuVxNcPFzdg=
			</data>
			<key>hash2</key>
			<data>
			YPM7wFIveXtTUS5N5/9q/A437m7KU0YQ6H6B206u6R4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkTargetProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			KjpJOKaE7Hu4fZ/kcuexDfd7h/E=
			</data>
			<key>hash2</key>
			<data>
			xRIohRLTU/DyWAlvJeGXzuTsyj3d0Pb9Erqu0nLl2Qo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkURL.h</key>
		<dict>
			<key>hash</key>
			<data>
			QygAk/iNI8C9zeo9e0YUgaIWY7g=
			</data>
			<key>hash2</key>
			<data>
			GAKSBD0rwQ0puEpcq+BbmYBTAYi4nS6GrhNxJ1vQoKQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkURLCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			oN/+vlRMv1IvKkWrsh+VtDwu92U=
			</data>
			<key>hash2</key>
			<data>
			sKw0E6SLYUeeNW0wOHnRp9kQIhstniWV4X8zLvnmzVU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkURLKeys.h</key>
		<dict>
			<key>hash</key>
			<data>
			9Ke3NaIqqmJ0mx/UMz1Vw+/X9hA=
			</data>
			<key>hash2</key>
			<data>
			Up9j6FGSd/tWYjJkFp+P1BcOj9PPjDtXZlpdfizgL4s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			CSxGCvSd3TFNsqT26linPXGDWM4=
			</data>
			<key>hash2</key>
			<data>
			kp2CnE92bsV1FLjTg72fYgYnBgr5Jsp2QEI1Efop6JQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinkVersion.h</key>
		<dict>
			<key>hash</key>
			<data>
			4lyPD97ZcXbRvhIigNtr3LGnzKo=
			</data>
			<key>hash2</key>
			<data>
			ApoRkmalk5WwUDAjytM+t8cyZVllZTYJC92Fbu0c22s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppLinksBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			TPb6CeLZPTcgS7hT6GviE2JRz4k=
			</data>
			<key>hash2</key>
			<data>
			ummPfWNsfoi+QpV6yx2bMpP5IvBHvQkfcON2myGbUy4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppOperationalDataType.h</key>
		<dict>
			<key>hash</key>
			<data>
			Yxnb0SsotnkBXyJBdPIv4DE+uuE=
			</data>
			<key>hash2</key>
			<data>
			Kyg3KDbG6mD8rK/gtHc1u8If+GNKh/8Uxmbfib1bpXg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppStoreReceiptProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			YAWUu9gn0hfRwdt2dscR4Pjncb0=
			</data>
			<key>hash2</key>
			<data>
			x7PPCf3qyw2K0DAl7af8aPMLzR2TVekwBP1nj0J4Ro4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAppURLSchemeProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			G7H5ArEaw56tAdukKkeFnHJW3yM=
			</data>
			<key>hash2</key>
			<data>
			o9vW113QSBrXeTu8w1RgrMfMpi3Li+ZHpavPt/xYGa4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKApplicationActivating.h</key>
		<dict>
			<key>hash</key>
			<data>
			oHcGo1IxNL4whFjkCAqY0Bezu1A=
			</data>
			<key>hash2</key>
			<data>
			N3sSrIIH8n9PUXaO6Ko042/wGj7FjgmjMz09LpHRsKk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKApplicationLifecycleNotifications.h</key>
		<dict>
			<key>hash</key>
			<data>
			HZOemKjr7HIyL3ND764s1sIhRdA=
			</data>
			<key>hash2</key>
			<data>
			gVCQIdprFkqHFWN03C0LuQuv1i9nrhtP3G3t03o3SUM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKApplicationLifecycleObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			tM9oT+qXMwjCWeGffrZ/7pU9dhk=
			</data>
			<key>hash2</key>
			<data>
			wPjg8wCzbmbbm8y0dJZMh/wce2hP0UMVUsa2oZnCv6E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKApplicationObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			xJEwA88eDINbA4Kq2mRmWsoZVaE=
			</data>
			<key>hash2</key>
			<data>
			jUSJX2r8mLJr8u+wAn2jLp571gJ3SriNQhCpnkSNEfw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKApplicationStateSetting.h</key>
		<dict>
			<key>hash</key>
			<data>
			fy5bOvCaeEMdMssTZs5062vJuLs=
			</data>
			<key>hash2</key>
			<data>
			th4kWyfL9axOR4QeSZnS2+ff21hvfrJBKkVqOS9qpY4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAuthenticationStatusUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			NzmrmWDBawgvUzFKUTG4Zq+D8PQ=
			</data>
			<key>hash2</key>
			<data>
			eSjfSzpHEMWVAvxBo7bJWUgrhMNFvTx8WmBhyasQcEY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAuthenticationToken.h</key>
		<dict>
			<key>hash</key>
			<data>
			Hbt4TrpEQ1E7m0cPJVPJrvfOMYE=
			</data>
			<key>hash2</key>
			<data>
			hc6XPooa0mMb1VzLG43orpr9kKXVeMa+FrWuKr51KE0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAuthenticationTokenProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			zDTRRbEFRongbmR9iUSO35PCbhs=
			</data>
			<key>hash2</key>
			<data>
			mfib/LOFoh5fxGgumdwep5nNFARnqYaIQPovjFKmlwo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKAutoSetup.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZrZsPjcuQHmtIb0VA/2JxYjo0Bk=
			</data>
			<key>hash2</key>
			<data>
			ex/CIuIAAIQMIaocCGblxTLzDaMcZuJvi3MU6lxdgM0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			tLjX3wSZTxU3/edMWI3RtKPsv+A=
			</data>
			<key>hash2</key>
			<data>
			o6EBabARMQ/jGNCu0KhpTrBsI6PnWr6akob4aCraUv4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIProtocolType.h</key>
		<dict>
			<key>hash</key>
			<data>
			DblZg5KWlfoY7uhozPWbR8A+C+Q=
			</data>
			<key>hash2</key>
			<data>
			A5XYnc2oBVddlLz3wGiibtMvO6i/A+82gXL5XY+AsgA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			9p88MKTDxjMSnhRmD3tBUBpyKP0=
			</data>
			<key>hash2</key>
			<data>
			/2BV782KXKlCMETLnCxG2DLQWT+tVDZ/Ijo516KxeVY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequestCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			fSrgRjXiEf48iO5SW4tUzCK4Dfw=
			</data>
			<key>hash2</key>
			<data>
			NX+gToQ0lPJ7TSH2qMy4mIDEYMYzkZhwyZbP0XvC1iI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequestOpening.h</key>
		<dict>
			<key>hash</key>
			<data>
			0yprixA39qr6F++QUQmDaS+kI5c=
			</data>
			<key>hash2</key>
			<data>
			RkLNO6zfPQbD2c3NWSSdmM3tTFBY3GPfYgBqxAleaf8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIRequestProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			QvIJ69YhObUzXyvQTDogepJbLIY=
			</data>
			<key>hash2</key>
			<data>
			zXyn4IrEQSs/K0vIuWaHZHWeYSHJfjrS2xjq3hrHThs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKBridgeAPIResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			0xHREU5UN9V2806Plbv9YnZCtvo=
			</data>
			<key>hash2</key>
			<data>
			VpEoCY99wbTWk0mTFsof9zSkSb9JK0bc0ty0LHM0GGE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKButton.h</key>
		<dict>
			<key>hash</key>
			<data>
			nRauGR6uRcDNSlw5mNra+SReMhI=
			</data>
			<key>hash2</key>
			<data>
			3PYcJgi9ksT3Oh7qkXSmtIZ4zC05ftjnedLkaQyiEEc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKButtonImpressionLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			LsVpQhR6smaHCPnF/ITEWiKGkKA=
			</data>
			<key>hash2</key>
			<data>
			pShKA3myYUve8S5W/TI08BTWmhPh0RgoZQ6lY9c5S9g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKClientTokenProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			W+JNLAn4YexFixxEQGR3Q9RH0KI=
			</data>
			<key>hash2</key>
			<data>
			GvqZ0xNKA6tPwu3fX7MjAlOAhByiiNjI7PR4KRn+IbU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCodelessIndexer.h</key>
		<dict>
			<key>hash</key>
			<data>
			ua4gDNRjw0KAFXM4vqI1AxIhmgQ=
			</data>
			<key>hash2</key>
			<data>
			FcsOP37OoWFHJHceDyLXTSnifB7NTvxbg8l+Ea2N7KE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCodelessIndexing.h</key>
		<dict>
			<key>hash</key>
			<data>
			vvo1NJ1mTuIU5ZOEX1L9qWcyo+s=
			</data>
			<key>hash2</key>
			<data>
			pLXLVKuG6Qa9BS6uHD2G6ofjKsDr28Rz0u2tNx1QKBM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKConstants.h</key>
		<dict>
			<key>hash</key>
			<data>
			D8KfnDhhBY1wExKSXz3swq16lTE=
			</data>
			<key>hash2</key>
			<data>
			FhwOaPhmdYaDveEEpkZpS+FYAManp/GC+vHLW+hAU6A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKContainerViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			U4pg9nowPmFOUC9fvIpLijlN6fg=
			</data>
			<key>hash2</key>
			<data>
			29xbe5Cc1TOVvV8YSvMCywrkYejv/ToTkWJXi96mSh0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKConversionValueUpdating.h</key>
		<dict>
			<key>hash</key>
			<data>
			fTxcQJg2+ZGuh6rqfJoDG5p6L94=
			</data>
			<key>hash2</key>
			<data>
			6KHP0pS9iTtIWkg6hqMPweWwdf3DKMJVhKQxPa5rhCY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCoreKit-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			6Q0IXgo7gt89R9ybn7xJCG359Lo=
			</data>
			<key>hash2</key>
			<data>
			g7XEQkenFT0vcIVYTUXN6AZV0llkCsNWEqTD4XZWjys=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCoreKit.h</key>
		<dict>
			<key>hash</key>
			<data>
			9NSBO9571MvtFn98up/7ePg4lwo=
			</data>
			<key>hash2</key>
			<data>
			o2TE4Ds12wtlfLBvKjQ8VsMS5tnFEdVj2vJfsqI/21I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCoreKitVersions.h</key>
		<dict>
			<key>hash</key>
			<data>
			/eqaMAzP93B16wXvakv2GDTKX0k=
			</data>
			<key>hash2</key>
			<data>
			Vfex5H/RRx7zB2DpAjP6NiIB1F+xRX28EDuU7fmVphY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCrashObserver.h</key>
		<dict>
			<key>hash</key>
			<data>
			i5LsrVb8OhFQsCQv02NggyWl4qk=
			</data>
			<key>hash2</key>
			<data>
			VLAJlXn/MRpH+6S2wXiJ2MMHTkHfS7Iiq8J4ETcBHo0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKCrashShield.h</key>
		<dict>
			<key>hash</key>
			<data>
			488qfAna0UU+EgR6ICrzMNPuE+Y=
			</data>
			<key>hash2</key>
			<data>
			wrUKA5GCwCODrnCFSkEOYMuhWsXWs+v3tqHYUpWF3JE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDataProcessingOptionKey.h</key>
		<dict>
			<key>hash</key>
			<data>
			2KTIgp4tO9oDInYHDKE4PkSZtuo=
			</data>
			<key>hash2</key>
			<data>
			N45K4T1fNjF4+O/UVhpkoNMCpF0tstXjwLVZpz1m/E0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDeviceInformationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			AhMeLL4pfNP6NOlehkTmCGd908I=
			</data>
			<key>hash2</key>
			<data>
			o//wG1+zWPP4qO37XPTeCIBSdGTXg1o88LARSZXZBpc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDialogConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			BkHb91/H2f5w4nnsjzWvACP+LO8=
			</data>
			<key>hash2</key>
			<data>
			sxJ8YoB2cRIbhfFUnxXIe0KRF4COPsi4Zlpo/qsKI/Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDialogConfigurationMapBuilding.h</key>
		<dict>
			<key>hash</key>
			<data>
			3JigPqy5HGPxZ1EfvLl6K2r09G4=
			</data>
			<key>hash2</key>
			<data>
			DoHcwC+ItwFyYXOM+3jsJKQDRSVYaPDdnK5QjYY6Bdg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDomainConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			z42cVELCq+08IS97csqrsv0G3UM=
			</data>
			<key>hash2</key>
			<data>
			ODDfLUp/EjYZAOC3/MOAnDiI7lc8nLJSqY8sennYpO4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDomainConfigurationManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			vhxTjwLkFlXSayMOST5Du3FF1Ns=
			</data>
			<key>hash2</key>
			<data>
			aJ/dMNcl3sV3TF84MdS1OF9uE+sX9Qh6m5P+Wu5QWhQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDomainConfigurationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			bAZKBepNb9Jwher3IYkkHIK+jM4=
			</data>
			<key>hash2</key>
			<data>
			A0RxB1Xye7jGvnnjZ0gR4CstN5CfaM7agMWrUsOCmHg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDomainHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			4pB423j54rXYv0k1XCxquIvgGII=
			</data>
			<key>hash2</key>
			<data>
			/swwaLXUz3BoEglRz+kpA5A5WTy4IyeJyk8eC4k3/4g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKDynamicFrameworkLoaderProxy.h</key>
		<dict>
			<key>hash</key>
			<data>
			gQze+1wXFmg8HHDE0Ba4/AdlSbk=
			</data>
			<key>hash2</key>
			<data>
			DKzc5A2VHGjb5zjiW11FfYE+be1Je5rxetWvpGpLWP8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			t9pebtrwIlsvol930VzK/Afs/8E=
			</data>
			<key>hash2</key>
			<data>
			DhLqWnWnnr8IYmWabnEPwwtlgNNnXJV/K4weIvUDLz4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorConfigurationProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			rWzO75a4AGVSBUj9usd195pV1pw=
			</data>
			<key>hash2</key>
			<data>
			+cf3Zndluf4/nH6CRo8QwhZjyyogyQr9rRGqFSJJ/Qw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorConfigurationProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			/NYL7EwesbS1Jj4zI5j/M36RrP4=
			</data>
			<key>hash2</key>
			<data>
			hujKKoeVm3HeeiIxu8JoiRbP5INzeD2/vMsdcL57FLM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorConfigurationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			8oEeLY1N7YRaIXucUhviYIeCeUA=
			</data>
			<key>hash2</key>
			<data>
			disBMqvoKxnyr7jUVHZSCd0dYEdLy+w18e3cMgW5INU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			hSTHauBFdEYzYLgpazD8Nu2mbvA=
			</data>
			<key>hash2</key>
			<data>
			J1rXYSPoy877pIwlZKDP6n/b3DufexQeIl0pOymmYVY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorRecoveryAttempting.h</key>
		<dict>
			<key>hash</key>
			<data>
			woJpc0K7/L9yeXE4C+9IxIUoyNo=
			</data>
			<key>hash2</key>
			<data>
			W0d5bC9aorrzc9ohCQNpLCX0lFAN0NEWcKF8J0wW/rs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorRecoveryConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			ycgx9i+ACoXgcR7EKbYFYCxzlOg=
			</data>
			<key>hash2</key>
			<data>
			sxH7BnnlsngUMLwevQeegntSFKDRSmTEKtWhPQj/1Gs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorReporter.h</key>
		<dict>
			<key>hash</key>
			<data>
			mr/DzUsbnLVLYMNNABg7aaa45vw=
			</data>
			<key>hash2</key>
			<data>
			rcdsRdF/ar59iiIPHyagPto5zGT/Sxfh95CnVdi22+w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKErrorReporting.h</key>
		<dict>
			<key>hash</key>
			<data>
			QPo2mKvh+gBY7bUYxLIRori3P84=
			</data>
			<key>hash2</key>
			<data>
			akNPfvBbZsCgwyqes5+H7hRxxdGAzyQYxlJqnkGK4hA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKEventLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			DUSe3ilZQO8BqMsmrVYsaoF7LGA=
			</data>
			<key>hash2</key>
			<data>
			0VHOLZCsy4MIa2IyIiw2m0r7HlIaK1PEG6/P55bLZ8o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKEventProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			gp3AdngU5TgiopBE4rJMoR6ANSg=
			</data>
			<key>hash2</key>
			<data>
			XdMXEDfV+PUzUnWtYK5vvmDq15EbhKM7Msfu34hUKZ8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKEventsProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			O75xhVmbjtVsiUJUjeGE1r788nM=
			</data>
			<key>hash2</key>
			<data>
			yIDs9c5MQ+yOaWXnwlnpInkbHV8GMjI7ZTBwOd+/AT0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKFeature.h</key>
		<dict>
			<key>hash</key>
			<data>
			Oh4B8Z0ZOzOtzlb70Z4TPxT1NQg=
			</data>
			<key>hash2</key>
			<data>
			V1hpI7TAZqwqEOEABosdtY1n2cBJTV/NzjeKNw9UDUc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKFeatureChecking.h</key>
		<dict>
			<key>hash</key>
			<data>
			rYTkx84W03mL0rrno4sthw6poiM=
			</data>
			<key>hash2</key>
			<data>
			QtYErERzFYGRmUpt4HXd8p062xQtrNl5l+J2nUhhc1k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKFeatureDisabling.h</key>
		<dict>
			<key>hash</key>
			<data>
			5RTMQMD5BH2IHczkFRDwcDa6YEc=
			</data>
			<key>hash2</key>
			<data>
			TKR1xKxgrd0N8JUx6kxkmWjLK07R7jMGOSWpnHlMoqA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKFeatureExtracting.h</key>
		<dict>
			<key>hash</key>
			<data>
			rLjAEFGlg1SFdt3LKE/Vyvpy4C4=
			</data>
			<key>hash2</key>
			<data>
			9+2zhi+7pfcv33U40/OTdeLKZgpJVvbygggabihtg/Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKFeatureExtractor.h</key>
		<dict>
			<key>hash</key>
			<data>
			UpeejK9e6KddzNj4pJ/187IOAok=
			</data>
			<key>hash2</key>
			<data>
			kEO+YvkRcV483BfguxWKQ5uS0+Qmx3F3mL3dsHGkRks=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGateKeeperManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			UApODVoVBgYDmemuf8pq5KhSZ2A=
			</data>
			<key>hash2</key>
			<data>
			7mWH6MgxmutTpF+sXRQKlG6n5MLh4o5QHROkkRPhs1E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGateKeeperManaging.h</key>
		<dict>
			<key>hash</key>
			<data>
			MZUyM6YX2TAfccBQg6xiTBypZ18=
			</data>
			<key>hash2</key>
			<data>
			ZRWGI6azeE93UU6MLd4PVLDsl0+khZ9ajsIyRJrwYNU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphErrorRecoveryProcessor.h</key>
		<dict>
			<key>hash</key>
			<data>
			aNj/uzA6QMePztxIxRll77xHLIg=
			</data>
			<key>hash2</key>
			<data>
			wyEA5FnCIsiLQMekTMTbQfJgpsp5uPtXVy0RVKPm/Co=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			W0+x27i8eT+6EMZ3lVcQXPKsAP0=
			</data>
			<key>hash2</key>
			<data>
			6GPfja4r0l+3PHnf0yfFzGgyV0iPVl+wm7ooUBBX1M0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnecting.h</key>
		<dict>
			<key>hash</key>
			<data>
			sAlo7za+Sf4zj8Xg+Wy83BUtRCE=
			</data>
			<key>hash2</key>
			<data>
			/aivBeJLeVgjejn+J7Ve8A1PgbyFwWSEIOEpNrMZwLw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnection.h</key>
		<dict>
			<key>hash</key>
			<data>
			NUnPpwpwUkVhUBU67o81VFaERfo=
			</data>
			<key>hash2</key>
			<data>
			1ICPsAdQVISoM+fhXtKdpyBkCX9WIi5bqIEJz2IfkHA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnectionDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			FSHiVcHDpJTlfdfBczQNHtjDJ8s=
			</data>
			<key>hash2</key>
			<data>
			pbeIgtjILQ+9lWJviGFFqpO8IqXWnVf2DQ094YKDKMY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnectionFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			gAfT3DO/vuTTlEaJPEP8hL5P3Eo=
			</data>
			<key>hash2</key>
			<data>
			xgTvIuiH3O0GP4dAfx+lVweVvj/3VUFzk/ZwDkEd6UM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestConnectionFactoryProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			A26a5H79Zb1dRO6YHMFB4DbS+D8=
			</data>
			<key>hash2</key>
			<data>
			ZSBkNkUs6k4hh3lOO6aa5+j5kuh9vwSm6BTbH++KEH4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestDataAttachment.h</key>
		<dict>
			<key>hash</key>
			<data>
			7vvCqPiZp4o6JKVaJJ4FP9XkXKE=
			</data>
			<key>hash2</key>
			<data>
			rrQm0dv7u0VNuBOOr4bOsLq22U2VRKN5+/8z8dvg8ac=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			lAwX1CKv5VHiJ07/xZZylICOdg4=
			</data>
			<key>hash2</key>
			<data>
			VHji6+eQJ/noGhXoav1+rDhYGkeNSPac5f3JMIMO4OQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestFactoryProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			ydipmJZDsiXmT3/5DNEiX0gzsNs=
			</data>
			<key>hash2</key>
			<data>
			LGCTe0D34qYvAzYLEDR50t2F4wbgmylanIR+2HAcV/E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestFlags.h</key>
		<dict>
			<key>hash</key>
			<data>
			Zas2ccUoNaCrjUffAdLC6TmKLWs=
			</data>
			<key>hash2</key>
			<data>
			QaBxTTw493IFQLv0fwkhBkWu57wAPkAZ/fexBSmcWuA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestHTTPMethod.h</key>
		<dict>
			<key>hash</key>
			<data>
			sF4WT7ko2ZXuQ91thBewwSb29Cc=
			</data>
			<key>hash2</key>
			<data>
			s/ZdV1PYtfb+e5MToTE5eWQ/g8Ea8Lfbn1y5cPXIois=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestMetadata.h</key>
		<dict>
			<key>hash</key>
			<data>
			4ZYoDzErnijlAw/YdD1e2qQ3eDs=
			</data>
			<key>hash2</key>
			<data>
			Twh3qoAgt3ReSFrZuMn8tfYVVZ+eHo/Z01B//qRL/aM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestPiggybackManaging.h</key>
		<dict>
			<key>hash</key>
			<data>
			RzvPVhBs2lDl2xH7PANCNXkswIE=
			</data>
			<key>hash2</key>
			<data>
			wxiIa9cDFOlxQYXM+TkWOQooJpOkR5UkCGuNOfRHzCo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			2WftYLHKj8D0QUQgx2F/+tCFX3k=
			</data>
			<key>hash2</key>
			<data>
			aKkuXQ5CLQQh3MSzr4CWjSGHECgsjZlvDwFTFbRrrFI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKGraphRequestQueue.h</key>
		<dict>
			<key>hash</key>
			<data>
			YazdAJ0rcrHmvRHvxaduooEWglg=
			</data>
			<key>hash2</key>
			<data>
			jg27Rw8BPOqfHG/lqSFvmFumU0quxxvP7wdja0rMIq8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKIAPDedupeProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			jFfQ2uToisxlUwu5HAehBwtT5yc=
			</data>
			<key>hash2</key>
			<data>
			mzR9LdGDTqtc3DYV+JOsBkVoOuodrkRWOO3EnpWU9C0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKIAPFailedTransactionLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			3naly4poGndtl04IKJLRhIn0y58=
			</data>
			<key>hash2</key>
			<data>
			YxYLbyBk6Uw+vYZwpHHGp5GKRCS9QE/hRs6s5QPT0uw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKIAPFailedTransactionLoggingCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			8f1KPldp/xKn7SdJ+extn1OcPWY=
			</data>
			<key>hash2</key>
			<data>
			ZzaGG8LO/3EvuN9LDMk/455U5A74GiZMmXHsNKy4Zlk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKIAPTransactionCaching.h</key>
		<dict>
			<key>hash</key>
			<data>
			EUOfrTb9ijRfv++ZOI1VjUF46+o=
			</data>
			<key>hash2</key>
			<data>
			bjiGwxnSAUlt+NVqp+emo6xGvyxyQzPz5qaW1530I/E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKImpressionLoggerFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			nfa97DKHYMz7v0+pUDy3nlxrUYk=
			</data>
			<key>hash2</key>
			<data>
			QuBRqz4neAxQgnQCCfZVT7P/volyw8iY5KgrMKRJmJs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKImpressionLoggerFactoryProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			3LEI96v+5GIEFysj3D1C/rG8ihw=
			</data>
			<key>hash2</key>
			<data>
			Us0Ez3sk3OsuatGsASCqoKebAXNWPg9S5pMOJ5DxDc4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKImpressionLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			wrrEayG2/hN5wUceRYRAU8HrXBk=
			</data>
			<key>hash2</key>
			<data>
			Vprv+yj1TQTAVjk0AdI+90h3w3DcuHwxVoAECMAadvM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKImpressionLoggingButton.h</key>
		<dict>
			<key>hash</key>
			<data>
			5jJQoQEgcqekG5Zl8U42hb9oI80=
			</data>
			<key>hash2</key>
			<data>
			ndY9up8dSnK60T7Qxxo1R50wARczYvrrA0RXdqjCzKI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKInstrumentManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			b4LuHg6PamZ7IBBxVoOYpxuYERM=
			</data>
			<key>hash2</key>
			<data>
			+5qq+ul/kPhxOgEhuLz8XmNc60qQeWJkTw9obciSaz4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKIntegrityParametersProcessorProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			YAejrtcnEaVtGKUEB4ZJpyeATvQ=
			</data>
			<key>hash2</key>
			<data>
			o5WVcgrcNZ9fy9iy8pkmLgt9cY41GNSvTFg3Z4b9k5A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKIntegrityProcessing.h</key>
		<dict>
			<key>hash</key>
			<data>
			87ZiOpw4U2tli0OR6o2FOZV0Szc=
			</data>
			<key>hash2</key>
			<data>
			ZIhsJR2yehcaQancBDHC1s72R+hCE/frIqi5p8edPn8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKInternalURLOpener.h</key>
		<dict>
			<key>hash</key>
			<data>
			FLV0q7bfHv0++Xz2yX93rch6WQk=
			</data>
			<key>hash2</key>
			<data>
			d7pu09v171CZOLDOnWFtkt5TfA+u9wD6UYyoWxkSA+w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKInternalUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			xVnxfL7eTB7Crbqhijh4imgTplo=
			</data>
			<key>hash2</key>
			<data>
			fGPA4wFG0PbwsHF1HMG8uXRBNUpRfuVBclcu0v5TDCk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKInternalUtilityProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			/ZcaAKHHVRGsTO5fkpWQKXtwRlg=
			</data>
			<key>hash2</key>
			<data>
			QxK2NWyHrM+GqD1pK5e3LD2xbiZX/p/UzObquLThWI0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKKeychainStore.h</key>
		<dict>
			<key>hash</key>
			<data>
			VSfBFlzguwgAJhVey77PM3TiKzI=
			</data>
			<key>hash2</key>
			<data>
			sQGCel/07cMPMZI0kl8wscwPV9WL/JIYf1ns0PSf8v0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKKeychainStoreProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			Vl4nIrUwT7cqcjwlXymbPkKUVDo=
			</data>
			<key>hash2</key>
			<data>
			XNfcYyJYq69j5eL0ARtTlzkh8SdVCq+G/s9eY4Pd8O8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKKeychainStoreProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			u2GnomfI+oHCeoe6Ei9pG5quwVE=
			</data>
			<key>hash2</key>
			<data>
			/INTsqlL5Gr82q1fsrI5Mm6ox4RJoy9zwOvstKFPXrI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLocation.h</key>
		<dict>
			<key>hash</key>
			<data>
			lVldFN//gmPckkWOntm6/lMe0QE=
			</data>
			<key>hash2</key>
			<data>
			4VM07vWgUKPPsLEMLF29hXYKIHBkc9vETSX506Z++Uw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			19xKJkbTRpwMoQwzaC6Nu+w5tME=
			</data>
			<key>hash2</key>
			<data>
			O7DI9fO361qB0Y+XI/ka6eMk5/LMGvTE5T0NPkcTBBI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLoggerFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			oN6VhKse4/3rPEKNFGojETNkg58=
			</data>
			<key>hash2</key>
			<data>
			e8PHQtEWsLuVh+XXwDbUAFSXUORwmsnkmOyyYz+ph8g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			/DbryGZcqEQACAktvCjPjV6SDG4=
			</data>
			<key>hash2</key>
			<data>
			IvKTyTv5bHSAJcZqLwaHR/lW5CFnjIggFOzHMRDWMk4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLoggingBehavior.h</key>
		<dict>
			<key>hash</key>
			<data>
			lNVc//dYNZlhHrZHLByspMT+TeY=
			</data>
			<key>hash2</key>
			<data>
			f5RLVvxNd/VtlSpoMhH6nO6jFRiC9rudIo+algkCGBs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLoggingNotifying.h</key>
		<dict>
			<key>hash</key>
			<data>
			ClxijYzHweoVytnlGNbT1n62Q2I=
			</data>
			<key>hash2</key>
			<data>
			/H/LDFDn+fA0n3pb11qgMdP8PeuXl02axVuT5C9K6nc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKLoginTooltip.h</key>
		<dict>
			<key>hash</key>
			<data>
			t9qlwGoUeyWhxDfXE8Ky6RnF/gg=
			</data>
			<key>hash2</key>
			<data>
			C6wHDAq5ukwucR1FkTnnq3ucsw6y7GR9wDadgB3zHZY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMacCatalystDetermining.h</key>
		<dict>
			<key>hash</key>
			<data>
			Fy2901RMaq8v1KZ1nkSvVuhYSN8=
			</data>
			<key>hash2</key>
			<data>
			dOVbOuPHBVVE2h/fz8iXrr824nuLBVRrqymrNwdF5kY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMath.h</key>
		<dict>
			<key>hash</key>
			<data>
			rYMaHGq5qMdO0PMhIT/NeaWQMFE=
			</data>
			<key>hash2</key>
			<data>
			RQb23pN1EiaRezovWmrJpZ289Eby0ndYmkHCpsWKbXo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMeasurementEventListener.h</key>
		<dict>
			<key>hash</key>
			<data>
			OVZSC/JIKFj+KgzAwt5biOqF7rI=
			</data>
			<key>hash2</key>
			<data>
			ELmJcxc3dSPYAYyrwqHJpwUxM4is2hfU27i90WTOYQ4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMeasurementEventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			9XUuzXpHXt6PyqsDNvqKaCYOsaQ=
			</data>
			<key>hash2</key>
			<data>
			Xy6hXD6E/laXHrWOeDhRU5cQKCbE05HcdVjR5tdUx6E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMetadataIndexer.h</key>
		<dict>
			<key>hash</key>
			<data>
			yvEMxozxtAwiiPOoouj17MOx38w=
			</data>
			<key>hash2</key>
			<data>
			FC2xPfBPJ+KkA1lGRnOxuFDbWIJc5fChKOpjrK4QYMk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMetadataIndexing.h</key>
		<dict>
			<key>hash</key>
			<data>
			0XbHu0BpTUjfkrPvYn1B9+H0Bww=
			</data>
			<key>hash2</key>
			<data>
			QjaWgUOa4ftPTfJONsDDsoLADLIhk0afRL0wWiGd9qc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKModelManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			UTvH2kBUyG0vy9bmR5tucIdzDMU=
			</data>
			<key>hash2</key>
			<data>
			4HNzWyenJC3JFcA8hUl1k235rwmiyBYrmtRHx6i0w2g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKMutableCopying.h</key>
		<dict>
			<key>hash</key>
			<data>
			CdAKmAi79FHfugMUCBcou38XjyY=
			</data>
			<key>hash2</key>
			<data>
			9WETC6Qraw3B3QY90JfYu/elsAnM/L40JTEsRAOO+hQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKNetworkErrorChecker.h</key>
		<dict>
			<key>hash</key>
			<data>
			lc4ltIsnGN0wefVKZeW3BTQqt8o=
			</data>
			<key>hash2</key>
			<data>
			mQPfqbSnxLTJW64KKhoGcZPQYNTYQABLzG1AZ2hcTSs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKNetworkErrorChecking.h</key>
		<dict>
			<key>hash</key>
			<data>
			DQOOpk+tae6sTARv6zgYkUNQv+4=
			</data>
			<key>hash2</key>
			<data>
			JaxujLpfeoL0uJ15AXk/+TxmnQ+XgmtHYz9sb5k3r1w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKObjectDecoding.h</key>
		<dict>
			<key>hash</key>
			<data>
			rsoqTN959edBc1dWsOJAk4SFajc=
			</data>
			<key>hash2</key>
			<data>
			qsowPp05Itw5uODC4pGjeSY1WE9ntYKWLfZW7A0nNw4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKOperatingSystemVersionComparing.h</key>
		<dict>
			<key>hash</key>
			<data>
			jd/kWOWeuMfcK4hF/tgU6AeS2yU=
			</data>
			<key>hash2</key>
			<data>
			KaQv/BSjIbO1IWul0m3Ckagnf4cBrNKnGm3fhM4GJcg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKPasteboard.h</key>
		<dict>
			<key>hash</key>
			<data>
			4+oJKTiDHC9gezS8CCuhdWHO2gs=
			</data>
			<key>hash2</key>
			<data>
			UJnUSOEkUYzGWK3fOCD3HGGdwQLxYhZg4znp+HpSNGQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKPaymentObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			2F/w1aia/WcWnKwAGffiRgYimWQ=
			</data>
			<key>hash2</key>
			<data>
			g21RW5m5mkv5jnc/g4IZvHnCPQLSjxGoaSvN3vo4UZw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKPaymentProductRequestor.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZQ6Btyy0z7ZiD/M9eX+7+KDRiLE=
			</data>
			<key>hash2</key>
			<data>
			S2CSCz82/VNHbyry4LOyJvanQu9UC+br3C0NUFHHqe8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKPaymentProductRequestorCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			kofChLuS6UMw2biE8Lcu2yhsmt0=
			</data>
			<key>hash2</key>
			<data>
			fGzWAJQkZyRBklHMViipfo4KyA1fMAOzlOiH4HhweQk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKProductAvailability.h</key>
		<dict>
			<key>hash</key>
			<data>
			4z6lAOLiyG+H6sMmuDzBXlrBO4Q=
			</data>
			<key>hash2</key>
			<data>
			AfSg3sbP+VegxUAApbWi9NSI+/dlu9LbDGiLvCWo3Z0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKProductCondition.h</key>
		<dict>
			<key>hash</key>
			<data>
			p2M86R+0XjuIIHBALGh4qHhF0sg=
			</data>
			<key>hash2</key>
			<data>
			dNGTpMMgyZMruD+nBPSsD0Y3Bc2L8ZoTcsW1f5tdK7Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKProductRequestFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			7BIaz0Ab6i62/Dq0UaZpdoNPhCs=
			</data>
			<key>hash2</key>
			<data>
			6bp/2xb4hfUOim9SEuGgPr9z+Bysu/FK27psJ1WxipQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKProductsRequestProtocols.h</key>
		<dict>
			<key>hash</key>
			<data>
			quFgypTeZ+2XNvJr6deEaYceClo=
			</data>
			<key>hash2</key>
			<data>
			a7mWH2GsxBZ9X9N8Z/19nO+toxG/0r99YGYxK28kY0o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKProfileBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			m3b2HY43bVO+k7tLfgTUHlel3XM=
			</data>
			<key>hash2</key>
			<data>
			4/f1aRcfzmU+yr0ypnHjdfSBpIQ8QxluAy3Yn0XxJPE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKProfileNotifications.h</key>
		<dict>
			<key>hash</key>
			<data>
			p/v7YsyAewf4Zx5PgVOw0g9689k=
			</data>
			<key>hash2</key>
			<data>
			yWNI2yhxUUa25VEKtZBJViFYRTXMmJxx1JUrJbK6QAs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKRandom.h</key>
		<dict>
			<key>hash</key>
			<data>
			rqrUnE5WdNwaBw/Fm/Sauc2KceA=
			</data>
			<key>hash2</key>
			<data>
			/FYnxRG5dQwkxSXWGGkucUnk7tABHKmrqud2upLxMSk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKRestrictiveDataFilterManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			FhBIBCgefnNx6Hx9hBvjEQxlgTk=
			</data>
			<key>hash2</key>
			<data>
			mSbOfI4ZD+1zdJOwvNbPEvSXVCr/iH1TcmA+bmwJW5k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKRulesFromKeyProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			9/nqsZZ8Epe4NBnTPqEOptCNdzs=
			</data>
			<key>hash2</key>
			<data>
			KU18OwgqjgGW8bB4DSVWVWXRm2R5Uh9WKEHg/7D71/c=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSKAdNetworkReporter.h</key>
		<dict>
			<key>hash</key>
			<data>
			N8+fSt031r3BemNKfykFVGaMU2g=
			</data>
			<key>hash2</key>
			<data>
			uqe1+q806a6PrWGJ1z275ZtkYMC6hW+JEjEM1j+UxFY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSKAdNetworkReporterV2.h</key>
		<dict>
			<key>hash</key>
			<data>
			t1JQiDuyttg76dcS901/oxfu6tQ=
			</data>
			<key>hash2</key>
			<data>
			hyqPE+oRV6Q/uWySMPT2HXaeoU0Fhbr/rzJPAblC8zQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKServerConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			ePai1Ct6rcU1fbdNFDdAFZgZLYo=
			</data>
			<key>hash2</key>
			<data>
			svD59mf/yaDmtJnt9mqyUETp6BqGqszyESR3t8RUtz0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKServerConfigurationManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			MabAusZdKvvZ5Ny2wrhRTzvzRhY=
			</data>
			<key>hash2</key>
			<data>
			7bxzdmwW3VP1D775pYiEPDzkARci0KU5+G83fUIv+Lk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKServerConfigurationProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			OVcy7K8/6LpbY7Ls9wBkMeiLOXc=
			</data>
			<key>hash2</key>
			<data>
			nggJ6UDcdQ8WAvG9K2eWGIr1+MNgK3/g6Tj4sImbWHM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSettingsLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			j4NKiO1um7BzI27sPShA+WNNV6E=
			</data>
			<key>hash2</key>
			<data>
			GgfZ+r7AkNT2pA6iA72tqgExzEgxS0MRiVNUSlpsa48=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSourceApplicationTracking.h</key>
		<dict>
			<key>hash</key>
			<data>
			eyc/NF7kaz05PnojBKao9RoOkXo=
			</data>
			<key>hash2</key>
			<data>
			qajy5wvDILF4b7Jcx9hTizBUhaT1i2GXvpxik1TzkNE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSuggestedEventsIndexer.h</key>
		<dict>
			<key>hash</key>
			<data>
			eLTHBeEjKRaaEKDEDTrfz5o1+pE=
			</data>
			<key>hash2</key>
			<data>
			o+ccIBHzx5V9J1IZLNYIg+BkWS3Kl3rMRYXWeWRdhDQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSuggestedEventsIndexerProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			Nlpu1MobSLVgHiTs61VhpCb8F84=
			</data>
			<key>hash2</key>
			<data>
			9kKE4jZryp1a9dSaPrjDlA+9RhscC9+Yq3GlCBL0DpU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSwizzler.h</key>
		<dict>
			<key>hash</key>
			<data>
			PgutzEuZyjT9se/U24OTeoEMo1g=
			</data>
			<key>hash2</key>
			<data>
			0n+HQ6UGePndL7zICwSJXV5WRg12HV3lB4DiARhtWIU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKSwizzling.h</key>
		<dict>
			<key>hash</key>
			<data>
			jMRYakKevWrqsOerYuQ9Fb1eCT4=
			</data>
			<key>hash2</key>
			<data>
			KFyWd42cMnaIyExVvONG1g3JPBSvnv/4xdag+5V9zDQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKTimeSpentData.h</key>
		<dict>
			<key>hash</key>
			<data>
			Zgill5e+tNHLsGRpZ8nXHCk2iEc=
			</data>
			<key>hash2</key>
			<data>
			4onV/tAXCdp3PH0pHlu7hEtJ7bg+cH1JUhZKPPfhxms=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKTimeSpentRecording.h</key>
		<dict>
			<key>hash</key>
			<data>
			NuI7oI0R+b4/s+KiUbS+MEn4x8k=
			</data>
			<key>hash2</key>
			<data>
			Xi9idQwWvd5DFqQNYAbll1aNfRM0zlSsrHt8LqUVh0E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKTokenCaching.h</key>
		<dict>
			<key>hash</key>
			<data>
			cla0KYAtpSCy2GcXC++XdSQrxck=
			</data>
			<key>hash2</key>
			<data>
			aqvT3pRj7C448KPxobWR9AZGsr5TeBuYFw6Fe2phX4o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKTokenStringProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			43wQbjzLSB+cSu9hTQZ4tJq24Mc=
			</data>
			<key>hash2</key>
			<data>
			TTwDb4bRpxFALwHRV7rPZ9zwNFPGZhy34If3avetGjM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKTransactionObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			lZIW9x0Pm8LlHaUourw8qilnS7I=
			</data>
			<key>hash2</key>
			<data>
			NOPfkdHDnW6uD+jIMqNB/e5UkbR4E7d1qzrjahnOHdo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKTransformer.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ui2GFPACS7T6kK9LcCLcdJyCYyo=
			</data>
			<key>hash2</key>
			<data>
			76ADDGvmmKAAjwJQmqZOKN1QRhrUmGE9qELPwGG2F4k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURL.h</key>
		<dict>
			<key>hash</key>
			<data>
			lg+hfhk8xZ7goFdbGZ8cVtCyNbw=
			</data>
			<key>hash2</key>
			<data>
			MC/UJOZYmjruWzpiMns2o9iOpycM+cvnUnx2d+brcDA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLHosting.h</key>
		<dict>
			<key>hash</key>
			<data>
			O+RAgM4Uu49yrzKRLeqtv9FfVKE=
			</data>
			<key>hash2</key>
			<data>
			tY5HluqD9iw44nsX/G1SrqCii9dfnxqiv8T/kapFGQs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLOpener.h</key>
		<dict>
			<key>hash</key>
			<data>
			/9/EmnpZWM6KdVyGGEI1NUvMbYk=
			</data>
			<key>hash2</key>
			<data>
			DUeUDJEfTX1NM5s8QxHytKu4/avgUtMZIXnRyx+sTF4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLOpening.h</key>
		<dict>
			<key>hash</key>
			<data>
			0r8abqnzwBXvpV5PlCa/kQ+OdW8=
			</data>
			<key>hash2</key>
			<data>
			djQLdPxXd/h2GC826rf/QKm1UM7Fb9poUtZsWIB9K/M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLScheme.h</key>
		<dict>
			<key>hash</key>
			<data>
			36HfFNYLwWfRajDYFDJeNZe/evc=
			</data>
			<key>hash2</key>
			<data>
			BdpEnJgCwk4m/BX4jdUcFWtch+dOASiPi4b4XuShRow=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLSessionProxyFactory.h</key>
		<dict>
			<key>hash</key>
			<data>
			fkszrJOzG1OgfuDotJLKT74xw1w=
			</data>
			<key>hash2</key>
			<data>
			nFIFaAzvqWS22hEvcMkix5YaPcK4GBmjn+7lCb40/RI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLSessionProxyProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			5Hely+G3LbaFH81TEA3aS8tH8Xk=
			</data>
			<key>hash2</key>
			<data>
			aTA0C16KsECkZ2E8HCXO7ce5CfBhS4krYqHjd2sMT84=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKURLSessionProxying.h</key>
		<dict>
			<key>hash</key>
			<data>
			LIrnTeexPfrk+a2pllxOcLBqw+Q=
			</data>
			<key>hash2</key>
			<data>
			xCWdJ07ZEEhMehbPyCICo2qsde4nyWuid/4r5sZNYM0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUnarchiverProvider.h</key>
		<dict>
			<key>hash</key>
			<data>
			mBxizULPLClFmFkzGvq7DZbedIU=
			</data>
			<key>hash2</key>
			<data>
			km9PeIz2mArhmhaSjTUxnyXNDnyTw45ONMOIrFvX9Ng=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUnarchiverProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			PU6uhik4cqPkhqWAmJpctYAS+vI=
			</data>
			<key>hash2</key>
			<data>
			oCPV4SoPYuNAh0Y+MkV7TYC6T/3tFQbq2/dC8tplruQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUserAgeRange.h</key>
		<dict>
			<key>hash</key>
			<data>
			paq4PxztwLvJ2EQKujqXLJfgjiA=
			</data>
			<key>hash2</key>
			<data>
			pLTGrXlcRTNXtDJu1omtFNVR7Tl9niHZllskPvHvlLI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUserDataPersisting.h</key>
		<dict>
			<key>hash</key>
			<data>
			eiIDv79NpGtVgJHZ+jovQsS4ZsU=
			</data>
			<key>hash2</key>
			<data>
			e5bOiJw1uxd3xCQKKVryAoxKqTxaIVcOMbL0cYWLyJo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUserDataStore.h</key>
		<dict>
			<key>hash</key>
			<data>
			iSw84njE3Q4fckPhhhUCpEMT4Uw=
			</data>
			<key>hash2</key>
			<data>
			RY2Lg4PYOft0K2wbJtT1axcgmL2lJ5PaGtXZga3hQjI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUserIDProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			1G3kOunQeQMavUujt30qZv1Oj98=
			</data>
			<key>hash2</key>
			<data>
			n8EfpaG2V2BCI5xE/8Hz8+83cLBSVMqXjkEx1Gv6r6E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUserIdentifier.h</key>
		<dict>
			<key>hash</key>
			<data>
			EqrAGtLbcJRfQwl12songIdMMEE=
			</data>
			<key>hash2</key>
			<data>
			TIFVVKaBj4BvRv/gFJvtxkh3Bm/40McFA0531BAu5AI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			ACK+e48w6WLwZDhZT9VIaXDWTlk=
			</data>
			<key>hash2</key>
			<data>
			aDJa31ufENPB6jQuSRYbBb60HcsTocEvvcqZw2iWBN8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKWebDialogDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			oPLwzJ7KUTr8T5hq/c983EM1rfE=
			</data>
			<key>hash2</key>
			<data>
			RhnzKhKPGNsn+I0efiSBdbYdU7h+Z/m5R4mQUqXW8xA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKWebDialogView.h</key>
		<dict>
			<key>hash</key>
			<data>
			EHOQKin9zYzG+gmc/LfBegKrvEE=
			</data>
			<key>hash2</key>
			<data>
			HC8WLkRk5PhVWGbE58hLSP/goSUOleGYhuUzBNm31v4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKWebView.h</key>
		<dict>
			<key>hash</key>
			<data>
			jUhFO4/5Ly1VNml/cAryOayE4Ag=
			</data>
			<key>hash2</key>
			<data>
			yZ2ZBoGlvdRcTOHXhCl80pgVGKFXIQXl9HClExQmfbc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKWebViewAppLinkResolver.h</key>
		<dict>
			<key>hash</key>
			<data>
			jEC9UH9Inm7DYqoFZv2qaN3Pe14=
			</data>
			<key>hash2</key>
			<data>
			+Ac5AbGpEiHL5SuKIFm5ORqswif+O0w+zlIxL1qgUd4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/FBSDKWebViewProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			6dBqgFJpP3qYvCEw7KHe/d5ieC8=
			</data>
			<key>hash2</key>
			<data>
			2TVzs8L8DTwpM3xFUOklhzjkivHzm2VLIiY+IXFxMnk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/NSNotificationCenter+NotificationPosting.h</key>
		<dict>
			<key>hash</key>
			<data>
			4IqhFgtQjABnwxNo0vP4+5Q/hSU=
			</data>
			<key>hash2</key>
			<data>
			/hTFkvjgGwyFzsQ6VB5CKDoMAKTrun+jQ7LMU4Bcfws=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/NSProcessInfo+MacCatalystDetermining.h</key>
		<dict>
			<key>hash</key>
			<data>
			P2PZ0k71jzSsWpQg6vH4TOHr6SU=
			</data>
			<key>hash2</key>
			<data>
			N5KvICMBUqwJ3JveHhLX4bomYQIHATUqScz3F4Vyf8Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/NSProcessInfo+OperatingSystemVersionComparing.h</key>
		<dict>
			<key>hash</key>
			<data>
			7p95y3GgCy4C7g+8xfGVXW4sr18=
			</data>
			<key>hash2</key>
			<data>
			G6fi3L4ee8mocOfWvq0r6J1Iy2HRUZ7PyHTmpxrxo08=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/UIApplication+URLOpener.h</key>
		<dict>
			<key>hash</key>
			<data>
			N6kxbbjqI4XB8O7cVqNSb4VtaOs=
			</data>
			<key>hash2</key>
			<data>
			LFldGsPohxFxp2EMtRNr9DW5offC4saf8wAKpZaih60=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/UIPasteboard+Pasteboard.h</key>
		<dict>
			<key>hash</key>
			<data>
			MKDA9GrF2o6+h3tbW9e9adSk23k=
			</data>
			<key>hash2</key>
			<data>
			JdRN7qh5XEBay0JfDd6bXvUO+ciilKne5qAXVmNWoyY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/WKWebView+WebViewProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			9AxT3+g6DPZx5q3rQWhRBMFJu64=
			</data>
			<key>hash2</key>
			<data>
			Uo6vnwYxxFh+V/+J5U7GTu7dFxg9xliEGHkxzgAJYTs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/_FBSDKNotificationPosting.h</key>
		<dict>
			<key>hash</key>
			<data>
			w1Gpzlc2q889xvFT3+ffPk2kwQU=
			</data>
			<key>hash2</key>
			<data>
			ZBUZDT5SUs3wBxxP06/A5KDaxedomK4NJ7axY0qD7jY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/_FBSDKWindowFinding.h</key>
		<dict>
			<key>hash</key>
			<data>
			Gac9mAAYHny41SRhpW53CbfSo2s=
			</data>
			<key>hash2</key>
			<data>
			QxPymhBROXgyvxD0bzeN+T5ennsD7zaelwvA1a2l3oE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Headers/__FBSDKLoggerCreating.h</key>
		<dict>
			<key>hash</key>
			<data>
			TTkFW5xuSEAOVq8O1cbEJ6/RgQg=
			</data>
			<key>hash2</key>
			<data>
			KjjsJxMgCNjZYBZHCA+t/dUcdoGwgXLLXtNZjYzHQrA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			QUelzWz7dqMd+QoK8TNxFAkreF8=
			</data>
			<key>hash2</key>
			<data>
			eKSONIqTiShuXTS2QlW/weQy7Gd3R7ZjXLkZJ0jsl0k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			OkoPZtcS5lu+Hk/2Cmm3NkUC1LA=
			</data>
			<key>hash2</key>
			<data>
			3vDnj3mq4rNEgbA2If/m00wW5JaGeu+mVlK7rYEf/2s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			VOcf6Ms+U2Dy9LSAiruhQJI4zX0=
			</data>
			<key>hash2</key>
			<data>
			PPFeUREBfk8vXpfZkFFbWQ2xm1YUESXW5eAGzS+fe8o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			sGE3AP/1KVSnPWfojA5wQ3t0qB0=
			</data>
			<key>hash2</key>
			<data>
			ZUrJpspFW0S6/mzSAWKsb62UEZ9KovyG9OUkwoQx4V8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			VOcf6Ms+U2Dy9LSAiruhQJI4zX0=
			</data>
			<key>hash2</key>
			<data>
			PPFeUREBfk8vXpfZkFFbWQ2xm1YUESXW5eAGzS+fe8o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			OkoPZtcS5lu+Hk/2Cmm3NkUC1LA=
			</data>
			<key>hash2</key>
			<data>
			3vDnj3mq4rNEgbA2If/m00wW5JaGeu+mVlK7rYEf/2s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			q9X/As+m8XdKb41fIQdGV52eO7Y=
			</data>
			<key>hash2</key>
			<data>
			baPGSbiReVVht2QP9q+EdhhzDIJm4diylxrtIZMeMrI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			NvjKwhBscIDrP7g81PSeDrj+svw=
			</data>
			<key>hash2</key>
			<data>
			n/t5fcXhn9YOQUl5nY5xFZSTcBj57/uzkeDh7wRbkwM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/FBSDKCoreKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			q9X/As+m8XdKb41fIQdGV52eO7Y=
			</data>
			<key>hash2</key>
			<data>
			baPGSbiReVVht2QP9q+EdhhzDIJm4diylxrtIZMeMrI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			130ouJhMpZj1Z376F5gsUfoyxyc=
			</data>
			<key>hash2</key>
			<data>
			l8ml0KsKmiLEQKTzAYpZRg2k4nhDb5o3vF3lArU4ZzY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			+4xzw+xvpntuK5AhSisGTSzsudg=
			</data>
			<key>hash2</key>
			<data>
			QgUFyksoQJOBn7vsX2WBw89uQ1s+OjAtb6XqI3vhQ74=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			GZ71Ja+PkhoMnlUakDMQn3MThQ0=
			</data>
			<key>hash2</key>
			<data>
			T1Nb8lwwUNZy+jOAojp0ebTZPRdrPExb6iSPZwc7oew=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			qG6Ty6t/qhX430RoQk8cuQ7fGXw=
			</data>
			<key>hash2</key>
			<data>
			rB6rIBwUCwLIwjj0MHS+vm+oRCTLb8bnr7hT9agltoY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/DWARF/FBSDKCoreKit</key>
		<dict>
			<key>hash</key>
			<data>
			wCSnuzPKIzQqOHyFxHGQOr2mbw8=
			</data>
			<key>hash2</key>
			<data>
			IAG1/aXEoYqwyNwt/E/PHtNl+lhTe12LBb0R1BROivw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKCoreKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			awyy358znRrie4VIrALKpm6yD+U=
			</data>
			<key>hash2</key>
			<data>
			fhznsFX5bUvcgOkpMJgFZbHSbB9voso7KrHNWgB3cJk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBSDKCoreKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			PiCM7UxuklwFMb2/bGt1cC3trl0=
			</data>
			<key>hash2</key>
			<data>
			udfuIL2vBfPWA3sgDLKj0u3UGKpV3Zv+WXiLKaUB4kA=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
