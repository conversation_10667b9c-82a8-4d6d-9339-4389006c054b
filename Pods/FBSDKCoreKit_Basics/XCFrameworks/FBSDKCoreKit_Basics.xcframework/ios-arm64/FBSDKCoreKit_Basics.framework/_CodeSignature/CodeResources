<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FBSDKBase64.h</key>
		<data>
		4WPA7ov2bAhRn5QP5eZDBYX+spk=
		</data>
		<key>Headers/FBSDKBasicUtility.h</key>
		<data>
		3Ij+0qMFvTmtUIuRbymzor6FDzg=
		</data>
		<key>Headers/FBSDKCoreKit_Basics.h</key>
		<data>
		98/O+nEXsNQ8AcNbx/iw8ai/ktE=
		</data>
		<key>Headers/FBSDKCrashHandler.h</key>
		<data>
		CZiEDVm8gHGkALwyjZtYF2K3LGk=
		</data>
		<key>Headers/FBSDKCrashHandlerProtocol.h</key>
		<data>
		vSB3Xn5mAIXVnisb8nAGwYKzAe0=
		</data>
		<key>Headers/FBSDKCrashObserving.h</key>
		<data>
		UhJc2ZYtB2hcyXxk00KbwKbTgcc=
		</data>
		<key>Headers/FBSDKDataPersisting.h</key>
		<data>
		DtnVkIyBlZEE5hHK6RM8kIawq7w=
		</data>
		<key>Headers/FBSDKFileDataExtracting.h</key>
		<data>
		BQDcfAulsMenmeoahsXxQgOwOH8=
		</data>
		<key>Headers/FBSDKFileManaging.h</key>
		<data>
		p/4j5P/SU+cTiWeHYIULKGdiaEg=
		</data>
		<key>Headers/FBSDKInfoDictionaryProviding.h</key>
		<data>
		okxIBK/wxL+ugvqad38fKg7+VyU=
		</data>
		<key>Headers/FBSDKLibAnalyzer.h</key>
		<data>
		Sr7GpMqIG4HmNNhoU6xSJrR36F8=
		</data>
		<key>Headers/FBSDKLinking.h</key>
		<data>
		1OBirQhOiNPiwhFBBuCxquzs+8o=
		</data>
		<key>Headers/FBSDKNetworkTask.h</key>
		<data>
		0sGmRF6Hdq6tvoAuo8957/Esjbc=
		</data>
		<key>Headers/FBSDKNotificationDelivering.h</key>
		<data>
		H1EtkXfryfCD6P8NlmWazMezAP0=
		</data>
		<key>Headers/FBSDKTypeUtility.h</key>
		<data>
		qgaA6wWpYIa+hM+VZSTB1P0w8Jk=
		</data>
		<key>Headers/FBSDKURLSession.h</key>
		<data>
		lNyxKy0KC2f37l17PcuP0Gv/EDA=
		</data>
		<key>Headers/FBSDKURLSessionProviding.h</key>
		<data>
		1wsWv3wp81s7ODMRhEscWZBDXkc=
		</data>
		<key>Headers/FBSDKURLSessionTask.h</key>
		<data>
		aOm3T7/JlTQP0GIRlnLQzT+NKc8=
		</data>
		<key>Headers/NSNotificationCenter+NotificationDelivering.h</key>
		<data>
		7k8yl29uNS0GjwEBkfViBHp6lN0=
		</data>
		<key>Info.plist</key>
		<data>
		CrnHEcJUaMM+o+Y/mgdmvmfp7Dg=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		dAKyD+E6uMaCMsZEO7c47okujEM=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		OyS/P0To2cmktqAfP4sDQxHrwXo=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FBSDKBase64.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ePF4YsPJC4jFg1uvNt6fb83WN1IDTijvBhGt+V0Rh/8=
			</data>
		</dict>
		<key>Headers/FBSDKBasicUtility.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4HOJGD9dL2BJIlaj/ej0J6MTNqBvYgF0/XrWiZtsT3E=
			</data>
		</dict>
		<key>Headers/FBSDKCoreKit_Basics.h</key>
		<dict>
			<key>hash2</key>
			<data>
			n96Ist8MxHyeU+TJA3OhwPRry3QcVtumXYCUmhcHycw=
			</data>
		</dict>
		<key>Headers/FBSDKCrashHandler.h</key>
		<dict>
			<key>hash2</key>
			<data>
			3ILHiPoEMzce96plPruxswh7D9L+ptDaLZH98MYQ5rY=
			</data>
		</dict>
		<key>Headers/FBSDKCrashHandlerProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			qZ25WCONiSxncOC5vN2J90QWqM9a21jnkfZjwxq0odA=
			</data>
		</dict>
		<key>Headers/FBSDKCrashObserving.h</key>
		<dict>
			<key>hash2</key>
			<data>
			tO6F4Bbijxuct9m9xB8pcqmAlnyPPJ0EqbN+Z8/EM1w=
			</data>
		</dict>
		<key>Headers/FBSDKDataPersisting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			VcT2tlzFWSs7Ld7eUXcIezAttUSWdAIip7lZdNoZiGc=
			</data>
		</dict>
		<key>Headers/FBSDKFileDataExtracting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			s/7qW6G7GCOOyCS4jkWz8szFjT6uujuGXGhTa5JF4G4=
			</data>
		</dict>
		<key>Headers/FBSDKFileManaging.h</key>
		<dict>
			<key>hash2</key>
			<data>
			6+3pxgKpUWQwvlj3RwBFPC0rr5+wK651qUwh0n4fPHs=
			</data>
		</dict>
		<key>Headers/FBSDKInfoDictionaryProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			udZpX0hlPOkgV99Ck52TrAmlNiyK4r2qtK+kyPsJl8M=
			</data>
		</dict>
		<key>Headers/FBSDKLibAnalyzer.h</key>
		<dict>
			<key>hash2</key>
			<data>
			vcTMWhsIb/z29oxTpsnEfTWCnlgLS7qlPQuTTREW6Ek=
			</data>
		</dict>
		<key>Headers/FBSDKLinking.h</key>
		<dict>
			<key>hash2</key>
			<data>
			5NmQW3WO6TuTqGtMxocXiOeGiV7p3hyX2Saia2UhhL4=
			</data>
		</dict>
		<key>Headers/FBSDKNetworkTask.h</key>
		<dict>
			<key>hash2</key>
			<data>
			WXUdURA38eU1UT+VzBqNd3NJZ/tMnqMgcYFfskguL9w=
			</data>
		</dict>
		<key>Headers/FBSDKNotificationDelivering.h</key>
		<dict>
			<key>hash2</key>
			<data>
			jCbQRKCjKWBaILGHzII7b5E1nAY37YAd0KWD5lhnyBM=
			</data>
		</dict>
		<key>Headers/FBSDKTypeUtility.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Xm2cMC3iy1kvQn8EXXkZ0eKLNRVCmSlSbefuvvTCTTs=
			</data>
		</dict>
		<key>Headers/FBSDKURLSession.h</key>
		<dict>
			<key>hash2</key>
			<data>
			5L3MvrHaYDNtK2zrxdEtZNCRnhTQHNM0bvoh0pAfKg0=
			</data>
		</dict>
		<key>Headers/FBSDKURLSessionProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			O0czQhC3wEX5AKGOHblqZYeevvp4+w3IVFYRSr4Uv9o=
			</data>
		</dict>
		<key>Headers/FBSDKURLSessionTask.h</key>
		<dict>
			<key>hash2</key>
			<data>
			3Efn6F2Y10Aul+I89unuMGdO1GJTM5xkZOglY+a5XtE=
			</data>
		</dict>
		<key>Headers/NSNotificationCenter+NotificationDelivering.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ODQiUcnulvLSI4AHqIqtQvUPeAp+/9qi2eIHXnVKxh8=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			Ax5EJgz2ae9iwvEaJiJhRzHL4ePWe4qZhIaEdKJk27I=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			WsyF5Q/iCf15mIOe/RlsYmYVYjlwU787E0oxD4TOhVs=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
